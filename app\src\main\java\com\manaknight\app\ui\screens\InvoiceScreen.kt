package com.manaknight.app.ui.screens



import Manaknight.R
import android.app.Dialog
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.ArrowForward
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.*
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.*
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.manaknight.app.model.remote.profitPro.AllLineItemsResponseModel
import com.manaknight.app.model.remote.profitPro.ClientDetailRespModel
import com.manaknight.app.model.remote.profitPro.JobDetailsRespModel
import com.manaknight.app.model.remote.profitPro.TotalRespModel
import com.manaknight.app.network.Status
import com.manaknight.app.viewmodels.BaasViewModel
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import com.manaknight.app.ui.utils.isTabletLayout
import com.manaknight.app.ui.utils.getMaxContentWidth

@Composable
fun InvoiceScreen(
    projectId: Int,
    baasViewModel: BaasViewModel,
    navController: NavController,
    dialog: Dialog
) {

    LaunchedEffect(projectId) {
        baasViewModel.setProjectId(projectId)
    }
    val projectDetailsResource by baasViewModel.projectDetailsResource.observeAsState()
    when (projectDetailsResource?.status) {
        Status.LOADING -> dialog.show()
        Status.ERROR -> {
            Text(text = "Error: ${projectDetailsResource?.message}", color = Color.Red)
            dialog.hide()
            }
            Status.SUCCESS -> {
                dialog.hide()
            projectDetailsResource?.data?.let { apiResponse ->
                InvoiceContent(apiResponse, navController)
            } ?: Text("No data available")
        }
        else -> Text(text = "No Data Available")
    }
}

@Composable
fun InvoiceContent(apiResponse: AllLineItemsResponseModel, navController: NavController) {
    val dateToDisplay = apiResponse.create_at ?: LocalDate.now().format(DateTimeFormatter.ofPattern("MM/dd/yyyy"))
    val isTablet = isTabletLayout()
    val maxContentWidth = getMaxContentWidth()

    Surface(color = Color.White) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.White)
        ) {
            Column(
                modifier = Modifier
                    .then(
                        if (isTablet && maxContentWidth != Dp.Unspecified)
                            Modifier.align(Alignment.TopCenter).widthIn(max = maxContentWidth)
                        else Modifier.fillMaxWidth()
                    )
                    .padding(16.dp)
                    .background(Color.White)
            ) {
                HeaderCard(name = apiResponse.client_details?.name, navController)
                val projectStatus = apiResponse.project.status
                InvoiceCard(
                    invoiceNumber = "${apiResponse.job_details.firstOrNull()?.line_id ?: "N/A"}",
                    create_at = dateToDisplay,
                    projectStatus
                )
                Spacer(modifier = Modifier.height(4.dp))
                Divider(color = Color.Gray.copy(alpha = 0.3f))
                Spacer(modifier = Modifier.height(12.dp))
                ClientDetails(clientDetails = apiResponse.client_details)
                Spacer(modifier = Modifier.height(12.dp))
                InvoiceDescription(
                    description = apiResponse.job_details.firstOrNull()?.description
                        ?: "No description available"
                )
                Spacer(modifier = Modifier.height(12.dp))
                TotalAmount(totalAmount = apiResponse.totals.sale_price?.toDouble() ?: 0.0)
                Spacer(modifier = Modifier.height(12.dp))
                DueText() // Assuming "Due upon receipt" is static for now
            }
        }
    }
}

@Composable
fun HeaderCard(name: String?,navController: NavController) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        IconButton(onClick = {
            navController.popBackStack()
            navController.popBackStack()
        }) {
            Icon(Icons.Default.ArrowBack, contentDescription = "Back")
        }
        Text(text = name ?: "Company Name", fontWeight = FontWeight.Bold, fontSize = 16.sp)
        IconButton(onClick = { /* Handle more options */ }) {

            Icon(
                painter = painterResource(id = R.drawable.ic_options_bold),
                contentDescription = "More",
                tint = colorResource(com.saksham.customloadingdialog.R.color.transparent),
                modifier = Modifier.size(16.dp)
            )
        }
    }
}
@Composable
fun InvoiceCard(invoiceNumber: String, create_at: String, status: Int) {
    Row(modifier = Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
        val message = if (status == 1 || status == 4) "Invoice " else "Estimate"


        Text(text = "$message #$invoiceNumber", fontSize = 14.sp, fontWeight = FontWeight.Medium, color = colorResource(R.color.profit_black))
        Text(text = "${ create_at}"?:"\"mm/dd/yyyy\"", fontSize = 14.sp, fontWeight = FontWeight.Medium, color = colorResource(R.color.profit_black))
    }

}

@Composable
fun ClientDetails(clientDetails: ClientDetailRespModel) {
    Text(text = "Client", fontSize = 16.sp, fontWeight = FontWeight.Medium, color = colorResource(R.color.profit_black))
    Spacer(modifier = Modifier.height(4.dp))
    Card(
        modifier = Modifier.fillMaxWidth().border(1.dp, color = colorResource(R.color.stroke_light)),
        shape = RoundedCornerShape(8.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 1.dp),

        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Column(modifier = Modifier.padding(16.dp), verticalArrangement = Arrangement.spacedBy(8.dp)) {

            Text(text = clientDetails.name ?: "N/A", fontSize = 14.sp, fontWeight = FontWeight.Medium, color = colorResource(R.color.profit_black))
            Text(text = clientDetails.email ?: "N/A", fontSize = 14.sp, color = colorResource(R.color.profit_black))
            Text(text = clientDetails.address ?: "N/A", fontSize = 14.sp, color = colorResource(R.color.profit_black))
        }
    }
}

@Composable
fun InvoiceDescription(description: String) {
    Text(text = "Milestone description", fontSize = 16.sp, fontWeight = FontWeight.Medium, color = colorResource(R.color.profit_black))
    Spacer(modifier = Modifier.height(4.dp))
    Card(
        modifier = Modifier.fillMaxWidth().border(1.dp, color = colorResource(R.color.stroke_light)),
        shape = RoundedCornerShape(8.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 1.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Column(modifier = Modifier.padding(16.dp)) {
            Text(
                text = description,
                fontSize = 14.sp,
                color = colorResource(R.color.text_sub)
            )
        }
    }
}

@Composable
fun TotalAmount(totalAmount: Double) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(8.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 1.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Row(modifier = Modifier.padding(16.dp).fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween, ) {
            Text(text = "Total amount due", fontSize = 16.sp, fontWeight = FontWeight.Medium)
            Text(text = "$${String.format("%.2f", totalAmount?.toDouble())}", fontSize = 16.sp, fontWeight = FontWeight.Medium)
        }
    }
}

@Composable
fun DueText() {
    Text(
        text = "Due upon receipt",
        fontSize = 14.sp,
        color = colorResource(R.color.text_sub)
    )
}

// Preview function for your composable
@Preview(showBackground = true)
@Composable
fun PreviewInvoiceScreen() {
    // Provide dummy data for the preview
    val dummyApiResponse = AllLineItemsResponseModel(
        error = false,
        job_details = arrayListOf(
            JobDetailsRespModel(
                line_id = 123,
                labour_hours = 10,
                labour_budget = 500,
                material_budget = 1000,
                profit_overhead_amount = 200.0,
                sale_price = 1700.0,
                estimated_by = "John Doe",
                description = "Sample invoice description.",
                materials = arrayListOf(),
                update_at = "2025/12/08",
            )
        ),
        totals = TotalRespModel(
            material_budget = 1000,
            labour_budget = 500,
            total_profit_overhead = 200.0,
            sale_price = 1700.0
        ),
        client_details = ClientDetailRespModel(
            name = "Jane Smith",
            email = "<EMAIL>",
            address = "456 Oak Ave, Anytown, USA",
            customer_id = 4,
            user_id = 2
        ),
        draws = arrayListOf(),
        create_at = "23/04/2025",
        update_at = "23/04/2025",
        status = 2,
        project = TODO()
    )
    InvoiceContent(dummyApiResponse, rememberNavController())
}

@Composable
fun JobDetailsRow(itemNumber: Int, description: String, amount: String) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = "#$itemNumber",
            modifier = Modifier.weight(0.1f),
            textAlign = TextAlign.Start
        )
        Text(
            text = description,
            modifier = Modifier.weight(0.6f),
            textAlign = TextAlign.Start
        )
        Text(
            text = amount,
            modifier = Modifier.weight(0.2f),
            textAlign = TextAlign.End
        )
        Icon(
            imageVector = Icons.Filled.ArrowForward,
            contentDescription = "View Details",
            modifier = Modifier.weight(0.1f)
        )
    }
}

@Composable
fun JobDetailsList() {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {
        Text(
            text = "Job Details",
            // You can add styling here, e.g., style = MaterialTheme.typography.h6
            modifier = Modifier.padding(bottom = 8.dp).border(0.5.dp, color = colorResource(R.color.stroke_light))
        )
        JobDetailsRow(itemNumber = 1, description = "{item.line.description}", amount = "$100")
        JobDetailsRow(itemNumber = 2, description = "{item.line.description}", amount = "$100")
        JobDetailsRow(itemNumber = 3, description = "{item.line.description}", amount = "$100")
        // You can add more JobDetailsRow composables here to display more items
    }
}

// Preview function (optional)
@Preview(showBackground = true)
@Composable
fun PreviewJobDetailsList() {
    JobDetailsList()
}



//// Helper function for rememberNavController (if not already imported)
//@Composable
//fun rememberNavController(): NavController {
//    return remember { androidx.navigation.compose.rememberNavController() }
//}