package com.manaknight.app.viewmodels

import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.map
import androidx.lifecycle.viewModelScope
import com.manaknight.app.model.remote.*
import com.manaknight.app.model.remote.profitPro.AllLineItemsResponseModel
import com.manaknight.app.model.remote.profitPro.CreateLineItemReqModel
import com.manaknight.app.model.remote.profitPro.CommonResponse
import com.manaknight.app.model.remote.profitPro.CompanyRequest
import com.manaknight.app.model.remote.profitPro.CreateDrawAmount
import com.manaknight.app.model.remote.profitPro.CreateDrawPercentage
import com.manaknight.app.model.remote.profitPro.CreatePercentageDrawRequest
import com.manaknight.app.model.remote.profitPro.CreatePriceDrawRequest
import com.manaknight.app.model.remote.profitPro.MaterialReqModel
import com.manaknight.app.model.remote.profitPro.CustomerModel
import com.manaknight.app.model.remote.profitPro.CustomerResponseModel
import com.manaknight.app.model.remote.profitPro.DefaultModel
import com.manaknight.app.model.remote.profitPro.DrawInfoRespModel
import com.manaknight.app.model.remote.profitPro.DrawItem
import com.manaknight.app.model.remote.profitPro.Labor
import com.manaknight.app.model.remote.profitPro.LinearFootReqModel
import com.manaknight.app.model.remote.profitPro.LinearResponseModel
import com.manaknight.app.model.remote.profitPro.MaterialItem
import com.manaknight.app.model.remote.profitPro.MaterialResponseModel
import com.manaknight.app.model.remote.profitPro.ProjectModel
import com.manaknight.app.model.remote.profitPro.ProjectTrackingResponse
import com.manaknight.app.model.remote.profitPro.SendInvoiceRequest
import com.manaknight.app.model.remote.profitPro.UpdateDrawAmount
import com.manaknight.app.model.remote.profitPro.UpdateDrawPercentage
import com.manaknight.app.model.remote.profitPro.UpdateDrawRequest
import com.manaknight.app.model.remote.profitPro.UpdateDrawRequest2
import com.manaknight.app.model.remote.profitPro.UpdateLineItemReqModel
import com.manaknight.app.network.Resource
import com.manaknight.app.network.Status
import com.manaknight.app.repositories.APIRepository
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import okhttp3.MultipartBody
import com.manaknight.app.model.remote.profitPro.*








enum class ItemVisualState {
    NORMAL, ADDED, EDITED_UP, EDITED_DOWN, DELETED
}

data class LinearItemDisplay(
    val id: Int?,
    val name: String?,
    val cost: Int?,
    val laborCost: Int?,
    var units: Int? = null,
    var selectedID: Int? = null,
    var visualState: ItemVisualState = ItemVisualState.NORMAL
)

data class MaterialDisplay(
    val id: Int,
    val name: String,
    val cost: String,
    val units: Int,
    val selectedID: Int?,
    val isSelected: Boolean
)



class BaasViewModel(
    private val repository: APIRepository
) : ViewModel() {


    fun signupCompanySeup(request: CompanyRequest): LiveData<Resource<CommonResponse>> {
        return repository.signupCompanySeup(request)
    }

    fun updateCompanyDefault(request: DefaultModel): LiveData<Resource<CommonResponse>> {
        return repository.updateCompanyDefault(request)
    }
    fun createMaterial(name: String? = null,
                       email: String? = null,
                       userId: Int? = null,
                       session_name: String? = null,
                       cost : Int,
                       hidden : Int =0,
                       is_default : Int): LiveData<Resource<CommonResponse>> {
        return repository.createMaterial(MaterialRequestModel(
            user_id = userId,
            name = name,
            email = email,
            is_default = is_default,
            cost = cost,
            session_name = session_name,
            hidden = hidden,
            pdf = null,
        ))
    }

    fun updateMaterial(name: String? = null,
                       email: String? = null,
                       userId: Int? = null,
                       session_name: String? = null,
                       cost : Int,
                       hidden : Int =0,
                       is_default : Int, id: Int? = null): LiveData<Resource<CommonResponse>> {
        return repository.updateMaterial(MaterialRequestModel(
            user_id = userId,
            name = name,
            email = email,
            is_default = is_default,
            cost = cost,
            session_name = session_name,
            hidden = hidden,
            pdf = null,
        ),  id)
    }

    fun searchCustomers(searchText: String?): LiveData<Resource<CustomerResponseModel>> {
        return repository.searchCustomers(searchText)
    }

    fun createCustomer(name: String? = "",email: String? = "",phone: String? = "",address: String? = "",userId: Int? = 0): LiveData<Resource<CommonResponse>> {
        return repository.createCustomer(CustomerModel(userId, name, email, phone, address))
    }

    fun updateCustomer(name: String? = "",email: String? = "",phone: String? = "",address: String? = "",userId: Int? = 0,id: Int? = 0): LiveData<Resource<CommonResponse>> {
        return repository.updateCustomer(CustomerModel(userId, name, email, phone, address),  id)
    }

    fun createNewEstimation(customerID: Int?): LiveData<Resource<CommonResponse>> {
        return repository.createNewEstimation(ProjectModel(customerID))
    }

    fun getDefaultMaterialList(order: String? = "",size: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<MaterialResponseModel>> {
        return repository.getDefaultMaterialList(     order, size, filter, join,  )
    }

    fun createDefaultMaterial(userId: Int? = 0,cost: Int? = 0, name: String? = "",hidden: Int? = 0): LiveData<Resource<CommonResponse>> {
        return repository.createDefaultMaterial(  MaterialReqModel(userId,cost,name,hidden) )
    }

    fun addLineItem(request: CreateLineItemReqModel): LiveData<Resource<CommonResponse>> {
        return repository.addLineItem(request)
    }

    fun updateLineItem(itemID: Int, request: UpdateLineItemReqModel): LiveData<Resource<CommonResponse>> {
        return repository.updateLineItem(itemID, request)
    }

    fun getSquareFootLinealFootCosts(type: String?): LiveData<Resource<LinearResponseModel>> {
        return repository.getSquareFootLinealFootCosts(type)
    }

    fun addLinealFootCost(name: String?,cost: Int?,laborCost: Int?,hidden: Int?): LiveData<Resource<CommonResponse>> {
        return repository.addLinealFootCost(  LinearFootReqModel(name,cost,laborCost,hidden))
    }

    fun addSquareFootCost(name: String?,cost: Int?,laborCost: Int?,hidden: Int?): LiveData<Resource<CommonResponse>> {
        return repository.addSquareFootCost( LinearFootReqModel(name,cost,laborCost,hidden))
    }

    fun getSingleProjectDetails(projectId: Int): LiveData<Resource<AllLineItemsResponseModel>> {
        return repository.getSingleProjectDetails(projectId)
    }














//      .................From the github...................................
// ........................................................................................
// ...........................................................................




    private val _userId = MutableLiveData<Int>()
    private val _projectsResource = MutableLiveData<Resource<ProjectResponseModel>>() // ✅ Fix here
    val projectsResource: LiveData<Resource<ProjectResponseModel>> = _projectsResource

    fun setUserId(newUserId: Int) {
        if (_userId.value != newUserId) {
            _userId.value = newUserId
            fetchProjects(newUserId) // ✅ Fetch new data when userId changes
        }
    }

    private fun fetchProjects(userId: Int) {
        _projectsResource.value = Resource.loading(null) // Show loading state
        viewModelScope.launch {
            try {
                repository.getAllProjects(userId).observeForever { response ->  // ✅ Observe LiveData
                    _projectsResource.postValue(response) // ✅ Update MutableLiveData
                }
            } catch (e: Exception) {
                _projectsResource.postValue(Resource.error(e.message ?: "Error", null,400))
            }
        }
    }


    private val _projectId = MutableLiveData<Int>()
    private val _projectDetailsResource = MutableLiveData<Resource<AllLineItemsResponseModel>>() // ✅ Fix here
    val projectDetailsResource: LiveData<Resource<AllLineItemsResponseModel>> = _projectDetailsResource

    fun setProjectId(newProjectId: Int) {
        if (_projectId.value != newProjectId) {
            _projectId.value = newProjectId
            fetchProjectDetails(newProjectId) // ✅ Fetch new data when userId changes
        }
    }




    public fun fetchProjectDetails(newProjectId: Int) {
        _projectDetailsResource.value = Resource.loading(null) // Show loading state
        viewModelScope.launch {
            try {
                repository.getSingleProjectDetails(newProjectId).observeForever { response ->  // ✅ Observe LiveData
                    _projectDetailsResource.postValue(response) // ✅ Update MutableLiveData
                }
            } catch (e: Exception) {
                _projectDetailsResource.postValue(Resource.error(e.message ?: "Error", null,400))
            }
        }
    }


//..................for cahcing the data when add or edit is clicked
    private val _cachedProjectDetailsResource = MutableLiveData<AllLineItemsResponseModel?>()
    val cachedProjectDetailsResource: LiveData<AllLineItemsResponseModel?> = _cachedProjectDetailsResource

    fun cacheProjectDetailsResource(resource: AllLineItemsResponseModel?) {
        _cachedProjectDetailsResource.value = resource
    }






    fun getProjects(user_id: Int): LiveData<Resource<ProjectResponseModel>> {
        return repository.getAllProjects(user_id)
    }
    private val _projectDetails = MutableLiveData<Resource<AllLineItemsResponseModel>>()
    val projectDetails: LiveData<Resource<AllLineItemsResponseModel>> = _projectDetails

    private var cachedProjectDetails: Resource<AllLineItemsResponseModel>? = null
    private var apiCallMade = false

    fun getSingleProjectDetailsModified(projectId: Int): LiveData<Resource<AllLineItemsResponseModel>> {
        if (!apiCallMade) {
            apiCallMade = true;
            Log.d("BaasViewModel", "getSingleProjectDetails called for projectId: $projectId")

            viewModelScope.launch {
                _projectDetails.value = Resource.loading(null)
                Log.d("BaasViewModel", "Loading project details...")

                try {
                    val response = repository.getSingleProjectDetails(projectId).value?.data;
                    cachedProjectDetails = Resource.success(response);
                    cachedProjectDetails?.let{
                        _projectDetails.value = it;
                    }

                    Log.d("BaasViewModel", "Project details loaded successfully: $response")

                } catch (e: Exception) {
                    cachedProjectDetails = Resource.error(e.message ?: "An error occurred", null, 400);
                    cachedProjectDetails?.let{
                        _projectDetails.value = it;
                    }
                    Log.e("BaasViewModel", "Error loading project details: ${e.message}", e)
                }
            }
        } else {
            Log.d("BaasViewModel", "cached data returned.");
            cachedProjectDetails?.let {
                _projectDetails.value = it;
            }
        }

        return projectDetails;
    }








// ......................................New implementations.....................................
//      ............................................................................................
// ..........................................................................................................



    // Existing LiveData and functions ...

    private val _linearItems = MutableLiveData<List<LinearItemDisplay>>(emptyList())
    val linearItems: LiveData<List<LinearItemDisplay>> = _linearItems

    private val _linealCostsResource = MutableLiveData<Resource<LinearResponseModel>>()
    val linealCostsResource: LiveData<Resource<LinearResponseModel>> = _linealCostsResource



    fun fetchSquareFootLinealFootCosts(type: String?) {
        _linealCostsResource.value = Resource.loading(null)
        viewModelScope.launch {
            try {
                repository.getSquareFootLinealFootCosts(type).observeForever { response ->
                    _linealCostsResource.postValue(response)
                    // Update linearItems LiveData upon successful fetch
                    if (response.status == Status.SUCCESS) {
                        _linearItems.postValue(response.data?.list?.map { linearCost ->
                            LinearItemDisplay(
                                id = linearCost.id,
                                name = linearCost.name,
                                cost = linearCost.cost,
                                laborCost = linearCost.labor_cost?.toInt()
                            )
                        } ?: emptyList())
                    }
                }
            } catch (e: Exception) {
                _linealCostsResource.postValue(Resource.error(e.message ?: "Error", null, 400))
            }
        }
    }



    private val _materialListResource = MutableLiveData<Resource<MaterialResponseModel>>()
    val materialListResource: LiveData<Resource<MaterialResponseModel>> = _materialListResource

    // Optional display model (similar to linearItems)
    private val _materialItems = MutableLiveData<List<MaterialDisplay>>(emptyList())
    val materialItems: LiveData<List<MaterialDisplay>> = _materialItems

    fun fetchDefaultMaterialList(
        order: String? = "",
        size: String? = "",
        filter: String? = "",
        join: String? = ""
    ) {
        _materialListResource.value = Resource.loading(null)

        viewModelScope.launch {
            try {
                repository.getDefaultMaterialList(order, size, filter, join).observeForever { response ->
                    _materialListResource.postValue(response)

                    if (response.status == Status.SUCCESS) {
                        _materialItems.postValue(
                            response.data?.list?.map { material ->
                                MaterialDisplay(
                                    id = material.id ?: 0,
                                    name = material.name.orEmpty(),
                                    cost = material.cost.orEmpty(),
                                    units = material.units ?: 0,
                                    selectedID = material.selectedID,
                                    isSelected = material.isSelected ?: false
                                )
                            } ?: emptyList()
                        )
                    }
                }
            } catch (e: Exception) {
                _materialListResource.postValue(Resource.error(e.message ?: "Error", null, 400))
            }
        }
    }




    private fun resetLinearItemVisualStateAfterDelay(item: LinearItemDisplay) {
        viewModelScope.launch {
            delay(1000) // Adjust delay as needed
            _linearItems.value = (_linearItems.value ?: emptyList()).map {
                if (it.id == item.id) {
                    it.copy(visualState = ItemVisualState.NORMAL)
                } else {
                    it
                }
            }
        }
    }





    private val _teamMembersResource = MutableLiveData<Resource<GetTeamMemberListResponse>>()
    val teamMembersResource: LiveData<Resource<GetTeamMemberListResponse>> = _teamMembersResource

    private val _teamMemberFilters = MutableLiveData<TeamMemberFilters>()

    data class TeamMemberFilters(
        val order: String? = null,
        val size: String? = null,
        val filter: String? = null,
        val join: String? = null
    )

    fun setTeamMemberFilters(
        order: String? = _teamMemberFilters.value?.order,
        size: String? = _teamMemberFilters.value?.size,
        filter: String? = _teamMemberFilters.value?.filter,
        join: String? = _teamMemberFilters.value?.join
    ) {
        val newFilters = TeamMemberFilters(order, size, filter, join)
        if (_teamMemberFilters.value != newFilters) {
            _teamMemberFilters.value = newFilters
            fetchTeamMembers(newFilters)
        }
    }
    fun refreshTeamMembers() {
        _teamMemberFilters.value?.let {
            fetchTeamMembers(it)
        }
    }

    private fun fetchTeamMembers(filters: TeamMemberFilters) {
        _teamMembersResource.value = Resource.loading(null)
        viewModelScope.launch {
            try {
                repository.getTeamMemberList(
                    filters.order,
                    filters.size,
                    filters.filter,
                    filters.join
                ).observeForever { response -> // Assuming repository.getTeamMemberList returns Flow
                    _teamMembersResource.postValue(response)
                }
            } catch (e: Exception) {
                _teamMembersResource.postValue(Resource.error(e.message ?: "Error fetching team members", null, 400))
            }
        }
    }





    private val _createTeamMemberResponse = MutableLiveData<Resource<CreateTeamMemberResponse>>()
    val createTeamMemberResponse: LiveData<Resource<CreateTeamMemberResponse>> = _createTeamMemberResponse

    private val _updateTeamMemberResponse = MutableLiveData<Resource<UpdateTeamMemberResponse>>()
    val updateTeamMemberResponse: LiveData<Resource<UpdateTeamMemberResponse>> = _updateTeamMemberResponse

    private val _deleteTeamMemberResponse = MutableLiveData<Resource<DeleteTeamMemberResponse>>()
    val deleteTeamMemberResponse: LiveData<Resource<DeleteTeamMemberResponse>> = _deleteTeamMemberResponse

    private val _updateDefaultResponse = MutableLiveData<Resource<CommonResponse>>()
    val updateDefaultResponse: LiveData<Resource<CommonResponse>> = _updateDefaultResponse

    fun clearUpdateTeamMemberResponse() {
        _updateTeamMemberResponse.value = null
    }
    fun clearCreateTeamMemberNewResponse() {
        _createTeamMemberResponse.value = null
    }
    fun clearUpdateCompanyDefaultNewResponse() {
        _updateDefaultResponse.value = null
    }
    fun clearDeleteTeamMemberNewResponse() {
        _deleteTeamMemberResponse.value = null
    }

    fun createTeamMemberNew(
        projectId: Int? = 0,
        userId: Int? = 0,
        name: String? = "",
        hourlyRate: Int? = 0,
        isDefault: Int? = 0
    ): LiveData<Resource<CreateTeamMemberResponse>> {
        _createTeamMemberResponse.value = Resource.loading(null)
        viewModelScope.launch {
            try {
                val response = repository.createTeamMember(
                    CreateTeamMemberRequest(projectId, userId, name, hourlyRate, isDefault)
                ).observeForever { resource ->
                    _createTeamMemberResponse.postValue(resource)
                }
            } catch (e: Exception) {
                _createTeamMemberResponse.postValue(Resource.error(e.message ?: "Error creating team member", null, 400))
            }
        }
        return _createTeamMemberResponse
    }

    fun updateTeamMemberNew(
        projectId: Int? = 0,
        userId: Int? = 0,
        name: String? = "",
        hourlyRate: Int? = 0,
        isDefault: Int? = 0,
        id: Int? = 0
    ): LiveData<Resource<UpdateTeamMemberResponse>> {
        _updateTeamMemberResponse.value = Resource.loading(null)
        viewModelScope.launch {
            try {
                repository.updateTeamMember(
                    UpdateTeamMemberRequest(projectId, userId, name, hourlyRate, isDefault),
                    id
                ).observeForever { resource ->
                    _updateTeamMemberResponse.postValue(resource)
                }
            } catch (e: Exception) {
                _updateTeamMemberResponse.postValue(Resource.error(e.message ?: "Error updating team member", null, 400))
            }
        }
        return _updateTeamMemberResponse
    }

    fun deleteTeamMemberNew(id: Int? = 0): LiveData<Resource<DeleteTeamMemberResponse>> {
        _deleteTeamMemberResponse.value = Resource.loading(null)
        viewModelScope.launch {
            try {
                repository.deleteTeamMember(id).observeForever { resource ->
                    _deleteTeamMemberResponse.postValue(resource)
                }
            } catch (e: Exception) {
                _deleteTeamMemberResponse.postValue(Resource.error(e.message ?: "Error deleting team member", null, 400))
            }
        }
        return _deleteTeamMemberResponse
    }

    fun updateCompanyDefaultNew(request: DefaultModel): LiveData<Resource<CommonResponse>> {
        _updateDefaultResponse.value = Resource.loading(null)
        viewModelScope.launch {
            try {
                repository.updateCompanyDefault(request).observeForever { resource ->
                    _updateDefaultResponse.postValue(resource)
                }
            } catch (e: Exception) {
                _updateDefaultResponse.postValue(Resource.error(e.message ?: "Error updating defaults", null, 400))
            }
        }
        return _updateDefaultResponse
    }



    private val _addSquareFootCostResponse = MutableLiveData<Resource<CommonResponse>>()
    val addSquareFootCostResponse: LiveData<Resource<CommonResponse>> = _addSquareFootCostResponse

    private val _addLinealFootCostResponse = MutableLiveData<Resource<CommonResponse>>()
    val addLinealFootCostResponse: LiveData<Resource<CommonResponse>> = _addLinealFootCostResponse

    private val _updateLinealFootCostResponse = MutableLiveData<Resource<CommonResponse>>()
    val updateLinealFootCostResponse: LiveData<Resource<CommonResponse>> = _updateLinealFootCostResponse

    private val _updateSquareFootCostResponse = MutableLiveData<Resource<CommonResponse>>()
    val updateSquareFootCostResponse: LiveData<Resource<CommonResponse>> = _updateSquareFootCostResponse

    private val _deleteLinealFootCostResponse = MutableLiveData<Resource<CommonResponse>>()
    val deleteLinealFootCostResponse: LiveData<Resource<CommonResponse>> = _deleteLinealFootCostResponse

    private val _deleteSquareFootCostResponse = MutableLiveData<Resource<CommonResponse>>()
    val deleteSquareFootCostResponse: LiveData<Resource<CommonResponse>> = _deleteSquareFootCostResponse

    private val _updateDefaultMaterialResponse = MutableLiveData<Resource<UpdateDefaultMaterialResponse>>()
    val updateDefaultMaterialResponse: LiveData<Resource<UpdateDefaultMaterialResponse>> = _updateDefaultMaterialResponse

    private val _deleteDefaultMaterialResponse = MutableLiveData<Resource<DeleteDefaultMaterialResponse>>()
    val deleteDefaultMaterialResponse: LiveData<Resource<DeleteDefaultMaterialResponse>> = _deleteDefaultMaterialResponse

    private val _createMaterialResponse = MutableLiveData<Resource<CommonResponse>>()
    val createMaterialResponse: LiveData<Resource<CommonResponse>> = _createMaterialResponse

    fun clearAddSquareFootCostResponse() {
        _addSquareFootCostResponse.value = null
    }

    fun clearAddLinealFootCostResponse() {
        _addLinealFootCostResponse.value = null
    }

    fun clearUpdateLinealFootCostResponse() {
        _updateLinealFootCostResponse.value = null
    }

    fun clearUpdateSquareFootCostResponse() {
        _updateSquareFootCostResponse.value = null
    }

    fun clearDeleteLinealFootCostResponse() {
        _deleteLinealFootCostResponse.value = null
    }

    fun clearDeleteSquareFootCostResponse() {
        _deleteSquareFootCostResponse.value = null
    }

    fun clearUpdateDefaultMaterialResponse() {
        _updateDefaultMaterialResponse.value = null
    }

    fun clearDeleteDefaultMaterialResponse() {
        _deleteDefaultMaterialResponse.value = null
    }

    fun clearCreateMaterialResponse() {
        _createMaterialResponse.value = null
    }

    private val _getLinealFootCostsResponse = MutableLiveData<Resource<LinearResponseModel>>()
    val getLinealFootCostsResponse: LiveData<Resource<LinearResponseModel>> = _getLinealFootCostsResponse

    private val _getSquareFootCostsResponse = MutableLiveData<Resource<LinearResponseModel>>()
    val getSquareFootCostsResponse: LiveData<Resource<LinearResponseModel>> = _getSquareFootCostsResponse

    fun clearGetLinealFootCostsResponse() {
        _getLinealFootCostsResponse.value = null
    }

    fun clearGetSquareFootCostsResponse() {
        _getSquareFootCostsResponse.value = null
    }

    fun getSquareFootLinealFootCostsNew(type: String?): LiveData<Resource<LinearResponseModel>> {
        when (type) {
            "lineal" -> {
                _getLinealFootCostsResponse.value = Resource.loading(null)
                viewModelScope.launch {
                    try {
                        repository.getSquareFootLinealFootCosts(type).observeForever { resource ->
                            _getLinealFootCostsResponse.postValue(resource)
                        }
                    } catch (e: Exception) {
                        _getLinealFootCostsResponse.postValue(
                            Resource.error(e.message ?: "Error fetching lineal foot costs", null, 400)
                        )
                    }
                }
                return _getLinealFootCostsResponse
            }
            "foot" -> {
                _getSquareFootCostsResponse.value = Resource.loading(null)
                viewModelScope.launch {
                    try {
                        repository.getSquareFootLinealFootCosts(type).observeForever { resource ->
                            _getSquareFootCostsResponse.postValue(resource)
                        }
                    } catch (e: Exception) {
                        _getSquareFootCostsResponse.postValue(
                            Resource.error(e.message ?: "Error fetching square foot costs", null, 400)
                        )
                    }
                }
                return _getSquareFootCostsResponse
            }
            else -> {
                // Handle invalid type, perhaps by returning a LiveData with an error state immediately
                val errorLiveData = MutableLiveData<Resource<LinearResponseModel>>()
                errorLiveData.value = Resource.error("Invalid type: $type", null, 400)
                return errorLiveData
            }
        }
    }

    fun addSquareFootCostNew(
        name: String? = null,
        cost: Int? = null,
        laborCost: Int? = null,
        hidden: Int? = null
    ): LiveData<Resource<CommonResponse>> {
        _addSquareFootCostResponse.value = Resource.loading(null)
        viewModelScope.launch {
            try {
                repository.addSquareFootCost(LinearFootReqModel(name, cost, laborCost, hidden)).observeForever { resource ->
                    _addSquareFootCostResponse.postValue(resource)
                }
            } catch (e: Exception) {
                _addSquareFootCostResponse.postValue(
                    Resource.error(e.message ?: "Error adding square foot cost", null, 400)
                )
            }
        }
        return _addSquareFootCostResponse
    }

    fun addLinealFootCostNew(
        name: String? = null,
        cost: Int? = null,
        laborCost: Int? = null,
        hidden: Int? = null
    ): LiveData<Resource<CommonResponse>> {
        _addLinealFootCostResponse.value = Resource.loading(null)
        viewModelScope.launch {
            try {
                repository.addLinealFootCost(LinearFootReqModel(name, cost, laborCost, hidden)).observeForever { resource ->
                    _addLinealFootCostResponse.postValue(resource)
                }
            } catch (e: Exception) {
                _addLinealFootCostResponse.postValue(
                    Resource.error(e.message ?: "Error adding lineal foot cost", null, 400)
                )
            }
        }
        return _addLinealFootCostResponse
    }

    fun updateLinealFootCostNew(
        cost: Int = 0,
        name: String? = null,
        hidden: Int? = 0,
        id: Int? = 0,
        laborCost: Int,
        isDefault: Int? = null
    ): LiveData<Resource<CommonResponse>> {
        _updateLinealFootCostResponse.value = Resource.loading(null)
        viewModelScope.launch {
            try {
                repository.updateLinealFootCost(
                    LinearFootReqModel(name, cost, hidden, laborCost, isDefault),
                    id
                ).observeForever { resource ->
                    _updateLinealFootCostResponse.postValue(resource)
                }
            } catch (e: Exception) {
                _updateLinealFootCostResponse.postValue(
                    Resource.error(e.message ?: "Error updating lineal foot cost", null, 400)
                )
            }
        }
        return _updateLinealFootCostResponse
    }

    fun updateSquareFootCostNew(
        cost: Int = 0,
        name: String? = null,
        hidden: Int? = 0,
        id: Int? = 0,
        laborCost: Int,
        isDefault: Int? = null
    ): LiveData<Resource<CommonResponse>> {
        _updateSquareFootCostResponse.value = Resource.loading(null)
        viewModelScope.launch {
            try {
                repository.updateSquareFootCost(
                    LinearFootReqModel(name, cost, hidden, laborCost, isDefault),
                    id
                ).observeForever { resource ->
                    _updateSquareFootCostResponse.postValue(resource)
                }
            } catch (e: Exception) {
                _updateSquareFootCostResponse.postValue(
                    Resource.error(e.message ?: "Error updating square foot cost", null, 400)
                )
            }
        }
        return _updateSquareFootCostResponse
    }

    fun deleteLinealFootCostsNew(id: Int? = 0): LiveData<Resource<CommonResponse>> {
        _deleteLinealFootCostResponse.value = Resource.loading(null)
        viewModelScope.launch {
            try {
                repository.deleteLinealFootCosts(id).observeForever { resource ->
                    _deleteLinealFootCostResponse.postValue(resource)
                }
            } catch (e: Exception) {
                _deleteLinealFootCostResponse.postValue(
                    Resource.error(e.message ?: "Error deleting lineal foot cost", null, 400)
                )
            }
        }
        return _deleteLinealFootCostResponse
    }

    fun deleteSquareFootCostNew(id: Int? = 0): LiveData<Resource<CommonResponse>> {
        _deleteSquareFootCostResponse.value = Resource.loading(null)
        viewModelScope.launch {
            try {
                repository.deleteSquareFootCost(id).observeForever { resource ->
                    _deleteSquareFootCostResponse.postValue(resource)
                }
            } catch (e: Exception) {
                _deleteSquareFootCostResponse.postValue(
                    Resource.error(e.message ?: "Error deleting square foot cost", null, 400)
                )
            }
        }
        return _deleteSquareFootCostResponse
    }

    fun updateDefaultMaterialNew(
        userId: String? = null,
        cost: String? = null,
        name: String? = null,
        hidden: Int? = null,
        id: Int? = null
    ): LiveData<Resource<UpdateDefaultMaterialResponse>> {
        _updateDefaultMaterialResponse.value = Resource.loading(null)
        viewModelScope.launch {
            try {
                repository.updateDefaultMaterial(
                    UpdateDefaultMaterialRequest(userId, cost, name, hidden),
                    id
                ).observeForever { resource ->
                    _updateDefaultMaterialResponse.postValue(resource)
                }
            } catch (e: Exception) {
                _updateDefaultMaterialResponse.postValue(
                    Resource.error(e.message ?: "Error updating default material", null, 400)
                )
            }
        }
        return _updateDefaultMaterialResponse
    }

    fun deleteDefaultMaterialNew(id: Int? = 0): LiveData<Resource<DeleteDefaultMaterialResponse>> {
        _deleteDefaultMaterialResponse.value = Resource.loading(null)
        viewModelScope.launch {
            try {
                repository.deleteDefaultMaterial(id).observeForever { resource ->
                    _deleteDefaultMaterialResponse.postValue(resource)
                }
            } catch (e: Exception) {
                _deleteDefaultMaterialResponse.postValue(
                    Resource.error(e.message ?: "Error deleting default material", null, 400)
                )
            }
        }
        return _deleteDefaultMaterialResponse
    }

    fun createMaterialNew(
        name: String? = null,
        email: String? = null,
        userId: Int? = null,
        session_name: String? = null,
         cost : Int,
         hidden : Int =0,
         is_default : Int
    ): LiveData<Resource<CommonResponse>> {
        _createMaterialResponse.value = Resource.loading(null)
        viewModelScope.launch {
            try {
                repository.createMaterial(MaterialRequestModel(
                    user_id = userId,
                    name = name,
                    email = email,
                    is_default = is_default,
                    cost = cost,
                    session_name = session_name,
                    hidden = hidden,
                    pdf = null,

                )).observeForever { resource ->
                    _createMaterialResponse.postValue(resource)
                }
            } catch (e: Exception) {
                _createMaterialResponse.postValue(Resource.error(e.message ?: "Error creating material", null, 400))
            }
        }
        return _createMaterialResponse
    }


//.............. Prev implementations........................................
//      ..................................................................
//...................................................................................



    fun initializeDraws(projectId: Int): LiveData<Resource<CommonResponse>> {
        return repository.initializeDraws(projectId)
    }

    fun getAllDraws(projectId: Int): LiveData<Resource<DrawInfoRespModel>> {
        return repository.getAllDraws(projectId)
    }

    fun deleteDraws(id: Int? = 0): LiveData<Resource<CommonResponse>> {
        return repository.deleteDraws(id)
    }

    fun createPriceDraw(projectID: Int?, description: String?, amount: String?): LiveData<Resource<CommonResponse>> {

        val drawAmountList = arrayListOf<CreateDrawAmount>()
        drawAmountList.add(CreateDrawAmount(0, description, amount))

        return repository.createPriceDraw( CreatePriceDrawRequest(projectID, drawAmountList))
    }

    fun createPercentageDraw(projectID: Int?, description: String?, percentage: String?): LiveData<Resource<CommonResponse>> {

        val drawPercentageList = arrayListOf<CreateDrawPercentage>()
        drawPercentageList.add(CreateDrawPercentage(1, description, percentage))

        return repository.createPercentageDraw( CreatePercentageDrawRequest(projectID,drawPercentageList))
    }
    fun sendInvoice( pdf: String?,  session_name: String?,  email: String?, project_id:Int?): LiveData<Resource<CommonResponse>> {
       return repository.sendInvoice( SendInvoiceRequest(pdf,session_name,email,project_id))
    }

    fun updatePriceDraw(projectID: Int?, description: String?, amount: String?, id: Int?): LiveData<Resource<CommonResponse>> {

        val drawAmountList = arrayListOf<UpdateDrawAmount>()
        drawAmountList.add(UpdateDrawAmount(id,0, description, amount))

        return repository.updatePriceDraw( UpdateDrawRequest(drawAmountList), projectID)
    }

    fun updatePercentageDraw(projectID: Int?, description: String?, percentage: String?, id: Int?): LiveData<Resource<CommonResponse>> {

        val drawPercentageList = arrayListOf<UpdateDrawPercentage>()
        drawPercentageList.add(UpdateDrawPercentage(id,1, description, percentage))

        return repository.updatePercentageDraw( UpdateDrawRequest2(drawPercentageList), projectID)

    }

    fun deleteLineItems(id: Int? = 0): LiveData<Resource<CommonResponse>> {
        return repository.deleteLineItems(id)
    }

    fun getAllUser(): LiveData<Resource<FriendListResponse>> {
      return repository.getAllUser()
  }

  fun createRoomRequests(user_id: Int, other_user_id: Int?): LiveData<Resource<CreateRoomResponse>> {
    return repository.createRoomRequests(CreateRoomRequests(user_id, other_user_id))
}

    fun sendTextMessage(room_id: String, user_id: Int, message: String?): LiveData<Resource<ChatTextResponse>> {
      return repository.sendTextMessage(ChatTextRequest(room_id, user_id, message))
  }

  fun getChats(room_id: String): LiveData<Resource<ChatResponse>> {
      return repository.getChats(ChatRequest(room_id))
  }

  fun getStartPool(user_id: Int): LiveData<Resource<SingleChatMessageResponse>> {
      return repository.getStartPool(user_id)
  }

  fun getAllRoom(user_id: Int): LiveData<Resource<ChatRoomResponse>> {
      return repository.getAllRoom(user_id)
  }



    fun sendMessageToBot(request: String): LiveData<Resource<ChatBotTextResponse>> {
      return repository.sendMessageToBot(request)
  }

  fun sendMessageToBot(request: ChatBotRequest): LiveData<Resource<ChatBotTextResponse>> {
      return repository.sendMessageToBot(request)
  }


  fun createChangeOrder(data: String?,projectId: Any): LiveData<Resource<CreateChangeOrderResponse>> {
     return repository.createChangeOrder(  CreateChangeOrderRequest(data),  projectId,   )
   }



  fun finalizeProject(): LiveData<Resource<FinalizeProjectResponse>> {
     return repository.finalizeProject(      )
   }



  fun getProjectReview(projectId: Any): LiveData<Resource<GetProjectReviewResponse>> {
     return repository.getProjectReview(    projectId,   )
   }



  fun updateDraws(draws: String?,projectId: Int, paymentType:String?,status:Int, description:String?,percentage:String?, amount:String?): LiveData<Resource<UpdateDrawsResponse>> {
     return repository.updateDraws(  UpdateDrawsRequest(draws, amount, percentage, description, status, project_id=projectId, paymentType),  projectId,   )
   }


    private val _trackingResponse = MutableLiveData<Resource<ProjectTrackingResponse>>()
    val trackingResponse: LiveData<Resource<ProjectTrackingResponse>> = _trackingResponse



    fun setTrackingProjectId(id: Int) {
        _projectId.value = id
        fetchProjectTrackingData(id)
    }

    fun fetchProjectTrackingData(projectId: Int) {
        viewModelScope.launch {
            _trackingResponse.postValue(Resource.loading(null))
            repository.getProjectTrackingDetails(projectId).observeForever { resource ->
                _trackingResponse.postValue(resource)
            }
        }
    }


  fun trackingMaterial(projectId: Int): LiveData<Resource<TrackingMaterialResponse>> {
     return repository.trackingMaterial(    projectId,   )
   }



  fun trackingLabour(projectId: Int): LiveData<Resource<TrackingLabourResponse>> {
     return repository.trackingLabour(    projectId,   )
   }



  fun trackingDraws(projectId: Int,status: String?): LiveData<Resource<TrackingDrawsResponse>> {
     return repository.trackingDraws(    projectId,  status,  )
   }



  fun getLineDetails(lineId: Any): LiveData<Resource<GetLineDetailsResponse>> {
     return repository.getLineDetails(    lineId,   )
   }







  fun finalizingOnboarding(): LiveData<Resource<FinalizingOnboardingResponse>> {
     return repository.finalizingOnboarding(      )
   }















  fun initializeUser(): LiveData<Resource<InitializeUserResponse>> {
     return repository.initializeUser(      )
   }



  fun saveDefaultsOnbording(defaultHourlyRate: Int,defaultProfitOverhead: Int): LiveData<Resource<SaveDefaultsOnbordingResponse>> {
     return repository.saveDefaultsOnbording(  SaveDefaultsOnbordingRequest(defaultHourlyRate,defaultProfitOverhead),    )
   }



  fun getProjects(type: String?,timePeriod: String?): LiveData<Resource<GetProjectsResponse>> {
     return repository.getProjects(     type, timePeriod,  )
   }



  fun onboarding(defaultHourlyRate: Int,defaultProfitOverhead: Int,name: String?): LiveData<Resource<OnboardingResponse>> {
     return repository.onboarding(  OnboardingRequest(defaultHourlyRate,defaultProfitOverhead,name),    )
   }



  fun companyOverview(): LiveData<Resource<CompanyOverviewResponse>> {
     return repository.companyOverview(      )
   }



  fun companyDetails(): LiveData<Resource<CompanyDetailsResponse>> {
     return repository.companyDetails(      )
   }



  fun getProjectStats(): LiveData<Resource<GetProjectStatsResponse>> {
     return repository.getProjectStats(      )
   }



  fun lambdaCheck(role: String?): LiveData<Resource<LambdaCheckResponse>> {
     return repository.lambdaCheck(  LambdaCheckRequest(role),    )
   }



  fun twoFALogin(email: String?,password: String?,role: String?): LiveData<Resource<TwoFALoginResponse>> {
     return repository.twoFALogin(  TwoFALoginRequest(email,password,role),    )
   }



  fun twoFASignin(email: String?,password: String?,role: String?): LiveData<Resource<TwoFASigninResponse>> {
     return repository.twoFASignin(  TwoFASigninRequest(email,password,role),    )
   }



  fun twoFAAuthorize(userId: String?): LiveData<Resource<TwoFAAuthorizeResponse>> {
     return repository.twoFAAuthorize(  TwoFAAuthorizeRequest(userId),    )
   }



  fun twoFAEnable(userId: String?,type: String?,phone: String?,token: String?): LiveData<Resource<TwoFAEnableResponse>> {
     return repository.twoFAEnable(  TwoFAEnableRequest(userId,type,phone,token),    )
   }



  fun twoFADisable(userId: String?): LiveData<Resource<TwoFADisableResponse>> {
     return repository.twoFADisable(  TwoFADisableRequest(userId),    )
   }



  fun twoFAVerify(token: String?,accessToken: String?): LiveData<Resource<TwoFAVerifyResponse>> {
     return repository.twoFAVerify(  TwoFAVerifyRequest(token,accessToken),    )
   }



  fun twoFAAuth(code: String?,token: String?): LiveData<Resource<TwoFAAuthResponse>> {
     return repository.twoFAAuth(  TwoFAAuthRequest(code,token),    )
   }



  fun analyticsLog(userId: String?,sessionId: String?,status: String?,userAgent: String?,application: String?,document: String?,url: ArrayList<Map<String, Any>>,linkClicks: Number?,clickedButtons: ArrayList<Map<String, Any>>,clientIp: String?,events: ArrayList<Map<String, Any>>,totalTime: Number?,data: Map<String, Any>): LiveData<Resource<AnalyticsLogResponse>> {
     return repository.analyticsLog(  AnalyticsLogRequest(userId,sessionId,status,userAgent,application,document,url,linkClicks,clickedButtons,clientIp,events,totalTime,data),    )
   }



  fun getAnalytics(): LiveData<Resource<GetAnalyticsResponse>> {
     return repository.getAnalytics(      )
   }



  fun logHeatmapAnalytics(userId: String?,sessionId: String?,userAgent: String?,scrollPosition: Map<String, Any>,coordinates: Map<String, Any>,status: String?,clientIp: String?,screenSize: Number?,screenWidth: Number?,page: String?,screenHeight: Number?,snapshot: String?,totalTime: Number?,data: Map<String, Any>): LiveData<Resource<LogHeatmapAnalyticsResponse>> {
     return repository.logHeatmapAnalytics(  LogHeatmapAnalyticsRequest(userId,sessionId,userAgent,scrollPosition,coordinates,status,clientIp,screenSize,screenWidth,page,screenHeight,snapshot,totalTime,data),    )
   }



  fun getHeatmapData(customDate: String?): LiveData<Resource<GetHeatmapDataResponse>> {
     return repository.getHeatmapData(     customDate,  )
   }



  fun userSessionsData(customDate: String?): LiveData<Resource<UserSessionsDataResponse>> {
     return repository.userSessionsData(     customDate,  )
   }



  fun createUserSessionsAnalytics(userId: Int,sessionId: Int,status: String?,events: ArrayList<Map<String, Any>>,screenWidth: Int,screenHeight: Int,screenSize: String?,startTime: String?,endTime: String?,htmlCopy: String?): LiveData<Resource<CreateUserSessionsAnalyticsResponse>> {
     return repository.createUserSessionsAnalytics(  CreateUserSessionsAnalyticsRequest(userId,sessionId,status,events,screenWidth,screenHeight,screenSize,startTime,endTime,htmlCopy),    )
   }



  fun appleLoginMobileEndpoint(firstName: String?,lastName: String?,identitytoken: String?,appleId: String?,role: String?): LiveData<Resource<AppleLoginMobileEndpointResponse>> {
     return repository.appleLoginMobileEndpoint(  AppleLoginMobileEndpointRequest(firstName,lastName,identitytoken,appleId,role),    )
   }



  fun appleLogin(): LiveData<Resource<AppleLoginResponse>> {
     return repository.appleLogin(      )
   }



  fun appleAuthCode(state: String?,code: String?): LiveData<Resource<AppleAuthCodeResponse>> {
     return repository.appleAuthCode(  AppleAuthCodeRequest(state,code),    )
   }



  fun googleCode(state: String): LiveData<Resource<GoogleCodeResponse>> {
     return repository.googleCode(     state,  )
   }



  fun googleCodeMobile(role: String?,isRefresh: Boolean?,code: String?): LiveData<Resource<GoogleCodeMobileResponse>> {
     return repository.googleCodeMobile(     role, isRefresh, code,  )
   }



  fun googleLogin(role: String?,companyId: String?,isRefresh: Boolean?): LiveData<Resource<GoogleLoginResponse>> {
     return repository.googleLogin(     role, companyId, isRefresh,  )
   }



  fun blogAll(limit: Int,offset: Int): LiveData<Resource<BlogAllResponse>> {
     return repository.blogAll(     limit, offset,  )
   }



  fun blogSimilar(top: Int): LiveData<Resource<BlogSimilarResponse>> {
     return repository.blogSimilar(     top,  )
   }



  fun blogFilter(categories: ArrayList<Map<String, Any>>,tags: ArrayList<Map<String, Any>>,rule: String?,search: String?,limit: Int,page: Int): LiveData<Resource<BlogFilterResponse>> {
     return repository.blogFilter(     categories, tags, rule, search, limit, page,  )
   }



  fun blogCreate(title: String?,body: String?,meta: Map<String, Any>,tags: ArrayList<Map<String, Any>>,categories: ArrayList<Map<String, Any>>,content: String?,description: String?,thumbnail: String?): LiveData<Resource<BlogCreateResponse>> {
     return repository.blogCreate(  BlogCreateRequest(title,body,meta,tags,categories,content,description,thumbnail),    )
   }



  fun blogEdit(title: String?,content: String?,description: String?,meta: Map<String, Any>,tags: ArrayList<Map<String, Any>>,categories: ArrayList<Map<String, Any>>,thumbnail: String?,id: String?): LiveData<Resource<BlogEditResponse>> {
     return repository.blogEdit(  BlogEditRequest(title,content,description,meta,tags,categories,thumbnail),  id,   )
   }



  fun blogDelete(id: String?): LiveData<Resource<BlogDeleteResponse>> {
     return repository.blogDelete(    id,   )
   }



  fun blogSingle(id: String?): LiveData<Resource<BlogSingleResponse>> {
     return repository.blogSingle(    id,   )
   }



  fun blogTags(name: String?): LiveData<Resource<BlogTagsResponse>> {
     return repository.blogTags(  BlogTagsRequest(name),    )
   }



  fun blogTagsUpdate(name: String?): LiveData<Resource<BlogTagsUpdateResponse>> {
     return repository.blogTagsUpdate(  BlogTagsUpdateRequest(name),    )
   }



  fun blogTagsRetrieve(limit: Int,page: Int,name: String?): LiveData<Resource<BlogTagsRetrieveResponse>> {
     return repository.blogTagsRetrieve(     limit, page, name,  )
   }



  fun blogTagsDeleteByID(id: String?): LiveData<Resource<BlogTagsDeleteByIDResponse>> {
     return repository.blogTagsDeleteByID(    id,   )
   }



  fun createBlogCategory(name: String?,parentId: String?): LiveData<Resource<CreateBlogCategoryResponse>> {
     return repository.createBlogCategory(  CreateBlogCategoryRequest(name,parentId),    )
   }



  fun updateBlogCategory(name: String?,parentId: String?): LiveData<Resource<UpdateBlogCategoryResponse>> {
     return repository.updateBlogCategory(  UpdateBlogCategoryRequest(name,parentId),    )
   }



  fun getBlogCategory(limit: Int,page: Int,name: String?): LiveData<Resource<GetBlogCategoryResponse>> {
     return repository.getBlogCategory(     limit, page, name,  )
   }



  fun getBlogSubcategory(id: String?): LiveData<Resource<GetBlogSubcategoryResponse>> {
     return repository.getBlogSubcategory(    id,   )
   }



  fun deleteBlogCategory(id: String?): LiveData<Resource<DeleteBlogCategoryResponse>> {
     return repository.deleteBlogCategory(    id,   )
   }



  fun captchaTest(width: Int,height: Int): LiveData<Resource<CaptchaTestResponse>> {
     return repository.captchaTest(    width, height,   )
   }



  fun captchaGenerate(width: Int,height: Int): LiveData<Resource<CaptchaGenerateResponse>> {
     return repository.captchaGenerate(    width, height,   )
   }



  fun googleCaptchaVerify(formdata: Map<String, Any>,captchatoken: String?): LiveData<Resource<GoogleCaptchaVerifyResponse>> {
     return repository.googleCaptchaVerify(  GoogleCaptchaVerifyRequest(formdata,captchatoken),    )
   }



  fun createCMSLambda(page: String?,key: String?,type: String?,value: String?): LiveData<Resource<CreateCMSLambdaResponse>> {
     return repository.createCMSLambda(  CreateCMSLambdaRequest(page,key,type,value),    )
   }



  fun updateCMSLambda(page: String?,key: String?,type: String?,value: String?,id: String?): LiveData<Resource<UpdateCMSLambdaResponse>> {
     return repository.updateCMSLambda(  UpdateCMSLambdaRequest(page,key,type,value),  id,   )
   }



  fun deleteCMSLambda(id: String?): LiveData<Resource<DeleteCMSLambdaResponse>> {
     return repository.deleteCMSLambda(    id,   )
   }



  fun getCMSByIDLambda(id: String?): LiveData<Resource<GetCMSByIDLambdaResponse>> {
     return repository.getCMSByIDLambda(    id,   )
   }



  fun getCMSByPageAndKeyLambda(page: String?,key: String?): LiveData<Resource<GetCMSByPageAndKeyLambdaResponse>> {
     return repository.getCMSByPageAndKeyLambda(    page, key,   )
   }



  fun getCMSByPageLambda(page: String?): LiveData<Resource<GetCMSByPageLambdaResponse>> {
     return repository.getCMSByPageLambda(    page,   )
   }



  fun getAllCMSLambda(): LiveData<Resource<GetAllCMSLambdaResponse>> {
     return repository.getAllCMSLambda(      )
   }



  fun registerLambda(email: String?,role: String?,verify: Boolean?,isRefresh: Boolean?,password: String?,firstName: String?,lastName: String?,photo: String?,phone: String?): LiveData<Resource<RegisterLambdaResponse>> {
     return repository.registerLambda(  RegisterLambdaRequest(email,role,verify,isRefresh,password,firstName,lastName,photo,phone),    )
   }



  fun loginLambda(email: String?,password: String?,role: String?,isRefresh: Boolean?): LiveData<Resource<LoginLambdaResponse>> {
     return repository.loginLambda(  LoginLambdaRequest(email,password,role,isRefresh),    )
   }



  fun marketingLoginLambda(email: String?,password: String?,role: String?,isRefresh: Boolean?): LiveData<Resource<MarketingLoginLambdaResponse>> {
     return repository.marketingLoginLambda(  MarketingLoginLambdaRequest(email,password,role,isRefresh),    )
   }



  fun profile(): LiveData<Resource<ProfileResponse>> {
     return repository.profile(      )
   }



  fun profileUpdate(firstName: String?,lastName: String?,photo: String?): LiveData<Resource<ProfileUpdateResponse>> {
     return repository.profileUpdate(  ProfileUpdateRequest(firstName,lastName,photo),    )
   }



  fun uploadImageLocalDefault(file: MultipartBody.Part): LiveData<Resource<UploadImageLocalDefaultResponse>> {
     return repository.uploadImageLocalDefault(      file, )
   }



  fun uploadimages3(file: MultipartBody.Part): LiveData<Resource<UploadImageS3Response>> {
     return repository.uploadimages3(      file, )
   }



  fun preferenceFetch(): LiveData<Resource<PreferenceFetchResponse>> {
     return repository.preferenceFetch(      )
   }



  fun preferenceUpdate(payload: Map<String, Any>): LiveData<Resource<PreferenceUpdateResponse>> {
     return repository.preferenceUpdate(  PreferenceUpdateRequest(payload),    )
   }



  fun getSowTree(order: String?,page: String?): LiveData<Resource<GetSowTreeResponse>> {
     return repository.getSowTree(     order, page,  )
   }



  fun appAlertsList(order: String?,page: String?,filter: String?): LiveData<Resource<AppAlertsListResponse>> {
     return repository.appAlertsList(     order, page, filter,  )
   }



  fun appAlertsUpdate(isRead: Int?,id: String?): LiveData<Resource<AppAlertsUpdateResponse>> {
     return repository.appAlertsUpdate(  AppAlertsUpdateRequest(isRead),  id,   )
   }



  fun retrieveProductDefault(page: Number?,limit: Number?,sortid: String?,direction: String?): LiveData<Resource<RetrieveProductDefaultResponse>> {
     return repository.retrieveProductDefault(  RetrieveProductDefaultRequest(page,limit,sortid,direction),    )
   }



  fun ecomProductByIDDefault(): LiveData<Resource<EcomProductByIDDefaultResponse>> {
     return repository.ecomProductByIDDefault(      )
   }



  fun addEcomProductLambda(slug: String?,categoryId: String?,type: String?,quantity: String?,data: String?,name: String?,isTaxable: Boolean?,isShipping: Boolean?,isSticky: Boolean?,isFeatured: Boolean?,isDownloadable: Boolean?,downloadLimit: String?,isBackorder: Boolean?,soldSingle: Boolean?,manageStock: Boolean?,thumbnailImage: String?,featuredImage: Boolean?,image: String?,sku: String?,weight: String?,height: String?,length: String?,weightUnit: String?,heightUnit: String?,lengthUnit: String?,avgReview: String?,salePrice: String?,shippingPrice: String?,regularPrice: String?,position: String?,downloadExpireAt: String?,scheduleSaleAt: String?,scheduleSaleEnd: String?,description: String?,isVirtual: Boolean?): LiveData<Resource<AddEcomProductLambdaResponse>> {
     return repository.addEcomProductLambda(  AddEcomProductLambdaRequest(slug,categoryId,type,quantity,data,name,isTaxable,isShipping,isSticky,isFeatured,isDownloadable,downloadLimit,isBackorder,soldSingle,manageStock,thumbnailImage,featuredImage,image,sku,weight,height,length,weightUnit,heightUnit,lengthUnit,avgReview,salePrice,shippingPrice,regularPrice,position,downloadExpireAt,scheduleSaleAt,scheduleSaleEnd,description,isVirtual),    )
   }



  fun editEcomProductLambda(payload: Map<String, Any>,id: Number?): LiveData<Resource<EditEcomProductLambdaResponse>> {
     return repository.editEcomProductLambda(  EditEcomProductLambdaRequest(payload),  id,   )
   }



  fun deleteEcomProductLambda(id: Number?): LiveData<Resource<DeleteEcomProductLambdaResponse>> {
     return repository.deleteEcomProductLambda(    id,   )
   }



  fun getCartItems(userId: String?): LiveData<Resource<GetCartItemsResponse>> {
     return repository.getCartItems(     userId,  )
   }



  fun ecomAddCart(userId: String?,productid: Int?,quantity: Int?): LiveData<Resource<EcomAddCartResponse>> {
     return repository.ecomAddCart(  EcomAddCartRequest(userId,productid,quantity),    )
   }



  fun ecomDeleteCartItem(userId: String?,data: String?): LiveData<Resource<EcomDeleteCartItemResponse>> {
     return repository.ecomDeleteCartItem(     userId, data,  )
   }



  fun ecomGetProductReview(productid: Int?): LiveData<Resource<EcomGetProductReviewResponse>> {
     return repository.ecomGetProductReview(     productid,  )
   }



  fun ecomAddProductReview(review: String?,productid: Int?): LiveData<Resource<EcomAddProductReviewResponse>> {
     return repository.ecomAddProductReview(     review, productid,  )
   }



  fun forgotPassword(email: String?,role: String?): LiveData<Resource<ForgotPasswordResponse>> {
     return repository.forgotPassword(  ForgotPasswordRequest(email,role),    )
   }



  fun forgotPasswordMobile(email: String?): LiveData<Resource<ForgotPasswordMobileResponse>> {
     return repository.forgotPasswordMobile(  ForgotPasswordMobileRequest(email),    )
   }



  fun resetPassword(token: String?,code: String?,password: String?): LiveData<Resource<ResetPasswordResponse>> {
     return repository.resetPassword(  ResetPasswordRequest(token,code,password),    )
   }



  fun resetPasswordMobile(code: String?,password: String?): LiveData<Resource<ResetPasswordMobileResponse>> {
     return repository.resetPasswordMobile(  ResetPasswordMobileRequest(code,password),    )
   }



  fun getStripeData(userId: String?,amount: String?,currency: String?): LiveData<Resource<GetStripeDataResponse>> {
     return repository.getStripeData(  GetStripeDataRequest(userId,amount,currency),    )
   }



  fun getOneDefaultSquareFootCost(id: Int? = 0,join: String? = ""): LiveData<Resource<GetOneDefaultSquareFootCostResponse>> {
     return repository.getOneDefaultSquareFootCost(    id,  join,  )
   }



  fun getOneSetting(id: Int? = 0,join: String? = ""): LiveData<Resource<GetOneSettingResponse>> {
     return repository.getOneSetting(    id,  join,  )
   }



  fun getOneCost(id: Int? = 0,join: String? = ""): LiveData<Resource<GetOneCostResponse>> {
     return repository.getOneCost(    id,  join,  )
   }



  fun getOneRoom(id: Int? = 0,join: String? = ""): LiveData<Resource<GetOneRoomResponse>> {
     return repository.getOneRoom(    id,  join,  )
   }



  fun getOneLabor(id: Int? = 0,join: String? = ""): LiveData<Resource<GetOneLaborResponse>> {
     return repository.getOneLabor(    id,  join,  )
   }



  fun getOneLineItemEntry(id: Int? = 0,join: String? = ""): LiveData<Resource<GetOneLineItemEntryResponse>> {
     return repository.getOneLineItemEntry(    id,  join,  )
   }



  fun getOneCompanySettings(id: Int? = 0,join: String? = ""): LiveData<Resource<GetOneCompanySettingsResponse>> {
     return repository.getOneCompanySettings(    id,  join,  )
   }



  fun getOneCms(id: Int? = 0,join: String? = ""): LiveData<Resource<GetOneCmsResponse>> {
     return repository.getOneCms(    id,  join,  )
   }



  fun getOneTeamMember(id: Int? = 0,join: String? = ""): LiveData<Resource<GetOneTeamMemberResponse>> {
     return repository.getOneTeamMember(    id,  join,  )
   }



  fun getOneDefaultMaterial(id: Int? = 0,join: String? = ""): LiveData<Resource<GetOneDefaultMaterialResponse>> {
     return repository.getOneDefaultMaterial(    id,  join,  )
   }



  fun getOneProject(id: Int? = 0,join: String? = ""): LiveData<Resource<GetOneProjectResponse>> {
     return repository.getOneProject(    id,  join,  )
   }



  fun getOneUser(id: Int? = 0,join: String? = ""): LiveData<Resource<GetOneUserResponse>> {
     return repository.getOneUser(    id,  join,  )
   }



  fun getOneProfile(id: Int? = 0,join: String? = ""): LiveData<Resource<GetOneProfileResponse>> {
     return repository.getOneProfile(    id,  join,  )
   }



  fun getOneLinealFootCost(id: Int? = 0,join: String? = ""): LiveData<Resource<GetOneLinealFootCostResponse>> {
     return repository.getOneLinealFootCost(    id,  join,  )
   }



  fun getOneCustomer(id: Int? = 0,join: String? = ""): LiveData<Resource<GetOneCustomerResponse>> {
     return repository.getOneCustomer(    id,  join,  )
   }



  fun getOnePermission(id: Int? = 0,join: String? = ""): LiveData<Resource<GetOnePermissionResponse>> {
     return repository.getOnePermission(    id,  join,  )
   }



  fun getOneToken(id: Int? = 0,join: String? = ""): LiveData<Resource<GetOneTokenResponse>> {
     return repository.getOneToken(    id,  join,  )
   }



  fun getOneSqftCosts(id: Int? = 0,join: String? = ""): LiveData<Resource<GetOneSqftCostsResponse>> {
     return repository.getOneSqftCosts(    id,  join,  )
   }



  fun getOneEmail(id: Int? = 0,join: String? = ""): LiveData<Resource<GetOneEmailResponse>> {
     return repository.getOneEmail(    id,  join,  )
   }



  fun getOneAlerts(id: Int? = 0,join: String? = ""): LiveData<Resource<GetOneAlertsResponse>> {
     return repository.getOneAlerts(    id,  join,  )
   }



  fun getOneDraws(id: Int? = 0,join: String? = ""): LiveData<Resource<GetOneDrawsResponse>> {
     return repository.getOneDraws(    id,  join,  )
   }



  fun getOneChat(id: Int? = 0,join: String? = ""): LiveData<Resource<GetOneChatResponse>> {
     return repository.getOneChat(    id,  join,  )
   }



  fun getOneMaterial(id: Int? = 0,join: String? = ""): LiveData<Resource<GetOneMaterialResponse>> {
     return repository.getOneMaterial(    id,  join,  )
   }



  fun getOneInvoice(id: Int? = 0,join: String? = ""): LiveData<Resource<GetOneInvoiceResponse>> {
     return repository.getOneInvoice(    id,  join,  )
   }



  fun getOneDefaultLinealFootCost(id: Int? = 0,join: String? = ""): LiveData<Resource<GetOneDefaultLinealFootCostResponse>> {
     return repository.getOneDefaultLinealFootCost(    id,  join,  )
   }



  fun getOneTriggerType(id: Int? = 0,join: String? = ""): LiveData<Resource<GetOneTriggerTypeResponse>> {
     return repository.getOneTriggerType(    id,  join,  )
   }



  fun getOneJob(id: Int? = 0,join: String? = ""): LiveData<Resource<GetOneJobResponse>> {
     return repository.getOneJob(    id,  join,  )
   }



  fun getOneLineItems(id: Int? = 0,join: String? = ""): LiveData<Resource<GetOneLineItemsResponse>> {
     return repository.getOneLineItems(    id,  join,  )
   }



  fun getOnePhoto(id: Int? = 0,join: String? = ""): LiveData<Resource<GetOnePhotoResponse>> {
     return repository.getOnePhoto(    id,  join,  )
   }



  fun getOneApiKeys(id: Int? = 0,join: String? = ""): LiveData<Resource<GetOneApiKeysResponse>> {
     return repository.getOneApiKeys(    id,  join,  )
   }



  fun getOneChangeOrderDescription(id: Int? = 0,join: String? = ""): LiveData<Resource<GetOneChangeOrderDescriptionResponse>> {
     return repository.getOneChangeOrderDescription(    id,  join,  )
   }



  fun getOneAnalyticLog(id: Int? = 0,join: String? = ""): LiveData<Resource<GetOneAnalyticLogResponse>> {
     return repository.getOneAnalyticLog(    id,  join,  )
   }



  fun getOnePosts(id: Int? = 0,join: String? = ""): LiveData<Resource<GetOnePostsResponse>> {
     return repository.getOnePosts(    id,  join,  )
   }



  fun getOneEmployee(id: Int? = 0,join: String? = ""): LiveData<Resource<GetOneEmployeeResponse>> {
     return repository.getOneEmployee(    id,  join,  )
   }



  fun getDefaultSquareFootCostList(order: String? = "",size: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetDefaultSquareFootCostListResponse>> {
     return repository.getDefaultSquareFootCostList(     order, size, filter, join,  )
   }



  fun getSettingList(order: String? = "",size: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetSettingListResponse>> {
     return repository.getSettingList(     order, size, filter, join,  )
   }



  fun getCostList(order: String? = "",size: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetCostListResponse>> {
     return repository.getCostList(     order, size, filter, join,  )
   }



  fun getRoomList(order: String? = "",size: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetRoomListResponse>> {
     return repository.getRoomList(     order, size, filter, join,  )
   }



  fun getLaborList(order: String? = "",size: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetLaborListResponse>> {
     return repository.getLaborList(     order, size, filter, join,  )
   }



  fun getLineItemEntryList(order: String? = "",size: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetLineItemEntryListResponse>> {
     return repository.getLineItemEntryList(     order, size, filter, join,  )
   }



  fun getCompanySettingsList(order: String? = "",size: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetCompanySettingsListResponse>> {
     return repository.getCompanySettingsList(     order, size, filter, join,  )
   }



  fun getCmsList(order: String? = "",size: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetCmsListResponse>> {
     return repository.getCmsList(     order, size, filter, join,  )
   }



  fun getTeamMemberList(order: String? = "",size: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetTeamMemberListResponse>> {
     return repository.getTeamMemberList(     order, size, filter, join,  )
   }







  fun getProjectList(order: String? = "",size: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetProjectListResponse>> {
     return repository.getProjectList(     order, size, filter, join,  )
   }



  fun getUserList(order: String? = "",size: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetUserListResponse>> {
     return repository.getUserList(     order, size, filter, join,  )
   }



  fun getProfileList(order: String? = "",size: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetProfileListResponse>> {
     return repository.getProfileList(     order, size, filter, join,  )
   }



  fun getLinealFootCostList(order: String? = "",size: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetLinealFootCostListResponse>> {
     return repository.getLinealFootCostList(     order, size, filter, join,  )
   }



  fun getCustomerList(order: String? = "",size: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetCustomerListResponse>> {
     return repository.getCustomerList(     order, size, filter, join,  )
   }



  fun getPermissionList(order: String? = "",size: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetPermissionListResponse>> {
     return repository.getPermissionList(     order, size, filter, join,  )
   }



  fun getTokenList(order: String? = "",size: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetTokenListResponse>> {
     return repository.getTokenList(     order, size, filter, join,  )
   }



  fun getSqftCostsList(order: String? = "",size: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetSqftCostsListResponse>> {
     return repository.getSqftCostsList(     order, size, filter, join,  )
   }



  fun getEmailList(order: String? = "",size: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetEmailListResponse>> {
     return repository.getEmailList(     order, size, filter, join,  )
   }



  fun getAlertsList(order: String? = "",size: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetAlertsListResponse>> {
     return repository.getAlertsList(     order, size, filter, join,  )
   }



  fun getDrawsList(order: String? = "",size: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetDrawsListResponse>> {
     return repository.getDrawsList(     order, size, filter, join,  )
   }



  fun getChatList(order: String? = "",size: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetChatListResponse>> {
     return repository.getChatList(     order, size, filter, join,  )
   }



  fun getMaterialList(order: String? = "",size: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetMaterialListResponse>> {
     return repository.getMaterialList(     order, size, filter, join,  )
   }



  fun getInvoiceList(order: String? = "",size: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetInvoiceListResponse>> {
     return repository.getInvoiceList(     order, size, filter, join,  )
   }



  fun getDefaultLinealFootCostList(order: String? = "",size: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetDefaultLinealFootCostListResponse>> {
     return repository.getDefaultLinealFootCostList(     order, size, filter, join,  )
   }



  fun getTriggerTypeList(order: String? = "",size: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetTriggerTypeListResponse>> {
     return repository.getTriggerTypeList(     order, size, filter, join,  )
   }



  fun getJobList(order: String? = "",size: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetJobListResponse>> {
     return repository.getJobList(     order, size, filter, join,  )
   }



  fun getLineItemsList(order: String? = "",size: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetLineItemsListResponse>> {
     return repository.getLineItemsList(     order, size, filter, join,  )
   }



  fun getPhotoList(order: String? = "",size: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetPhotoListResponse>> {
     return repository.getPhotoList(     order, size, filter, join,  )
   }



  fun getApiKeysList(order: String? = "",size: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetApiKeysListResponse>> {
     return repository.getApiKeysList(     order, size, filter, join,  )
   }



  fun getChangeOrderDescriptionList(order: String? = "",size: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetChangeOrderDescriptionListResponse>> {
     return repository.getChangeOrderDescriptionList(     order, size, filter, join,  )
   }



  fun getAnalyticLogList(order: String? = "",size: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetAnalyticLogListResponse>> {
     return repository.getAnalyticLogList(     order, size, filter, join,  )
   }



  fun getPostsList(order: String? = "",size: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetPostsListResponse>> {
     return repository.getPostsList(     order, size, filter, join,  )
   }



  fun getEmployeeList(order: String? = "",size: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetEmployeeListResponse>> {
     return repository.getEmployeeList(     order, size, filter, join,  )
   }



  fun getDefaultSquareFootCostPaginated(order: String? = "",page: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetDefaultSquareFootCostPaginatedResponse>> {
     return repository.getDefaultSquareFootCostPaginated(     order, page, filter, join,  )
   }



  fun getSettingPaginated(order: String? = "",page: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetSettingPaginatedResponse>> {
     return repository.getSettingPaginated(     order, page, filter, join,  )
   }



  fun getCostPaginated(order: String? = "",page: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetCostPaginatedResponse>> {
     return repository.getCostPaginated(     order, page, filter, join,  )
   }



  fun getRoomPaginated(order: String? = "",page: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetRoomPaginatedResponse>> {
     return repository.getRoomPaginated(     order, page, filter, join,  )
   }



  fun getLaborPaginated(order: String? = "",page: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetLaborPaginatedResponse>> {
     return repository.getLaborPaginated(     order, page, filter, join,  )
   }



  fun getLineItemEntryPaginated(order: String? = "",page: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetLineItemEntryPaginatedResponse>> {
     return repository.getLineItemEntryPaginated(     order, page, filter, join,  )
   }



  fun getCompanySettingsPaginated(order: String? = "",page: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetCompanySettingsPaginatedResponse>> {
     return repository.getCompanySettingsPaginated(     order, page, filter, join,  )
   }



  fun getCmsPaginated(order: String? = "",page: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetCmsPaginatedResponse>> {
     return repository.getCmsPaginated(     order, page, filter, join,  )
   }



  fun getTeamMemberPaginated(order: String? = "",page: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetTeamMemberPaginatedResponse>> {
     return repository.getTeamMemberPaginated(     order, page, filter, join,  )
   }



  fun getDefaultMaterialPaginated(order: String? = "",page: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetDefaultMaterialPaginatedResponse>> {
     return repository.getDefaultMaterialPaginated(     order, page, filter, join,  )
   }



  fun getProjectPaginated(order: String? = "",page: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetProjectPaginatedResponse>> {
     return repository.getProjectPaginated(     order, page, filter, join,  )
   }



  fun getUserPaginated(order: String? = "",page: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetUserPaginatedResponse>> {
     return repository.getUserPaginated(     order, page, filter, join,  )
   }



  fun getProfilePaginated(order: String? = "",page: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetProfilePaginatedResponse>> {
     return repository.getProfilePaginated(     order, page, filter, join,  )
   }



  fun getLinealFootCostPaginated(order: String? = "",page: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetLinealFootCostPaginatedResponse>> {
     return repository.getLinealFootCostPaginated(     order, page, filter, join,  )
   }



  fun getCustomerPaginated(order: String? = "",page: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetCustomerPaginatedResponse>> {
     return repository.getCustomerPaginated(     order, page, filter, join,  )
   }



  fun getPermissionPaginated(order: String? = "",page: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetPermissionPaginatedResponse>> {
     return repository.getPermissionPaginated(     order, page, filter, join,  )
   }



  fun getTokenPaginated(order: String? = "",page: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetTokenPaginatedResponse>> {
     return repository.getTokenPaginated(     order, page, filter, join,  )
   }



  fun getSqftCostsPaginated(order: String? = "",page: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetSqftCostsPaginatedResponse>> {
     return repository.getSqftCostsPaginated(     order, page, filter, join,  )
   }



  fun getEmailPaginated(order: String? = "",page: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetEmailPaginatedResponse>> {
     return repository.getEmailPaginated(     order, page, filter, join,  )
   }



  fun getAlertsPaginated(order: String? = "",page: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetAlertsPaginatedResponse>> {
     return repository.getAlertsPaginated(     order, page, filter, join,  )
   }



  fun getDrawsPaginated(order: String? = "",page: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetDrawsPaginatedResponse>> {
     return repository.getDrawsPaginated(     order, page, filter, join,  )
   }



  fun getChatPaginated(order: String? = "",page: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetChatPaginatedResponse>> {
     return repository.getChatPaginated(     order, page, filter, join,  )
   }



  fun getMaterialPaginated(order: String? = "",page: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetMaterialPaginatedResponse>> {
     return repository.getMaterialPaginated(     order, page, filter, join,  )
   }



  fun getInvoicePaginated(order: String? = "",page: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetInvoicePaginatedResponse>> {
     return repository.getInvoicePaginated(     order, page, filter, join,  )
   }



  fun getDefaultLinealFootCostPaginated(order: String? = "",page: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetDefaultLinealFootCostPaginatedResponse>> {
     return repository.getDefaultLinealFootCostPaginated(     order, page, filter, join,  )
   }



  fun getTriggerTypePaginated(order: String? = "",page: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetTriggerTypePaginatedResponse>> {
     return repository.getTriggerTypePaginated(     order, page, filter, join,  )
   }



  fun getJobPaginated(order: String? = "",page: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetJobPaginatedResponse>> {
     return repository.getJobPaginated(     order, page, filter, join,  )
   }



  fun getLineItemsPaginated(order: String? = "",page: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetLineItemsPaginatedResponse>> {
     return repository.getLineItemsPaginated(     order, page, filter, join,  )
   }



  fun getPhotoPaginated(order: String? = "",page: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetPhotoPaginatedResponse>> {
     return repository.getPhotoPaginated(     order, page, filter, join,  )
     }



    fun getApiKeysPaginated(order: String? = "",page: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetApiKeysPaginatedResponse>> {
       return repository.getApiKeysPaginated(     order, page, filter, join,  )
     }



    fun getChangeOrderDescriptionPaginated(order: String? = "",page: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetChangeOrderDescriptionPaginatedResponse>> {
       return repository.getChangeOrderDescriptionPaginated(     order, page, filter, join,  )
     }



    fun getAnalyticLogPaginated(order: String? = "",page: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetAnalyticLogPaginatedResponse>> {
       return repository.getAnalyticLogPaginated(     order, page, filter, join,  )
     }



    fun getPostsPaginated(order: String? = "",page: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetPostsPaginatedResponse>> {
       return repository.getPostsPaginated(     order, page, filter, join,  )
     }



    fun getEmployeePaginated(order: String? = "",page: String? = "",filter: String? = "",join: String? = ""): LiveData<Resource<GetEmployeePaginatedResponse>> {
       return repository.getEmployeePaginated(     order, page, filter, join,  )
     }



    fun createDefaultSquareFootCost(name: String? = "",cost: String?,laborCost: String?,userId: Int? = 0,hidden: Int? = 0): LiveData<Resource<CreateDefaultSquareFootCostResponse>> {
       return repository.createDefaultSquareFootCost(  CreateDefaultSquareFootCostRequest(name,cost,laborCost,userId,hidden),    )
     }



    fun createSetting(settingKey: String? = "",settingValue: String? = ""): LiveData<Resource<CreateSettingResponse>> {
       return repository.createSetting(  CreateSettingRequest(settingKey,settingValue),    )
     }



    fun createCost(costType: String? = "",name: String? = "",unitCost: Int? = 0,linealFootCost: Int? = 0,laborCost: Int? = 0,materialCost: Int? = 0,profitOverhead: Int? = 0): LiveData<Resource<CreateCostResponse>> {
       return repository.createCost(  CreateCostRequest(costType,name,unitCost,linealFootCost,laborCost,materialCost,profitOverhead),    )
     }



    fun createRoom(userId: Int? = 0,otherUserId: Int? = 0,chatId: Int? = 0,unread: Int? = 0,userUpdateAt: String? = "",otherUserUpdateAt: String? = ""): LiveData<Resource<CreateRoomResponse>> {
       return repository.createRoom(  CreateRoomRequest(userId,otherUserId,chatId,unread,userUpdateAt,otherUserUpdateAt),    )
     }



    fun createLabor(name: String? = "",hours: String? = "",amount: Int? = 0,perHour: Int? = 0): LiveData<Resource<CreateLaborResponse>> {
       return repository.createLabor(  CreateLaborRequest(name,hours,amount,perHour),    )
     }



    fun createLineItemEntry(parentId: Int? = 0,name: String? = "",cost: String?,laborCost: String?,lineItemId: Int? = 0,quantity: String?): LiveData<Resource<CreateLineItemEntryResponse>> {
       return repository.createLineItemEntry(  CreateLineItemEntryRequest(parentId,name,cost,laborCost,lineItemId,quantity),    )
     }



    fun createCompanySettings(defaultHourlyRate: String?,defaultProfitOverhead: String?,userId: Int? = 0): LiveData<Resource<CreateCompanySettingsResponse>> {
       return repository.createCompanySettings(  CreateCompanySettingsRequest(defaultHourlyRate,defaultProfitOverhead,userId),    )
     }



    fun createCms(page: String? = "",contentKey: String? = "",contentType: String? = "",contentValue: String? = ""): LiveData<Resource<CreateCmsResponse>> {
       return repository.createCms(  CreateCmsRequest(page,contentKey,contentType,contentValue),    )
     }



    fun createTeamMember(projectId: Int? = 0,userId: Int? = 0,name: String? = "",hourlyRate: Int? = 0,isDefault: Int? = 0): LiveData<Resource<CreateTeamMemberResponse>> {
       return repository.createTeamMember(  CreateTeamMemberRequest(projectId,userId,name,hourlyRate,isDefault),    )
     }


    fun createDraw(projectId: Int? = 0,amount: String? = "",description: String? = "" ,check_no: String? = "",status: Int? = 0): LiveData<Resource<CommonResponse>> {
       return repository.createDraw(  CreateDrawRequest(amount=amount,description=description,check_no=check_no,status=status),    )
     }
//    projectId=projectId


    fun createProject(changeCount: Int? = 0,customerId: Int? = 0,userId: Int? = 0,status: Int? = 0,profitOverhead: String?,hourlyRate: String?): LiveData<Resource<CreateProjectResponse>> {
       return repository.createProject(  CreateProjectRequest(changeCount,customerId,userId,status,profitOverhead,hourlyRate),    )
     }



    fun createUser(oauth: String? = "",role: String? = "",firstName: String? = "",lastName: String? = "",email: String? = "",password: String? = "",type: Int? = 0,verify: Int? = 0,phone: String? = "",companyName: String? = "",photo: String? = "",refer: String? = "",stripeUid: String? = "",paypalUid: String? = "",twoFactorAuthentication: Int? = 0,status: Int? = 0): LiveData<Resource<CreateUserResponse>> {
       return repository.createUser(  CreateUserRequest(oauth,role,firstName,lastName,email,password,type,verify,phone,companyName,photo,refer,stripeUid,paypalUid,twoFactorAuthentication,status),    )
     }



    fun createProfile(userId: Int? = 0,fcmToken: String? = "",deviceId: String? = "",deviceType: String? = ""): LiveData<Resource<CreateProfileResponse>> {
       return repository.createProfile(  CreateProfileRequest(userId,fcmToken,deviceId,deviceType),    )
     }



    fun createLinealFootCost(name: String? = "",linealFootCost: Int? = 0,profitOverhead: Int? = 0,laborCost: Int? = 0,materialCost: Int? = 0): LiveData<Resource<CreateLinealFootCostResponse>> {
       return repository.createLinealFootCost(  CreateLinealFootCostRequest(name,linealFootCost,profitOverhead,laborCost,materialCost),    )
     }







    fun createPermission(role: String? = "",permission: String? = ""): LiveData<Resource<CreatePermissionResponse>> {
       return repository.createPermission(  CreatePermissionRequest(role,permission),    )
     }



    fun createToken(token: String? = "",type: Int? = 0,data: String? = "",userId: Int? = 0,status: Int? = 0,expireAt: String? = ""): LiveData<Resource<CreateTokenResponse>> {
       return repository.createToken(  CreateTokenRequest(token,type,data,userId,status,expireAt),    )
     }



    fun createSqftCosts(name: String? = "",linealFootCost: Int? = 0,profitOverhead: Int? = 0,laborCost: Int? = 0,materialCost: Int? = 0): LiveData<Resource<CreateSqftCostsResponse>> {
       return repository.createSqftCosts(  CreateSqftCostsRequest(name,linealFootCost,profitOverhead,laborCost,materialCost),    )
     }



    fun createEmail(slug: String? = "",subject: String? = "",tag: String? = "",html: String? = ""): LiveData<Resource<CreateEmailResponse>> {
       return repository.createEmail(  CreateEmailRequest(slug,subject,tag,html),    )
     }



    fun createAlerts(userId: Int? = 0,message: String? = "",image: String? = "",isRead: Int? = 0): LiveData<Resource<CreateAlertsResponse>> {
       return repository.createAlerts(  CreateAlertsRequest(userId,message,image,isRead),    )
     }




    fun createChat(roomId: Int? = 0,unread: Int? = 0,chat: String? = ""): LiveData<Resource<CreateChatResponse>> {
       return repository.createChat(  CreateChatRequest(roomId,unread,chat),    )
     }



    fun createMaterial(name: String? = "",unitCost: Int? = 0,projectId: String? = ""): LiveData<Resource<CreateMaterialResponse>> {
       return repository.createMaterial(  CreateMaterialRequest(name,unitCost,projectId),    )
     }



    fun createInvoice(companyName: String? = "",name: String? = "",email: String? = "",address: String? = "",milestoneDescription: String? = "",totalAmountDue: String? = ""): LiveData<Resource<CreateInvoiceResponse>> {
       return repository.createInvoice(  CreateInvoiceRequest(companyName,name,email,address,milestoneDescription,totalAmountDue),    )
     }



    fun createDefaultLinealFootCost(name: String? = "",cost: String?,laborCost: String?,userId: Int? = 0,hidden: Int? = 0): LiveData<Resource<CreateDefaultLinealFootCostResponse>> {
       return repository.createDefaultLinealFootCost(  CreateDefaultLinealFootCostRequest(name,cost,laborCost,userId,hidden),    )
     }



    fun createTriggerType(name: String? = ""): LiveData<Resource<CreateTriggerTypeResponse>> {
       return repository.createTriggerType(  CreateTriggerTypeRequest(name),    )
     }



    fun createJob(task: String? = "",arguments: String? = "",errorLog: String? = "",identifier: String? = "",retries: Int? = 0,retryCount: Int? = 0,timeInterval: String? = "",lastRun: String? = "",status: String? = ""): LiveData<Resource<CreateJobResponse>> {
       return repository.createJob(  CreateJobRequest(task,arguments,errorLog,identifier,retries,retryCount,timeInterval,lastRun,status),    )
     }



    fun createLineItems(hidden: Int? = 0,projectId: Int? = 0,description: String? = "",estimatedBy: String? = "",laborHours: String?): LiveData<Resource<CreateLineItemsResponse>> {
       return repository.createLineItems(  CreateLineItemsRequest(hidden,projectId,description,estimatedBy,laborHours),    )
     }



    fun createPhoto(url: String? = "",caption: String? = "",userId: Int? = 0,width: Int? = 0,height: Int? = 0,type: Int? = 0): LiveData<Resource<CreatePhotoResponse>> {
       return repository.createPhoto(  CreatePhotoRequest(url,caption,userId,width,height,type),    )
     }



    fun createApiKeys(key: String? = "",value: String? = "",description: String? = ""): LiveData<Resource<CreateApiKeysResponse>> {
       return repository.createApiKeys(  CreateApiKeysRequest(key,value,description),    )
     }



    fun createChangeOrderDescription(description: String? = "",lineItemId: Int? = 0): LiveData<Resource<CreateChangeOrderDescriptionResponse>> {
       return repository.createChangeOrderDescription(  CreateChangeOrderDescriptionRequest(description,lineItemId),    )
     }



    fun createAnalyticLog(userId: Int? = 0,url: String? = "",path: String? = "",hostname: String? = "",ip: String? = "",role: String? = "",browser: String? = "",country: String? = ""): LiveData<Resource<CreateAnalyticLogResponse>> {
       return repository.createAnalyticLog(  CreateAnalyticLogRequest(userId,url,path,hostname,ip,role,browser,country),    )
     }



    fun createPosts(status: Int? = 0,type: String? = "",data: String? = "",links: String? = ""): LiveData<Resource<CreatePostsResponse>> {
       return repository.createPosts(  CreatePostsRequest(status,type,data,links),    )
     }



    fun createEmployee(userId: Int? = 0,defaultHourlyRate: Int? = 0,defaultProfitOverhead: Int? = 0): LiveData<Resource<CreateEmployeeResponse>> {
       return repository.createEmployee(  CreateEmployeeRequest(userId,defaultHourlyRate,defaultProfitOverhead),    )
     }



    fun updateDefaultSquareFootCost(name: String? = "",cost: String?,laborCost: String?,userId: Int? = 0,hidden: Int? = 0,id: Int? = 0): LiveData<Resource<UpdateDefaultSquareFootCostResponse>> {
       return repository.updateDefaultSquareFootCost(  UpdateDefaultSquareFootCostRequest(name,cost,laborCost,userId,hidden),  id,   )
     }



    fun updateSetting(settingKey: String? = "",settingValue: String? = "",id: Int? = 0): LiveData<Resource<UpdateSettingResponse>> {
       return repository.updateSetting(  UpdateSettingRequest(settingKey,settingValue),  id,   )
     }



    fun updateCost(costType: String? = "",name: String? = "",unitCost: Int? = 0,linealFootCost: Int? = 0,laborCost: Int? = 0,materialCost: Int? = 0,profitOverhead: Int? = 0,id: Int? = 0): LiveData<Resource<UpdateCostResponse>> {
       return repository.updateCost(  UpdateCostRequest(costType,name,unitCost,linealFootCost,laborCost,materialCost,profitOverhead),  id,   )
     }



    fun updateRoom(userId: Int? = 0,otherUserId: Int? = 0,chatId: Int? = 0,unread: Int? = 0,userUpdateAt: String? = "",otherUserUpdateAt: String? = "",id: Int? = 0): LiveData<Resource<UpdateRoomResponse>> {
       return repository.updateRoom(  UpdateRoomRequest(userId,otherUserId,chatId,unread,userUpdateAt,otherUserUpdateAt),  id,   )
     }



    fun updateLabor(name: String? = "",hours: String? = "",amount: Int? = 0,perHour: Int? = 0,id: Int? = 0): LiveData<Resource<UpdateLaborResponse>> {
       return repository.updateLabor(  UpdateLaborRequest(name,hours,amount,perHour),  id,   )
     }



    fun updateLineItemEntry(parentId: Int? = 0,name: String? = "",cost: String?,laborCost: String?,lineItemId: Int? = 0,quantity: String?,id: Int? = 0): LiveData<Resource<UpdateLineItemEntryResponse>> {
       return repository.updateLineItemEntry(  UpdateLineItemEntryRequest(parentId,name,cost,laborCost,lineItemId,quantity),  id,   )
     }



    fun updateCompanySettings(defaultHourlyRate: String?,defaultProfitOverhead: String?,userId: Int? = 0,id: Int? = 0): LiveData<Resource<UpdateCompanySettingsResponse>> {
       return repository.updateCompanySettings(  UpdateCompanySettingsRequest(defaultHourlyRate,defaultProfitOverhead,userId),  id,   )
     }



    fun updateCms(page: String? = "",contentKey: String? = "",contentType: String? = "",contentValue: String? = "",id: Int? = 0): LiveData<Resource<UpdateCmsResponse>> {
       return repository.updateCms(  UpdateCmsRequest(page,contentKey,contentType,contentValue),  id,   )
     }



    fun updateTeamMember(projectId: Int? = 0,userId: Int? = 0,name: String? = "",hourlyRate: Int? = 0,isDefault: Int? = 0,id: Int? = 0): LiveData<Resource<UpdateTeamMemberResponse>> {
       return repository.updateTeamMember(  UpdateTeamMemberRequest(projectId,userId,name,hourlyRate,isDefault),  id,   )
     }



    fun updateDefaultMaterial(userId: String? = "",cost: String? = "",name: String? = "",hidden: Int? = 0,id: Int? = 0): LiveData<Resource<UpdateDefaultMaterialResponse>> {
       return repository.updateDefaultMaterial(  UpdateDefaultMaterialRequest(userId,cost,name,hidden),  id,   )
     }

    fun updateLinealFootCost(cost: Int = 0,name: String? = "",hidden: Int? = 0,id: Int? = 0,laborCost: Int, is_default : Int?): LiveData<Resource<CommonResponse>> {
       return repository.updateLinealFootCost(  LinearFootReqModel(name,cost,hidden,laborCost,is_default),  id,   )
     }

    fun updateSquareFootCost(cost: Int = 0,name: String? = "",hidden: Int? = 0,id: Int? = 0,laborCost: Int, is_default : Int?): LiveData<Resource<CommonResponse>> {
       return repository.updateSquareFootCost(  LinearFootReqModel(name,cost,hidden,laborCost,is_default),  id,   )
     }


//    fun updateProject(changeCount: Int? = 0,customerId: Int? = 0,userId: Int? = 0,status: Int? = 0,profitOverhead: String?,hourlyRate: String?,id: Int? = 0): LiveData<Resource<UpdateProjectResponse>> {
//       return repository.updateProject(  UpdateProjectRequest(changeCount,customerId,userId,status,profitOverhead,hourlyRate),  id,   )
//     }

      fun updateProject(changeCount: Int? = 0,customerId: Int? = 0,userId: Int? = 0,status: Int? = 0,profitOverhead: String?,hourlyRate: String?,id: Int? = 0): LiveData<Resource<UpdateProjectResponse>> {
          return repository.updateProject(  UpdateProjectRequest(changeCount,status),  id,   )
      }



    fun updateUser(oauth: String? = "",role: String? = "",firstName: String? = "",lastName: String? = "",email: String? = "",password: String? = "",type: Int? = 0,verify: Int? = 0,phone: String? = "",companyName: String? = "",photo: String? = "",refer: String? = "",stripeUid: String? = "",paypalUid: String? = "",twoFactorAuthentication: Int? = 0,status: Int? = 0,id: Int? = 0): LiveData<Resource<UpdateUserResponse>> {
       return repository.updateUser(  UpdateUserRequest(oauth,role,firstName,lastName,email,password,type,verify,phone,companyName,photo,refer,stripeUid,paypalUid,twoFactorAuthentication,status),  id,   )
     }



    fun updateProfile(userId: Int? = 0,fcmToken: String? = "",deviceId: String? = "",deviceType: String? = "",id: Int? = 0): LiveData<Resource<UpdateProfileResponse>> {
       return repository.updateProfile(  UpdateProfileRequest(userId,fcmToken,deviceId,deviceType),  id,   )
     }



    fun updateLinealFootCost(name: String? = "",linealFootCost: Int? = 0,profitOverhead: Int? = 0,laborCost: Int? = 0,materialCost: Int? = 0,id: Int? = 0): LiveData<Resource<UpdateLinealFootCostResponse>> {
       return repository.updateLinealFootCost(  UpdateLinealFootCostRequest(name,linealFootCost,profitOverhead,laborCost,materialCost),  id,   )
     }







    fun updatePermission(role: String? = "",permission: String? = "",id: Int? = 0): LiveData<Resource<UpdatePermissionResponse>> {
       return repository.updatePermission(  UpdatePermissionRequest(role,permission),  id,   )
     }



    fun updateToken(token: String? = "",type: Int? = 0,data: String? = "",userId: Int? = 0,status: Int? = 0,expireAt: String? = "",id: Int? = 0): LiveData<Resource<UpdateTokenResponse>> {
       return repository.updateToken(  UpdateTokenRequest(token,type,data,userId,status,expireAt),  id,   )
     }



    fun updateSqftCosts(name: String? = "",linealFootCost: Int? = 0,profitOverhead: Int? = 0,laborCost: Int? = 0,materialCost: Int? = 0,id: Int? = 0): LiveData<Resource<UpdateSqftCostsResponse>> {
       return repository.updateSqftCosts(  UpdateSqftCostsRequest(name,linealFootCost,profitOverhead,laborCost,materialCost),  id,   )
     }



    fun updateEmail(slug: String? = "",subject: String? = "",tag: String? = "",html: String? = "",id: Int? = 0): LiveData<Resource<UpdateEmailResponse>> {
       return repository.updateEmail(  UpdateEmailRequest(slug,subject,tag,html),  id,   )
     }



    fun updateAlerts(userId: Int? = 0,message: String? = "",image: String? = "",isRead: Int? = 0,id: Int? = 0): LiveData<Resource<UpdateAlertsResponse>> {
       return repository.updateAlerts(  UpdateAlertsRequest(userId,message,image,isRead),  id,   )
     }



    fun updateDraws(checkNo: String? = "",amount: String?,percentage: String?,description: String? = "",status: Int? = 0,projectId: Int? = 0,paymentType: String? = "",id: Int? = 0): LiveData<Resource<UpdateDrawsResponse>> {
       return repository.updateDraws(  UpdateDrawsRequest(checkNo,amount,percentage,description,status,projectId,paymentType),  id,   )
     }



    fun updateChat(roomId: Int? = 0,unread: Int? = 0,chat: String? = "",id: Int? = 0): LiveData<Resource<UpdateChatResponse>> {
       return repository.updateChat(  UpdateChatRequest(roomId,unread,chat),  id,   )
     }



    fun updateMaterial(name: String? = "",unitCost: Int? = 0,projectId: String? = "",id: Int? = 0): LiveData<Resource<UpdateMaterialResponse>> {
       return repository.updateMaterial(  UpdateMaterialRequest(name,unitCost,projectId),  id,   )
     }



    fun updateInvoice(companyName: String? = "",name: String? = "",email: String? = "",address: String? = "",milestoneDescription: String? = "",totalAmountDue: String? = "",id: Int? = 0): LiveData<Resource<UpdateInvoiceResponse>> {
       return repository.updateInvoice(  UpdateInvoiceRequest(companyName,name,email,address,milestoneDescription,totalAmountDue),  id,   )
     }



    fun updateDefaultLinealFootCost(name: String? = "",cost: String?,laborCost: String?,userId: Int? = 0,hidden: Int? = 0,id: Int? = 0): LiveData<Resource<UpdateDefaultLinealFootCostResponse>> {
       return repository.updateDefaultLinealFootCost(  UpdateDefaultLinealFootCostRequest(name,cost,laborCost,userId,hidden),  id,   )
     }



    fun updateTriggerType(name: String? = "",id: Int? = 0): LiveData<Resource<UpdateTriggerTypeResponse>> {
       return repository.updateTriggerType(  UpdateTriggerTypeRequest(name),  id,   )
     }



    fun updateJob(task: String? = "",arguments: String? = "",errorLog: String? = "",identifier: String? = "",retries: Int? = 0,retryCount: Int? = 0,timeInterval: String? = "",lastRun: String? = "",status: String? = "",id: Int? = 0): LiveData<Resource<UpdateJobResponse>> {
       return repository.updateJob(  UpdateJobRequest(task,arguments,errorLog,identifier,retries,retryCount,timeInterval,lastRun,status),  id,   )
     }



    fun updateLineItems(hidden: Int? = 0,projectId: Int? = 0,description: String? = "",estimatedBy: String? = "",laborHours: String?,id: Int? = 0): LiveData<Resource<UpdateLineItemsResponse>> {
       return repository.updateLineItems(  UpdateLineItemsRequest(hidden,projectId,description,estimatedBy,laborHours),  id,   )
     }



    fun updatePhoto(url: String? = "",caption: String? = "",userId: Int? = 0,width: Int? = 0,height: Int? = 0,type: Int? = 0,id: Int? = 0): LiveData<Resource<UpdatePhotoResponse>> {
       return repository.updatePhoto(  UpdatePhotoRequest(url,caption,userId,width,height,type),  id,   )
     }



    fun updateApiKeys(key: String? = "",value: String? = "",description: String? = "",id: Int? = 0): LiveData<Resource<UpdateApiKeysResponse>> {
       return repository.updateApiKeys(  UpdateApiKeysRequest(key,value,description),  id,   )
     }



    fun updateChangeOrderDescription(description: String? = "",lineItemId: Int? = 0,id: Int? = 0): LiveData<Resource<UpdateChangeOrderDescriptionResponse>> {
       return repository.updateChangeOrderDescription(  UpdateChangeOrderDescriptionRequest(description,lineItemId),  id,   )
     }



    fun updateAnalyticLog(userId: Int? = 0,url: String? = "",path: String? = "",hostname: String? = "",ip: String? = "",role: String? = "",browser: String? = "",country: String? = "",id: Int? = 0): LiveData<Resource<UpdateAnalyticLogResponse>> {
       return repository.updateAnalyticLog(  UpdateAnalyticLogRequest(userId,url,path,hostname,ip,role,browser,country),  id,   )
     }



    fun updatePosts(status: Int? = 0,type: String? = "",data: String? = "",links: String? = "",id: Int? = 0): LiveData<Resource<UpdatePostsResponse>> {
       return repository.updatePosts(  UpdatePostsRequest(status,type,data,links),  id,   )
     }



    fun updateEmployee(userId: Int? = 0,defaultHourlyRate: Int? = 0,defaultProfitOverhead: Int? = 0,id: Int? = 0): LiveData<Resource<UpdateEmployeeResponse>> {
       return repository.updateEmployee(  UpdateEmployeeRequest(userId,defaultHourlyRate,defaultProfitOverhead),  id,   )
     }



    fun deleteDefaultSquareFootCost(id: Int? = 0): LiveData<Resource<DeleteDefaultSquareFootCostResponse>> {
       return repository.deleteDefaultSquareFootCost(    id,   )
     }



    fun deleteSetting(id: Int? = 0): LiveData<Resource<DeleteSettingResponse>> {
       return repository.deleteSetting(    id,   )
     }



    fun deleteCost(id: Int? = 0): LiveData<Resource<DeleteCostResponse>> {
       return repository.deleteCost(    id,   )
     }



    fun deleteRoom(id: Int? = 0): LiveData<Resource<DeleteRoomResponse>> {
       return repository.deleteRoom(    id,   )
     }



    fun deleteLabor(id: Int? = 0): LiveData<Resource<DeleteLaborResponse>> {
       return repository.deleteLabor(    id,   )
     }



    fun deleteLineItemEntry(id: Int? = 0): LiveData<Resource<DeleteLineItemEntryResponse>> {
       return repository.deleteLineItemEntry(    id,   )
     }



    fun deleteCompanySettings(id: Int? = 0): LiveData<Resource<DeleteCompanySettingsResponse>> {
       return repository.deleteCompanySettings(    id,   )
     }



    fun deleteCms(id: Int? = 0): LiveData<Resource<DeleteCmsResponse>> {
       return repository.deleteCms(    id,   )
     }



    fun deleteTeamMember(id: Int? = 0): LiveData<Resource<DeleteTeamMemberResponse>> {
       return repository.deleteTeamMember(    id,   )
     }



    fun deleteDefaultMaterial(id: Int? = 0): LiveData<Resource<DeleteDefaultMaterialResponse>> {
       return repository.deleteDefaultMaterial(    id,   )
     }


    fun deleteLinealFootCosts(id: Int? = 0): LiveData<Resource<CommonResponse>> {
       return repository.deleteLinealFootCosts(    id,   )
     }

      fun deleteSquareFootCost(id: Int? = 0): LiveData<Resource<CommonResponse>> {
          return repository.deleteSquareFootCost(    id,   )
      }




    fun deleteProject(id: Int? = 0): LiveData<Resource<DeleteProjectResponse>> {
       return repository.deleteProject(    id,   )
     }



    fun deleteUser(id: Int? = 0): LiveData<Resource<DeleteUserResponse>> {
       return repository.deleteUser(    id,   )
     }



    fun deleteProfile(id: Int? = 0): LiveData<Resource<DeleteProfileResponse>> {
       return repository.deleteProfile(    id,   )
     }



    fun deleteLinealFootCost(id: Int? = 0): LiveData<Resource<DeleteLinealFootCostResponse>> {
       return repository.deleteLinealFootCost(    id,   )
     }



    fun deleteCustomer(id: Int? = 0): LiveData<Resource<DeleteCustomerResponse>> {
       return repository.deleteCustomer(    id,   )
     }



    fun deletePermission(id: Int? = 0): LiveData<Resource<DeletePermissionResponse>> {
       return repository.deletePermission(    id,   )
     }



    fun deleteToken(id: Int? = 0): LiveData<Resource<DeleteTokenResponse>> {
       return repository.deleteToken(    id,   )
     }



    fun deleteSqftCosts(id: Int? = 0): LiveData<Resource<DeleteSqftCostsResponse>> {
       return repository.deleteSqftCosts(    id,   )
     }



    fun deleteEmail(id: Int? = 0): LiveData<Resource<DeleteEmailResponse>> {
       return repository.deleteEmail(    id,   )
     }



    fun deleteAlerts(id: Int? = 0): LiveData<Resource<DeleteAlertsResponse>> {
       return repository.deleteAlerts(    id,   )
     }







    fun deleteChat(id: Int? = 0): LiveData<Resource<DeleteChatResponse>> {
       return repository.deleteChat(    id,   )
     }



    fun deleteMaterial(id: Int? = 0): LiveData<Resource<DeleteMaterialResponse>> {
       return repository.deleteMaterial(    id,   )
     }



    fun deleteInvoice(id: Int? = 0): LiveData<Resource<DeleteInvoiceResponse>> {
       return repository.deleteInvoice(    id,   )
     }



    fun deleteDefaultLinealFootCost(id: Int? = 0): LiveData<Resource<DeleteDefaultLinealFootCostResponse>> {
       return repository.deleteDefaultLinealFootCost(    id,   )
     }



    fun deleteTriggerType(id: Int? = 0): LiveData<Resource<DeleteTriggerTypeResponse>> {
       return repository.deleteTriggerType(    id,   )
     }



    fun deleteJob(id: Int? = 0): LiveData<Resource<DeleteJobResponse>> {
       return repository.deleteJob(    id,   )
     }







    fun deletePhoto(id: Int? = 0): LiveData<Resource<DeletePhotoResponse>> {
       return repository.deletePhoto(    id,   )
     }



    fun deleteApiKeys(id: Int? = 0): LiveData<Resource<DeleteApiKeysResponse>> {
       return repository.deleteApiKeys(    id,   )
     }



    fun deleteChangeOrderDescription(id: Int? = 0): LiveData<Resource<DeleteChangeOrderDescriptionResponse>> {
       return repository.deleteChangeOrderDescription(    id,   )
     }



    fun deleteAnalyticLog(id: Int? = 0): LiveData<Resource<DeleteAnalyticLogResponse>> {
       return repository.deleteAnalyticLog(    id,   )
     }



    fun deletePosts(id: Int? = 0): LiveData<Resource<DeletePostsResponse>> {
       return repository.deletePosts(    id,   )
     }



    fun deleteEmployee(id: Int? = 0): LiveData<Resource<DeleteEmployeeResponse>> {
       return repository.deleteEmployee(    id,   )
     }


    // Subscription API functions - Following fetchProjects pattern
    private val _userSubscriptionsResource = MutableLiveData<Resource<GetUserSubscriptionsResponse>>()
    val userSubscriptionsResource: LiveData<Resource<GetUserSubscriptionsResponse>> = _userSubscriptionsResource

    private val _plansResource = MutableLiveData<Resource<GetPlansResponse>>()
    val plansResource: LiveData<Resource<GetPlansResponse>> = _plansResource

    private val _paymentHistoryResource = MutableLiveData<Resource<GetPaymentHistoryResponse>>()
    val paymentHistoryResource: LiveData<Resource<GetPaymentHistoryResponse>> = _paymentHistoryResource

    private val _cancelSubscriptionResponse = MutableLiveData<Resource<CancelSubscriptionResponse>>()
    val cancelSubscriptionResponse: LiveData<Resource<CancelSubscriptionResponse>> = _cancelSubscriptionResponse

    fun setUserIdForSubscriptions(newUserId: Int) {
        if (_userId.value != newUserId) {
            _userId.value = newUserId
        }
        fetchUserSubscriptions(newUserId)
        fetchPaymentHistory(newUserId)
    }

    fun fetchPlans() {
        _plansResource.value = Resource.loading(null)
        viewModelScope.launch {
            try {
                repository.getPlans().observeForever { response ->
                    _plansResource.postValue(response)
                }
            } catch (e: Exception) {
                _plansResource.postValue(Resource.error(e.message ?: "Error fetching plans", null, 400))
            }
        }
    }

    private fun fetchUserSubscriptions(userId: Int) {
        _userSubscriptionsResource.value = Resource.loading(null)
        viewModelScope.launch {
            try {
                repository.getUserSubscriptions(userId).observeForever { response ->
                    _userSubscriptionsResource.postValue(response)
                }
            } catch (e: Exception) {
                _userSubscriptionsResource.postValue(Resource.error(e.message ?: "Error fetching subscriptions", null, 400))
            }
        }
    }

    private fun fetchPaymentHistory(userId: Int) {
        _paymentHistoryResource.value = Resource.loading(null)
        viewModelScope.launch {
            try {
                repository.getPaymentHistory(userId).observeForever { response ->
                    _paymentHistoryResource.postValue(response)
                }
            } catch (e: Exception) {
                _paymentHistoryResource.postValue(Resource.error(e.message ?: "Error fetching payment history", null, 400))
            }
        }
    }

    fun createSubscription(userId: Int, status: Int, cardCharged: String, amount: String): LiveData<Resource<CreateSubscriptionResponse>> {
        return repository.createSubscription(CreateSubscriptionRequest(userId, status, cardCharged, amount))
    }

    fun getPlans(): LiveData<Resource<GetPlansResponse>> {
        return repository.getPlans()
    }

    fun getUserSubscriptions(userId: Int): LiveData<Resource<GetUserSubscriptionsResponse>> {
        return repository.getUserSubscriptions(userId)
    }

    fun cancelSubscription(subscriptionId: Int, reason: String): LiveData<Resource<CancelSubscriptionResponse>> {
        viewModelScope.launch {
            _cancelSubscriptionResponse.postValue(Resource.loading(null))
            repository.cancelSubscription(CancelSubscriptionRequest(subscriptionId, reason)).observeForever {
                _cancelSubscriptionResponse.postValue(it)
            }
        }
        return _cancelSubscriptionResponse
    }

    fun getPaymentHistory(userId: Int): LiveData<Resource<GetPaymentHistoryResponse>> {
        return repository.getPaymentHistory(userId)
    }

    fun getSubscriptionStatus(subscriptionId: Int): LiveData<Resource<GetSubscriptionStatusResponse>> {
        return repository.getSubscriptionStatus(subscriptionId)
    }

  }
