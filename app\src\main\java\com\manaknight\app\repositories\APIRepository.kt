package com.manaknight.app.repositories

import androidx.lifecycle.LiveData
import androidx.lifecycle.liveData
import com.manaknight.app.model.remote.*
import com.manaknight.app.model.remote.profitPro.AllLineItemsResponseModel
import com.manaknight.app.model.remote.profitPro.CreateLineItemReqModel
import com.manaknight.app.model.remote.profitPro.CommonResponse
import com.manaknight.app.model.remote.profitPro.CompanyRequest
import com.manaknight.app.model.remote.profitPro.CreatePercentageDrawRequest
import com.manaknight.app.model.remote.profitPro.CreatePriceDrawRequest
import com.manaknight.app.model.remote.profitPro.MaterialReqModel
import com.manaknight.app.model.remote.profitPro.CustomerModel
import com.manaknight.app.model.remote.profitPro.CustomerResponseModel
import com.manaknight.app.model.remote.profitPro.DefaultModel
import com.manaknight.app.model.remote.profitPro.DrawInfoRespModel
import com.manaknight.app.model.remote.profitPro.LinearFootReqModel
import com.manaknight.app.model.remote.profitPro.LinearResponseModel
import com.manaknight.app.model.remote.profitPro.MaterialRequestModel
import com.manaknight.app.model.remote.profitPro.MaterialResponseModel
import com.manaknight.app.model.remote.profitPro.ProjectModel
import com.manaknight.app.model.remote.profitPro.ProjectTrackingResponse
import com.manaknight.app.model.remote.profitPro.SendInvoiceRequest
import com.manaknight.app.model.remote.profitPro.UpdateDrawRequest
import com.manaknight.app.model.remote.profitPro.UpdateDrawRequest2
import com.manaknight.app.model.remote.profitPro.UpdateLineItemReqModel
import com.manaknight.app.network.Resource
import com.manaknight.app.network.RemoteDataSource
import kotlinx.coroutines.Dispatchers
import okhttp3.MultipartBody

class APIRepository(
    private val remoteDataSource: RemoteDataSource,
    //private val preferences: AppPreferences
) {

    fun signupCompanySeup(request: CompanyRequest): LiveData<Resource<CommonResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.signupCompanySeup(request)
            emit(response)
    }

    fun updateCompanyDefault(request: DefaultModel): LiveData<Resource<CommonResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.updateCompanyDefault(request)
            emit(response)
    }

    fun createMaterial(request: MaterialRequestModel): LiveData<Resource<CommonResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.createMaterial(request)
            emit(response)
        }

    fun updateMaterial( request: MaterialRequestModel, id: Int?): LiveData<Resource<CommonResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.updateMaterial(request, id)
            emit(response)
        }


    fun searchCustomers(searchText: String?): LiveData<Resource<CustomerResponseModel>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.searchCustomers(searchText)
            emit(response)
    }


    fun createCustomer(request: CustomerModel): LiveData<Resource<CommonResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.createCustomer(request)
            emit(response)
    }

    fun updateCustomer( request: CustomerModel, id: Int?): LiveData<Resource<CommonResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.updateCustomer(request, id)
            emit(response)
    }

    fun createNewEstimation(request: ProjectModel): LiveData<Resource<CommonResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.createNewEstimation(request)
            emit(response)
        }



    fun getDefaultMaterialList(     order: String?,
                                    size: String?,
                                    filter: String?,
                                    join: String?,  ): LiveData<Resource<MaterialResponseModel>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getDefaultMaterialList(     order,
                size,
                filter,
                join,  )
            emit(response)
        }

    fun createDefaultMaterial(request: MaterialReqModel): LiveData<Resource<CommonResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.createDefaultMaterial(request)
            emit(response)
        }


    fun addLineItem(request: CreateLineItemReqModel): LiveData<Resource<CommonResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.addLineItem(request)
            emit(response)
        }

    fun updateLineItem(itemID: Int, request: UpdateLineItemReqModel): LiveData<Resource<CommonResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.updateLineItem(itemID, request)
            emit(response)
        }

    fun getSquareFootLinealFootCosts( type: String?): LiveData<Resource<LinearResponseModel>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getSquareFootLinealFootCosts(type)
            emit(response)
        }

    fun addLinealFootCost(request: LinearFootReqModel): LiveData<Resource<CommonResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.addLinealFootCost(request)
            emit(response)
        }

    fun addSquareFootCost( request: LinearFootReqModel): LiveData<Resource<CommonResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.addSquareFootCost(request)
            emit(response)
        }

    fun getSingleProjectDetails(projectId: Int): LiveData<Resource<AllLineItemsResponseModel>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getSingleProjectDetails(projectId)
            emit(response)
        }
    fun getAllProjects(user_id: Int): LiveData<Resource<ProjectResponseModel>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getAllProjects(user_id)
            emit(response)
        }

    fun initializeDraws(projectId: Int): LiveData<Resource<CommonResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.initializeDraws(projectId)
            emit(response)
        }

    fun getAllDraws(projectId: Int): LiveData<Resource<DrawInfoRespModel>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getAllDraws(projectId)
            emit(response)
        }

    fun deleteDraws(id: Int?): LiveData<Resource<CommonResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.deleteDraws(id)
            emit(response)
        }

    fun createPriceDraw( request: CreatePriceDrawRequest): LiveData<Resource<CommonResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.createPriceDraw(request)
            emit(response)
        }

    fun createPercentageDraw( request: CreatePercentageDrawRequest): LiveData<Resource<CommonResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.createPercentageDraw(request)
            emit(response)
        }
    fun sendInvoice( request: SendInvoiceRequest): LiveData<Resource<CommonResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.sendInvoice(request)
            emit(response)
        }

    fun updatePriceDraw(request: UpdateDrawRequest, id: Int?): LiveData<Resource<CommonResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.updatePriceDraw(request, id)
            emit(response)
        }

    fun updatePercentageDraw(request: UpdateDrawRequest2, id: Int?): LiveData<Resource<CommonResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.updatePercentageDraw(request, id)
            emit(response)
        }

    fun deleteLineItems(id: Int?): LiveData<Resource<CommonResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.deleteLineItems(id)
            emit(response)
        }






























    fun getAllUser( ): LiveData<Resource<FriendListResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getAllUser()
            emit(response)
        }

        fun sendMessageToBot( request: String): LiveData<Resource<ChatBotTextResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.sendMessageToBot(request)
            emit(response)
        }

    fun sendMessageToBot( request: ChatBotRequest): LiveData<Resource<ChatBotTextResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.sendMessageToBot(request)
            emit(response)
        }

    fun sendTextMessage( request: ChatTextRequest): LiveData<Resource<ChatTextResponse>> =
    liveData(Dispatchers.IO) {
        emit(Resource.loading(null))
        val response = remoteDataSource.sendTextMessage(request)
        emit(response)
    }

    fun createRoomRequests( request: CreateRoomRequests): LiveData<Resource<CreateRoomResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.createRoomRequests(request)
            emit(response)
        }


fun getChats( request: ChatRequest): LiveData<Resource<ChatResponse>> =
    liveData(Dispatchers.IO) {
        emit(Resource.loading(null))
        val response = remoteDataSource.getChats(request)
        emit(response)
    }


fun getStartPool( user_id: Int): LiveData<Resource<SingleChatMessageResponse>> =
    liveData(Dispatchers.IO) {
        emit(Resource.loading(null))
        val response = remoteDataSource.getStartPool(user_id)
        emit(response)
    }

fun getAllRoom( user_id: Int): LiveData<Resource<ChatRoomResponse>> =
    liveData(Dispatchers.IO) {
        emit(Resource.loading(null))
        val response = remoteDataSource.getAllRoom(user_id)
        emit(response)
    }



        fun createChangeOrder( request: CreateChangeOrderRequest,     projectId: Any,  ): LiveData<Resource<CreateChangeOrderResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.createChangeOrder(request,    projectId,  )
            emit(response)
          }



        fun finalizeProject(   ): LiveData<Resource<FinalizeProjectResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.finalizeProject(   )
            emit(response)
          }



        fun getProjectReview(    projectId: Any,  ): LiveData<Resource<GetProjectReviewResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getProjectReview(    projectId,  )
            emit(response)
          }



        fun updateDraws( request: UpdateDrawsRequest,     projectId: Int,  ): LiveData<Resource<UpdateDrawsResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.updateDraws(request,    projectId,  )
            emit(response)
          }











        fun trackingMaterial(    projectId: Int,  ): LiveData<Resource<TrackingMaterialResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.trackingMaterial(    projectId,  )
            emit(response)
          }



        fun getProjectTrackingDetails(    projectId: Int,  ): LiveData<Resource<ProjectTrackingResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getProjectTrackingDetails(    projectId,  )
            emit(response)
          }
        fun trackingLabour(    projectId: Int,  ): LiveData<Resource<TrackingLabourResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.trackingLabour(    projectId,  )
            emit(response)
          }



        fun trackingDraws(    projectId: Int,    status: String?,  ): LiveData<Resource<TrackingDrawsResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.trackingDraws(    projectId,    status,  )
            emit(response)
          }



        fun getLineDetails(    lineId: Any,  ): LiveData<Resource<GetLineDetailsResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getLineDetails(    lineId,  )
            emit(response)
          }







        fun finalizingOnboarding(   ): LiveData<Resource<FinalizingOnboardingResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.finalizingOnboarding(   )
            emit(response)
          }















        fun initializeUser(   ): LiveData<Resource<InitializeUserResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.initializeUser(   )
            emit(response)
          }



        fun saveDefaultsOnbording( request: SaveDefaultsOnbordingRequest,    ): LiveData<Resource<SaveDefaultsOnbordingResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.saveDefaultsOnbording(request,   )
            emit(response)
          }



        fun getProjects(     type: String?,
  timePeriod: String?,  ): LiveData<Resource<GetProjectsResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getProjects(     type,
  timePeriod,  )
            emit(response)
          }



        fun onboarding( request: OnboardingRequest,    ): LiveData<Resource<OnboardingResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.onboarding(request,   )
            emit(response)
          }



        fun companyOverview(   ): LiveData<Resource<CompanyOverviewResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.companyOverview(   )
            emit(response)
          }



        fun companyDetails(   ): LiveData<Resource<CompanyDetailsResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.companyDetails(   )
            emit(response)
          }



        fun getProjectStats(   ): LiveData<Resource<GetProjectStatsResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getProjectStats(   )
            emit(response)
          }



        fun lambdaCheck( request: LambdaCheckRequest,    ): LiveData<Resource<LambdaCheckResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.lambdaCheck(request,   )
            emit(response)
          }



        fun twoFALogin( request: TwoFALoginRequest,    ): LiveData<Resource<TwoFALoginResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.twoFALogin(request,   )
            emit(response)
          }



        fun twoFASignin( request: TwoFASigninRequest,    ): LiveData<Resource<TwoFASigninResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.twoFASignin(request,   )
            emit(response)
          }



        fun twoFAAuthorize( request: TwoFAAuthorizeRequest,    ): LiveData<Resource<TwoFAAuthorizeResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.twoFAAuthorize(request,   )
            emit(response)
          }



        fun twoFAEnable( request: TwoFAEnableRequest,    ): LiveData<Resource<TwoFAEnableResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.twoFAEnable(request,   )
            emit(response)
          }



        fun twoFADisable( request: TwoFADisableRequest,    ): LiveData<Resource<TwoFADisableResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.twoFADisable(request,   )
            emit(response)
          }



        fun twoFAVerify( request: TwoFAVerifyRequest,    ): LiveData<Resource<TwoFAVerifyResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.twoFAVerify(request,   )
            emit(response)
          }



        fun twoFAAuth( request: TwoFAAuthRequest,    ): LiveData<Resource<TwoFAAuthResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.twoFAAuth(request,   )
            emit(response)
          }



        fun analyticsLog( request: AnalyticsLogRequest,    ): LiveData<Resource<AnalyticsLogResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.analyticsLog(request,   )
            emit(response)
          }



        fun getAnalytics(   ): LiveData<Resource<GetAnalyticsResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getAnalytics(   )
            emit(response)
          }



        fun logHeatmapAnalytics( request: LogHeatmapAnalyticsRequest,    ): LiveData<Resource<LogHeatmapAnalyticsResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.logHeatmapAnalytics(request,   )
            emit(response)
          }



        fun getHeatmapData(     customDate: String?,  ): LiveData<Resource<GetHeatmapDataResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getHeatmapData(     customDate,  )
            emit(response)
          }



        fun userSessionsData(     customDate: String?,  ): LiveData<Resource<UserSessionsDataResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.userSessionsData(     customDate,  )
            emit(response)
          }



        fun createUserSessionsAnalytics( request: CreateUserSessionsAnalyticsRequest,    ): LiveData<Resource<CreateUserSessionsAnalyticsResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.createUserSessionsAnalytics(request,   )
            emit(response)
          }



        fun appleLoginMobileEndpoint( request: AppleLoginMobileEndpointRequest,    ): LiveData<Resource<AppleLoginMobileEndpointResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.appleLoginMobileEndpoint(request,   )
            emit(response)
          }



        fun appleLogin(   ): LiveData<Resource<AppleLoginResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.appleLogin(   )
            emit(response)
          }



        fun appleAuthCode( request: AppleAuthCodeRequest,    ): LiveData<Resource<AppleAuthCodeResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.appleAuthCode(request,   )
            emit(response)
          }



        fun googleCode(     state: String,  ): LiveData<Resource<GoogleCodeResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.googleCode(     state,  )
            emit(response)
          }



        fun googleCodeMobile(     role: String?,
  isRefresh: Boolean?,
  code: String?,  ): LiveData<Resource<GoogleCodeMobileResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.googleCodeMobile(     role,
  isRefresh,
  code,  )
            emit(response)
          }



        fun googleLogin(     role: String?,
  companyId: String?,
  isRefresh: Boolean?,  ): LiveData<Resource<GoogleLoginResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.googleLogin(     role,
  companyId,
  isRefresh,  )
            emit(response)
          }



        fun blogAll(     limit: Int,
  offset: Int,  ): LiveData<Resource<BlogAllResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.blogAll(     limit,
  offset,  )
            emit(response)
          }



        fun blogSimilar(     top: Int,  ): LiveData<Resource<BlogSimilarResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.blogSimilar(     top,  )
            emit(response)
          }



        fun blogFilter(     categories: ArrayList<Map<String, Any>>,
  tags: ArrayList<Map<String, Any>>,
  rule: String?,
  search: String?,
  limit: Int,
  page: Int,  ): LiveData<Resource<BlogFilterResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.blogFilter(     categories,
  tags,
  rule,
  search,
  limit,
  page,  )
            emit(response)
          }



        fun blogCreate( request: BlogCreateRequest,    ): LiveData<Resource<BlogCreateResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.blogCreate(request,   )
            emit(response)
          }



        fun blogEdit( request: BlogEditRequest,     id: String?,  ): LiveData<Resource<BlogEditResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.blogEdit(request,    id,  )
            emit(response)
          }



        fun blogDelete(    id: String?,  ): LiveData<Resource<BlogDeleteResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.blogDelete(    id,  )
            emit(response)
          }



        fun blogSingle(    id: String?,  ): LiveData<Resource<BlogSingleResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.blogSingle(    id,  )
            emit(response)
          }



        fun blogTags( request: BlogTagsRequest,    ): LiveData<Resource<BlogTagsResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.blogTags(request,   )
            emit(response)
          }



        fun blogTagsUpdate( request: BlogTagsUpdateRequest,    ): LiveData<Resource<BlogTagsUpdateResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.blogTagsUpdate(request,   )
            emit(response)
          }



        fun blogTagsRetrieve(     limit: Int,
  page: Int,
  name: String?,  ): LiveData<Resource<BlogTagsRetrieveResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.blogTagsRetrieve(     limit,
  page,
  name,  )
            emit(response)
          }



        fun blogTagsDeleteByID(    id: String?,  ): LiveData<Resource<BlogTagsDeleteByIDResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.blogTagsDeleteByID(    id,  )
            emit(response)
          }



        fun createBlogCategory( request: CreateBlogCategoryRequest,    ): LiveData<Resource<CreateBlogCategoryResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.createBlogCategory(request,   )
            emit(response)
          }



        fun updateBlogCategory( request: UpdateBlogCategoryRequest,    ): LiveData<Resource<UpdateBlogCategoryResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.updateBlogCategory(request,   )
            emit(response)
          }



        fun getBlogCategory(     limit: Int,
  page: Int,
  name: String?,  ): LiveData<Resource<GetBlogCategoryResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getBlogCategory(     limit,
  page,
  name,  )
            emit(response)
          }



        fun getBlogSubcategory(    id: String?,  ): LiveData<Resource<GetBlogSubcategoryResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getBlogSubcategory(    id,  )
            emit(response)
          }



        fun deleteBlogCategory(    id: String?,  ): LiveData<Resource<DeleteBlogCategoryResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.deleteBlogCategory(    id,  )
            emit(response)
          }



        fun captchaTest(    width: Int,
  height: Int,  ): LiveData<Resource<CaptchaTestResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.captchaTest(    width,
  height,  )
            emit(response)
          }



        fun captchaGenerate(    width: Int,
  height: Int,  ): LiveData<Resource<CaptchaGenerateResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.captchaGenerate(    width,
  height,  )
            emit(response)
          }



        fun googleCaptchaVerify( request: GoogleCaptchaVerifyRequest,    ): LiveData<Resource<GoogleCaptchaVerifyResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.googleCaptchaVerify(request,   )
            emit(response)
          }



        fun createCMSLambda( request: CreateCMSLambdaRequest,    ): LiveData<Resource<CreateCMSLambdaResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.createCMSLambda(request,   )
            emit(response)
          }



        fun updateCMSLambda( request: UpdateCMSLambdaRequest,     id: String?,  ): LiveData<Resource<UpdateCMSLambdaResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.updateCMSLambda(request,    id,  )
            emit(response)
          }



        fun deleteCMSLambda(    id: String?,  ): LiveData<Resource<DeleteCMSLambdaResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.deleteCMSLambda(    id,  )
            emit(response)
          }



        fun getCMSByIDLambda(    id: String?,  ): LiveData<Resource<GetCMSByIDLambdaResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getCMSByIDLambda(    id,  )
            emit(response)
          }



        fun getCMSByPageAndKeyLambda(    page: String?,
  key: String?,  ): LiveData<Resource<GetCMSByPageAndKeyLambdaResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getCMSByPageAndKeyLambda(    page,
  key,  )
            emit(response)
          }



        fun getCMSByPageLambda(    page: String?,  ): LiveData<Resource<GetCMSByPageLambdaResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getCMSByPageLambda(    page,  )
            emit(response)
          }



        fun getAllCMSLambda(   ): LiveData<Resource<GetAllCMSLambdaResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getAllCMSLambda(   )
            emit(response)
          }



        fun registerLambda( request: RegisterLambdaRequest,    ): LiveData<Resource<RegisterLambdaResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.registerLambda(request,   )
            emit(response)
          }



        fun loginLambda( request: LoginLambdaRequest,    ): LiveData<Resource<LoginLambdaResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.loginLambda(request,   )
            emit(response)
          }



        fun marketingLoginLambda( request: MarketingLoginLambdaRequest,    ): LiveData<Resource<MarketingLoginLambdaResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.marketingLoginLambda(request,   )
            emit(response)
          }



        fun profile(   ): LiveData<Resource<ProfileResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.profile(   )
            emit(response)
          }



        fun profileUpdate( request: ProfileUpdateRequest,    ): LiveData<Resource<ProfileUpdateResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.profileUpdate(request,   )
            emit(response)
          }



        fun uploadImageLocalDefault(     file: MultipartBody.Part): LiveData<Resource<UploadImageLocalDefaultResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.uploadImageLocalDefault(    file)
            emit(response)
          }



        fun uploadimages3(     file: MultipartBody.Part): LiveData<Resource<UploadImageS3Response>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.uploadimages3(    file)
            emit(response)
          }



        fun preferenceFetch(   ): LiveData<Resource<PreferenceFetchResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.preferenceFetch(   )
            emit(response)
          }



        fun preferenceUpdate( request: PreferenceUpdateRequest,    ): LiveData<Resource<PreferenceUpdateResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.preferenceUpdate(request,   )
            emit(response)
          }



        fun getSowTree(     order: String?,
  page: String?,  ): LiveData<Resource<GetSowTreeResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getSowTree(     order,
  page,  )
            emit(response)
          }



        fun appAlertsList(     order: String?,
  page: String?,
  filter: String?,  ): LiveData<Resource<AppAlertsListResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.appAlertsList(     order,
  page,
  filter,  )
            emit(response)
          }



        fun appAlertsUpdate( request: AppAlertsUpdateRequest,     id: String?,  ): LiveData<Resource<AppAlertsUpdateResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.appAlertsUpdate(request,    id,  )
            emit(response)
          }



        fun retrieveProductDefault( request: RetrieveProductDefaultRequest,    ): LiveData<Resource<RetrieveProductDefaultResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.retrieveProductDefault(request,   )
            emit(response)
          }



        fun ecomProductByIDDefault(   ): LiveData<Resource<EcomProductByIDDefaultResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.ecomProductByIDDefault(   )
            emit(response)
          }



        fun addEcomProductLambda( request: AddEcomProductLambdaRequest,    ): LiveData<Resource<AddEcomProductLambdaResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.addEcomProductLambda(request,   )
            emit(response)
          }



        fun editEcomProductLambda( request: EditEcomProductLambdaRequest,     id: Number?,  ): LiveData<Resource<EditEcomProductLambdaResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.editEcomProductLambda(request,    id,  )
            emit(response)
          }



        fun deleteEcomProductLambda(    id: Number?,  ): LiveData<Resource<DeleteEcomProductLambdaResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.deleteEcomProductLambda(    id,  )
            emit(response)
          }



        fun getCartItems(     userId: String?,  ): LiveData<Resource<GetCartItemsResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getCartItems(     userId,  )
            emit(response)
          }



        fun ecomAddCart( request: EcomAddCartRequest,    ): LiveData<Resource<EcomAddCartResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.ecomAddCart(request,   )
            emit(response)
          }



        fun ecomDeleteCartItem(     userId: String?,
  data: String?,  ): LiveData<Resource<EcomDeleteCartItemResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.ecomDeleteCartItem(     userId,
  data,  )
            emit(response)
          }



        fun ecomGetProductReview(     productid: Int?,  ): LiveData<Resource<EcomGetProductReviewResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.ecomGetProductReview(     productid,  )
            emit(response)
          }



        fun ecomAddProductReview(     review: String?,
  productid: Int?,  ): LiveData<Resource<EcomAddProductReviewResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.ecomAddProductReview(     review,
  productid,  )
            emit(response)
          }



        fun forgotPassword( request: ForgotPasswordRequest,    ): LiveData<Resource<ForgotPasswordResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.forgotPassword(request,   )
            emit(response)
          }



        fun forgotPasswordMobile( request: ForgotPasswordMobileRequest,    ): LiveData<Resource<ForgotPasswordMobileResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.forgotPasswordMobile(request,   )
            emit(response)
          }



        fun resetPassword( request: ResetPasswordRequest,    ): LiveData<Resource<ResetPasswordResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.resetPassword(request,   )
            emit(response)
          }



        fun resetPasswordMobile( request: ResetPasswordMobileRequest,    ): LiveData<Resource<ResetPasswordMobileResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.resetPasswordMobile(request,   )
            emit(response)
          }



        fun getStripeData( request: GetStripeDataRequest,    ): LiveData<Resource<GetStripeDataResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getStripeData(request,   )
            emit(response)
          }



        fun getOneDefaultSquareFootCost(    id: Int?,    join: String?,  ): LiveData<Resource<GetOneDefaultSquareFootCostResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getOneDefaultSquareFootCost(    id,    join,  )
            emit(response)
          }



        fun getOneSetting(    id: Int?,    join: String?,  ): LiveData<Resource<GetOneSettingResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getOneSetting(    id,    join,  )
            emit(response)
          }



        fun getOneCost(    id: Int?,    join: String?,  ): LiveData<Resource<GetOneCostResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getOneCost(    id,    join,  )
            emit(response)
          }



        fun getOneRoom(    id: Int?,    join: String?,  ): LiveData<Resource<GetOneRoomResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getOneRoom(    id,    join,  )
            emit(response)
          }



        fun getOneLabor(    id: Int?,    join: String?,  ): LiveData<Resource<GetOneLaborResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getOneLabor(    id,    join,  )
            emit(response)
          }



        fun getOneLineItemEntry(    id: Int?,    join: String?,  ): LiveData<Resource<GetOneLineItemEntryResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getOneLineItemEntry(    id,    join,  )
            emit(response)
          }



        fun getOneCompanySettings(    id: Int?,    join: String?,  ): LiveData<Resource<GetOneCompanySettingsResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getOneCompanySettings(    id,    join,  )
            emit(response)
          }



        fun getOneCms(    id: Int?,    join: String?,  ): LiveData<Resource<GetOneCmsResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getOneCms(    id,    join,  )
            emit(response)
          }



        fun getOneTeamMember(    id: Int?,    join: String?,  ): LiveData<Resource<GetOneTeamMemberResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getOneTeamMember(    id,    join,  )
            emit(response)
          }



        fun getOneDefaultMaterial(    id: Int?,    join: String?,  ): LiveData<Resource<GetOneDefaultMaterialResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getOneDefaultMaterial(    id,    join,  )
            emit(response)
          }



        fun getOneProject(    id: Int?,    join: String?,  ): LiveData<Resource<GetOneProjectResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getOneProject(    id,    join,  )
            emit(response)
          }



        fun getOneUser(    id: Int?,    join: String?,  ): LiveData<Resource<GetOneUserResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getOneUser(    id,    join,  )
            emit(response)
          }



        fun getOneProfile(    id: Int?,    join: String?,  ): LiveData<Resource<GetOneProfileResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getOneProfile(    id,    join,  )
            emit(response)
          }



        fun getOneLinealFootCost(    id: Int?,    join: String?,  ): LiveData<Resource<GetOneLinealFootCostResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getOneLinealFootCost(    id,    join,  )
            emit(response)
          }



        fun getOneCustomer(    id: Int?,    join: String?,  ): LiveData<Resource<GetOneCustomerResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getOneCustomer(    id,    join,  )
            emit(response)
          }



        fun getOnePermission(    id: Int?,    join: String?,  ): LiveData<Resource<GetOnePermissionResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getOnePermission(    id,    join,  )
            emit(response)
          }



        fun getOneToken(    id: Int?,    join: String?,  ): LiveData<Resource<GetOneTokenResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getOneToken(    id,    join,  )
            emit(response)
          }



        fun getOneSqftCosts(    id: Int?,    join: String?,  ): LiveData<Resource<GetOneSqftCostsResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getOneSqftCosts(    id,    join,  )
            emit(response)
          }



        fun getOneEmail(    id: Int?,    join: String?,  ): LiveData<Resource<GetOneEmailResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getOneEmail(    id,    join,  )
            emit(response)
          }



        fun getOneAlerts(    id: Int?,    join: String?,  ): LiveData<Resource<GetOneAlertsResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getOneAlerts(    id,    join,  )
            emit(response)
          }



        fun getOneDraws(    id: Int?,    join: String?,  ): LiveData<Resource<GetOneDrawsResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getOneDraws(    id,    join,  )
            emit(response)
          }



        fun getOneChat(    id: Int?,    join: String?,  ): LiveData<Resource<GetOneChatResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getOneChat(    id,    join,  )
            emit(response)
          }



        fun getOneMaterial(    id: Int?,    join: String?,  ): LiveData<Resource<GetOneMaterialResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getOneMaterial(    id,    join,  )
            emit(response)
          }



        fun getOneInvoice(    id: Int?,    join: String?,  ): LiveData<Resource<GetOneInvoiceResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getOneInvoice(    id,    join,  )
            emit(response)
          }



        fun getOneDefaultLinealFootCost(    id: Int?,    join: String?,  ): LiveData<Resource<GetOneDefaultLinealFootCostResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getOneDefaultLinealFootCost(    id,    join,  )
            emit(response)
          }



        fun getOneTriggerType(    id: Int?,    join: String?,  ): LiveData<Resource<GetOneTriggerTypeResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getOneTriggerType(    id,    join,  )
            emit(response)
          }



        fun getOneJob(    id: Int?,    join: String?,  ): LiveData<Resource<GetOneJobResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getOneJob(    id,    join,  )
            emit(response)
          }



        fun getOneLineItems(    id: Int?,    join: String?,  ): LiveData<Resource<GetOneLineItemsResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getOneLineItems(    id,    join,  )
            emit(response)
          }



        fun getOnePhoto(    id: Int?,    join: String?,  ): LiveData<Resource<GetOnePhotoResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getOnePhoto(    id,    join,  )
            emit(response)
          }



        fun getOneApiKeys(    id: Int?,    join: String?,  ): LiveData<Resource<GetOneApiKeysResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getOneApiKeys(    id,    join,  )
            emit(response)
          }



        fun getOneChangeOrderDescription(    id: Int?,    join: String?,  ): LiveData<Resource<GetOneChangeOrderDescriptionResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getOneChangeOrderDescription(    id,    join,  )
            emit(response)
          }



        fun getOneAnalyticLog(    id: Int?,    join: String?,  ): LiveData<Resource<GetOneAnalyticLogResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getOneAnalyticLog(    id,    join,  )
            emit(response)
          }



        fun getOnePosts(    id: Int?,    join: String?,  ): LiveData<Resource<GetOnePostsResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getOnePosts(    id,    join,  )
            emit(response)
          }



        fun getOneEmployee(    id: Int?,    join: String?,  ): LiveData<Resource<GetOneEmployeeResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getOneEmployee(    id,    join,  )
            emit(response)
          }



        fun getDefaultSquareFootCostList(     order: String?,
  size: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetDefaultSquareFootCostListResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getDefaultSquareFootCostList(     order,
  size,
  filter,
  join,  )
            emit(response)
          }



        fun getSettingList(     order: String?,
  size: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetSettingListResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getSettingList(     order,
  size,
  filter,
  join,  )
            emit(response)
          }



        fun getCostList(     order: String?,
  size: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetCostListResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getCostList(     order,
  size,
  filter,
  join,  )
            emit(response)
          }



        fun getRoomList(     order: String?,
  size: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetRoomListResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getRoomList(     order,
  size,
  filter,
  join,  )
            emit(response)
          }



        fun getLaborList(     order: String?,
  size: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetLaborListResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getLaborList(     order,
  size,
  filter,
  join,  )
            emit(response)
          }



        fun getLineItemEntryList(     order: String?,
  size: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetLineItemEntryListResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getLineItemEntryList(     order,
  size,
  filter,
  join,  )
            emit(response)
          }



        fun getCompanySettingsList(     order: String?,
  size: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetCompanySettingsListResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getCompanySettingsList(     order,
  size,
  filter,
  join,  )
            emit(response)
          }



        fun getCmsList(     order: String?,
  size: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetCmsListResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getCmsList(     order,
  size,
  filter,
  join,  )
            emit(response)
          }



        fun getTeamMemberList(     order: String?,
  size: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetTeamMemberListResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getTeamMemberList(     order,
  size,
  filter,
  join,  )
            emit(response)
          }







        fun getProjectList(     order: String?,
  size: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetProjectListResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getProjectList(     order,
  size,
  filter,
  join,  )
            emit(response)
          }



        fun getUserList(     order: String?,
  size: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetUserListResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getUserList(     order,
  size,
  filter,
  join,  )
            emit(response)
          }



        fun getProfileList(     order: String?,
  size: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetProfileListResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getProfileList(     order,
  size,
  filter,
  join,  )
            emit(response)
          }



        fun getLinealFootCostList(     order: String?,
  size: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetLinealFootCostListResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getLinealFootCostList(     order,
  size,
  filter,
  join,  )
            emit(response)
          }



        fun getCustomerList(     order: String?,
  size: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetCustomerListResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getCustomerList(     order,
  size,
  filter,
  join,  )
            emit(response)
          }



        fun getPermissionList(     order: String?,
  size: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetPermissionListResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getPermissionList(     order,
  size,
  filter,
  join,  )
            emit(response)
          }



        fun getTokenList(     order: String?,
  size: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetTokenListResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getTokenList(     order,
  size,
  filter,
  join,  )
            emit(response)
          }



        fun getSqftCostsList(     order: String?,
  size: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetSqftCostsListResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getSqftCostsList(     order,
  size,
  filter,
  join,  )
            emit(response)
          }



        fun getEmailList(     order: String?,
  size: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetEmailListResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getEmailList(     order,
  size,
  filter,
  join,  )
            emit(response)
          }



        fun getAlertsList(     order: String?,
  size: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetAlertsListResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getAlertsList(     order,
  size,
  filter,
  join,  )
            emit(response)
          }



        fun getDrawsList(     order: String?,
  size: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetDrawsListResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getDrawsList(     order,
  size,
  filter,
  join,  )
            emit(response)
          }



        fun getChatList(     order: String?,
  size: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetChatListResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getChatList(     order,
  size,
  filter,
  join,  )
            emit(response)
          }



        fun getMaterialList(     order: String?,
  size: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetMaterialListResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getMaterialList(     order,
  size,
  filter,
  join,  )
            emit(response)
          }



        fun getInvoiceList(     order: String?,
  size: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetInvoiceListResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getInvoiceList(     order,
  size,
  filter,
  join,  )
            emit(response)
          }



        fun getDefaultLinealFootCostList(     order: String?,
  size: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetDefaultLinealFootCostListResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getDefaultLinealFootCostList(     order,
  size,
  filter,
  join,  )
            emit(response)
          }



        fun getTriggerTypeList(     order: String?,
  size: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetTriggerTypeListResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getTriggerTypeList(     order,
  size,
  filter,
  join,  )
            emit(response)
          }



        fun getJobList(     order: String?,
  size: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetJobListResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getJobList(     order,
  size,
  filter,
  join,  )
            emit(response)
          }



        fun getLineItemsList(     order: String?,
  size: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetLineItemsListResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getLineItemsList(     order,
  size,
  filter,
  join,  )
            emit(response)
          }



        fun getPhotoList(     order: String?,
  size: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetPhotoListResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getPhotoList(     order,
  size,
  filter,
  join,  )
            emit(response)
          }



        fun getApiKeysList(     order: String?,
  size: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetApiKeysListResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getApiKeysList(     order,
  size,
  filter,
  join,  )
            emit(response)
          }



        fun getChangeOrderDescriptionList(     order: String?,
  size: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetChangeOrderDescriptionListResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getChangeOrderDescriptionList(     order,
  size,
  filter,
  join,  )
            emit(response)
          }



        fun getAnalyticLogList(     order: String?,
  size: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetAnalyticLogListResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getAnalyticLogList(     order,
  size,
  filter,
  join,  )
            emit(response)
          }



        fun getPostsList(     order: String?,
  size: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetPostsListResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getPostsList(     order,
  size,
  filter,
  join,  )
            emit(response)
          }



        fun getEmployeeList(     order: String?,
  size: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetEmployeeListResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getEmployeeList(     order,
  size,
  filter,
  join,  )
            emit(response)
          }



        fun getDefaultSquareFootCostPaginated(     order: String?,
  page: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetDefaultSquareFootCostPaginatedResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getDefaultSquareFootCostPaginated(     order,
  page,
  filter,
  join,  )
            emit(response)
          }



        fun getSettingPaginated(     order: String?,
  page: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetSettingPaginatedResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getSettingPaginated(     order,
  page,
  filter,
  join,  )
            emit(response)
          }



        fun getCostPaginated(     order: String?,
  page: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetCostPaginatedResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getCostPaginated(     order,
  page,
  filter,
  join,  )
            emit(response)
          }



        fun getRoomPaginated(     order: String?,
  page: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetRoomPaginatedResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getRoomPaginated(     order,
  page,
  filter,
  join,  )
            emit(response)
          }



        fun getLaborPaginated(     order: String?,
  page: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetLaborPaginatedResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getLaborPaginated(     order,
  page,
  filter,
  join,  )
            emit(response)
          }



        fun getLineItemEntryPaginated(     order: String?,
  page: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetLineItemEntryPaginatedResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getLineItemEntryPaginated(     order,
  page,
  filter,
  join,  )
            emit(response)
          }



        fun getCompanySettingsPaginated(     order: String?,
  page: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetCompanySettingsPaginatedResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getCompanySettingsPaginated(     order,
  page,
  filter,
  join,  )
            emit(response)
          }



        fun getCmsPaginated(     order: String?,
  page: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetCmsPaginatedResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getCmsPaginated(     order,
  page,
  filter,
  join,  )
            emit(response)
          }



        fun getTeamMemberPaginated(     order: String?,
  page: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetTeamMemberPaginatedResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getTeamMemberPaginated(     order,
  page,
  filter,
  join,  )
            emit(response)
          }



        fun getDefaultMaterialPaginated(     order: String?,
  page: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetDefaultMaterialPaginatedResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getDefaultMaterialPaginated(     order,
  page,
  filter,
  join,  )
            emit(response)
          }



        fun getProjectPaginated(     order: String?,
  page: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetProjectPaginatedResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getProjectPaginated(     order,
  page,
  filter,
  join,  )
            emit(response)
          }



        fun getUserPaginated(     order: String?,
  page: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetUserPaginatedResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getUserPaginated(     order,
  page,
  filter,
  join,  )
            emit(response)
          }



        fun getProfilePaginated(     order: String?,
  page: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetProfilePaginatedResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getProfilePaginated(     order,
  page,
  filter,
  join,  )
            emit(response)
          }



        fun getLinealFootCostPaginated(     order: String?,
  page: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetLinealFootCostPaginatedResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getLinealFootCostPaginated(     order,
  page,
  filter,
  join,  )
            emit(response)
          }



        fun getCustomerPaginated(     order: String?,
  page: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetCustomerPaginatedResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getCustomerPaginated(     order,
  page,
  filter,
  join,  )
            emit(response)
          }



        fun getPermissionPaginated(     order: String?,
  page: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetPermissionPaginatedResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getPermissionPaginated(     order,
  page,
  filter,
  join,  )
            emit(response)
          }



        fun getTokenPaginated(     order: String?,
  page: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetTokenPaginatedResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getTokenPaginated(     order,
  page,
  filter,
  join,  )
            emit(response)
          }



        fun getSqftCostsPaginated(     order: String?,
  page: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetSqftCostsPaginatedResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getSqftCostsPaginated(     order,
  page,
  filter,
  join,  )
            emit(response)
          }



        fun getEmailPaginated(     order: String?,
  page: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetEmailPaginatedResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getEmailPaginated(     order,
  page,
  filter,
  join,  )
            emit(response)
          }



        fun getAlertsPaginated(     order: String?,
  page: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetAlertsPaginatedResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getAlertsPaginated(     order,
  page,
  filter,
  join,  )
            emit(response)
          }



        fun getDrawsPaginated(     order: String?,
  page: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetDrawsPaginatedResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getDrawsPaginated(     order,
  page,
  filter,
  join,  )
            emit(response)
          }



        fun getChatPaginated(     order: String?,
  page: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetChatPaginatedResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getChatPaginated(     order,
  page,
  filter,
  join,  )
            emit(response)
          }



        fun getMaterialPaginated(     order: String?,
  page: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetMaterialPaginatedResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getMaterialPaginated(     order,
  page,
  filter,
  join,  )
            emit(response)
          }



        fun getInvoicePaginated(     order: String?,
  page: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetInvoicePaginatedResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getInvoicePaginated(     order,
  page,
  filter,
  join,  )
            emit(response)
          }



        fun getDefaultLinealFootCostPaginated(     order: String?,
  page: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetDefaultLinealFootCostPaginatedResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getDefaultLinealFootCostPaginated(     order,
  page,
  filter,
  join,  )
            emit(response)
          }



        fun getTriggerTypePaginated(     order: String?,
  page: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetTriggerTypePaginatedResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getTriggerTypePaginated(     order,
  page,
  filter,
  join,  )
            emit(response)
          }



        fun getJobPaginated(     order: String?,
  page: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetJobPaginatedResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getJobPaginated(     order,
  page,
  filter,
  join,  )
            emit(response)
          }



        fun getLineItemsPaginated(     order: String?,
  page: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetLineItemsPaginatedResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getLineItemsPaginated(     order,
  page,
  filter,
  join,  )
            emit(response)
          }



        fun getPhotoPaginated(     order: String?,
  page: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetPhotoPaginatedResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getPhotoPaginated(     order,
  page,
  filter,
  join,  )
            emit(response)
          }



        fun getApiKeysPaginated(     order: String?,
  page: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetApiKeysPaginatedResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getApiKeysPaginated(     order,
  page,
  filter,
  join,  )
            emit(response)
          }



        fun getChangeOrderDescriptionPaginated(     order: String?,
  page: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetChangeOrderDescriptionPaginatedResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getChangeOrderDescriptionPaginated(     order,
  page,
  filter,
  join,  )
            emit(response)
          }



        fun getAnalyticLogPaginated(     order: String?,
  page: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetAnalyticLogPaginatedResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getAnalyticLogPaginated(     order,
  page,
  filter,
  join,  )
            emit(response)
          }



        fun getPostsPaginated(     order: String?,
  page: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetPostsPaginatedResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getPostsPaginated(     order,
  page,
  filter,
  join,  )
            emit(response)
          }



        fun getEmployeePaginated(     order: String?,
  page: String?,
  filter: String?,
  join: String?,  ): LiveData<Resource<GetEmployeePaginatedResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getEmployeePaginated(     order,
  page,
  filter,
  join,  )
            emit(response)
          }



        fun createDefaultSquareFootCost( request: CreateDefaultSquareFootCostRequest,    ): LiveData<Resource<CreateDefaultSquareFootCostResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.createDefaultSquareFootCost(request,   )
            emit(response)
          }



        fun createSetting( request: CreateSettingRequest,    ): LiveData<Resource<CreateSettingResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.createSetting(request,   )
            emit(response)
          }



        fun createCost( request: CreateCostRequest,    ): LiveData<Resource<CreateCostResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.createCost(request,   )
            emit(response)
          }



        fun createRoom( request: CreateRoomRequest,    ): LiveData<Resource<CreateRoomResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.createRoom(request,   )
            emit(response)
          }



        fun createLabor( request: CreateLaborRequest,    ): LiveData<Resource<CreateLaborResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.createLabor(request,   )
            emit(response)
          }



        fun createLineItemEntry( request: CreateLineItemEntryRequest,    ): LiveData<Resource<CreateLineItemEntryResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.createLineItemEntry(request,   )
            emit(response)
          }



        fun createCompanySettings( request: CreateCompanySettingsRequest,    ): LiveData<Resource<CreateCompanySettingsResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.createCompanySettings(request,   )
            emit(response)
          }



        fun createCms( request: CreateCmsRequest,    ): LiveData<Resource<CreateCmsResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.createCms(request,   )
            emit(response)
          }



        fun createTeamMember( request: CreateTeamMemberRequest,    ): LiveData<Resource<CreateTeamMemberResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.createTeamMember(request,   )
            emit(response)
          }


        fun createDraw( request: CreateDrawRequest,    ): LiveData<Resource<CommonResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.createDraw(request,   )
            emit(response)
          }







        fun createProject( request: CreateProjectRequest,    ): LiveData<Resource<CreateProjectResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.createProject(request,   )
            emit(response)
          }



        fun createUser( request: CreateUserRequest,    ): LiveData<Resource<CreateUserResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.createUser(request,   )
            emit(response)
          }



        fun createProfile( request: CreateProfileRequest,    ): LiveData<Resource<CreateProfileResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.createProfile(request,   )
            emit(response)
          }



        fun createLinealFootCost( request: CreateLinealFootCostRequest,    ): LiveData<Resource<CreateLinealFootCostResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.createLinealFootCost(request,   )
            emit(response)
          }




        fun createPermission( request: CreatePermissionRequest,    ): LiveData<Resource<CreatePermissionResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.createPermission(request,   )
            emit(response)
          }



        fun createToken( request: CreateTokenRequest,    ): LiveData<Resource<CreateTokenResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.createToken(request,   )
            emit(response)
          }



        fun createSqftCosts( request: CreateSqftCostsRequest,    ): LiveData<Resource<CreateSqftCostsResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.createSqftCosts(request,   )
            emit(response)
          }



        fun createEmail( request: CreateEmailRequest,    ): LiveData<Resource<CreateEmailResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.createEmail(request,   )
            emit(response)
          }



        fun createAlerts( request: CreateAlertsRequest,    ): LiveData<Resource<CreateAlertsResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.createAlerts(request,   )
            emit(response)
          }

        fun createChat( request: CreateChatRequest,    ): LiveData<Resource<CreateChatResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.createChat(request,   )
            emit(response)
          }



        fun createMaterial( request: CreateMaterialRequest,    ): LiveData<Resource<CreateMaterialResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.createMaterial(request,   )
            emit(response)
          }



        fun createInvoice( request: CreateInvoiceRequest,    ): LiveData<Resource<CreateInvoiceResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.createInvoice(request,   )
            emit(response)
          }



        fun createDefaultLinealFootCost( request: CreateDefaultLinealFootCostRequest,    ): LiveData<Resource<CreateDefaultLinealFootCostResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.createDefaultLinealFootCost(request,   )
            emit(response)
          }



        fun createTriggerType( request: CreateTriggerTypeRequest,    ): LiveData<Resource<CreateTriggerTypeResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.createTriggerType(request,   )
            emit(response)
          }



        fun createJob( request: CreateJobRequest,    ): LiveData<Resource<CreateJobResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.createJob(request,   )
            emit(response)
          }



        fun createLineItems( request: CreateLineItemsRequest,    ): LiveData<Resource<CreateLineItemsResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.createLineItems(request,   )
            emit(response)
          }



        fun createPhoto( request: CreatePhotoRequest,    ): LiveData<Resource<CreatePhotoResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.createPhoto(request,   )
            emit(response)
          }



        fun createApiKeys( request: CreateApiKeysRequest,    ): LiveData<Resource<CreateApiKeysResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.createApiKeys(request,   )
            emit(response)
          }



        fun createChangeOrderDescription( request: CreateChangeOrderDescriptionRequest,    ): LiveData<Resource<CreateChangeOrderDescriptionResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.createChangeOrderDescription(request,   )
            emit(response)
          }



        fun createAnalyticLog( request: CreateAnalyticLogRequest,    ): LiveData<Resource<CreateAnalyticLogResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.createAnalyticLog(request,   )
            emit(response)
          }



        fun createPosts( request: CreatePostsRequest,    ): LiveData<Resource<CreatePostsResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.createPosts(request,   )
            emit(response)
          }



        fun createEmployee( request: CreateEmployeeRequest,    ): LiveData<Resource<CreateEmployeeResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.createEmployee(request,   )
            emit(response)
          }



        fun updateDefaultSquareFootCost( request: UpdateDefaultSquareFootCostRequest,     id: Int?,  ): LiveData<Resource<UpdateDefaultSquareFootCostResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.updateDefaultSquareFootCost(request,    id,  )
            emit(response)
          }



        fun updateSetting( request: UpdateSettingRequest,     id: Int?,  ): LiveData<Resource<UpdateSettingResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.updateSetting(request,    id,  )
            emit(response)
          }



        fun updateCost( request: UpdateCostRequest,     id: Int?,  ): LiveData<Resource<UpdateCostResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.updateCost(request,    id,  )
            emit(response)
          }



        fun updateRoom( request: UpdateRoomRequest,     id: Int?,  ): LiveData<Resource<UpdateRoomResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.updateRoom(request,    id,  )
            emit(response)
          }



        fun updateLabor( request: UpdateLaborRequest,     id: Int?,  ): LiveData<Resource<UpdateLaborResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.updateLabor(request,    id,  )
            emit(response)
          }



        fun updateLineItemEntry( request: UpdateLineItemEntryRequest,     id: Int?,  ): LiveData<Resource<UpdateLineItemEntryResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.updateLineItemEntry(request,    id,  )
            emit(response)
          }



        fun updateCompanySettings( request: UpdateCompanySettingsRequest,     id: Int?,  ): LiveData<Resource<UpdateCompanySettingsResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.updateCompanySettings(request,    id,  )
            emit(response)
          }



        fun updateCms( request: UpdateCmsRequest,     id: Int?,  ): LiveData<Resource<UpdateCmsResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.updateCms(request,    id,  )
            emit(response)
          }



        fun updateTeamMember( request: UpdateTeamMemberRequest,     id: Int?,  ): LiveData<Resource<UpdateTeamMemberResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.updateTeamMember(request,    id,  )
            emit(response)
          }



        fun updateDefaultMaterial( request: UpdateDefaultMaterialRequest,     id: Int?,  ): LiveData<Resource<UpdateDefaultMaterialResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.updateDefaultMaterial(request,    id,  )
            emit(response)
          }

        fun updateLinealFootCost( request: LinearFootReqModel,     id: Int?,  ): LiveData<Resource<CommonResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.updateLinealFootCost(request,    id,  )
            emit(response)
          }

        fun updateSquareFootCost( request: LinearFootReqModel,     id: Int?,  ): LiveData<Resource<CommonResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.updateSquareFootCost(request,    id,  )
            emit(response)
          }



        fun updateProject( request: UpdateProjectRequest,     id: Int?,  ): LiveData<Resource<UpdateProjectResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.updateProject(request,    id,  )
            emit(response)
          }



        fun updateUser( request: UpdateUserRequest,     id: Int?,  ): LiveData<Resource<UpdateUserResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.updateUser(request,    id,  )
            emit(response)
          }



        fun updateProfile( request: UpdateProfileRequest,     id: Int?,  ): LiveData<Resource<UpdateProfileResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.updateProfile(request,    id,  )
            emit(response)
          }



        fun updateLinealFootCost( request: UpdateLinealFootCostRequest,     id: Int?,  ): LiveData<Resource<UpdateLinealFootCostResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.updateLinealFootCost(request,    id,  )
            emit(response)
          }







        fun updatePermission( request: UpdatePermissionRequest,     id: Int?,  ): LiveData<Resource<UpdatePermissionResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.updatePermission(request,    id,  )
            emit(response)
          }



        fun updateToken( request: UpdateTokenRequest,     id: Int?,  ): LiveData<Resource<UpdateTokenResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.updateToken(request,    id,  )
            emit(response)
          }



        fun updateSqftCosts( request: UpdateSqftCostsRequest,     id: Int?,  ): LiveData<Resource<UpdateSqftCostsResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.updateSqftCosts(request,    id,  )
            emit(response)
          }



        fun updateEmail( request: UpdateEmailRequest,     id: Int?,  ): LiveData<Resource<UpdateEmailResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.updateEmail(request,    id,  )
            emit(response)
          }



        fun updateAlerts( request: UpdateAlertsRequest,     id: Int?,  ): LiveData<Resource<UpdateAlertsResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.updateAlerts(request,    id,  )
            emit(response)
          }



        fun updateDraws( request: UpdateDrawsRequest,     id: Int?,  ): LiveData<Resource<UpdateDrawsResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.updateDraws(request,    id,  )
            emit(response)
          }



        fun updateChat( request: UpdateChatRequest,     id: Int?,  ): LiveData<Resource<UpdateChatResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.updateChat(request,    id,  )
            emit(response)
          }



        fun updateMaterial( request: UpdateMaterialRequest,     id: Int?,  ): LiveData<Resource<UpdateMaterialResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.updateMaterial(request,    id,  )
            emit(response)
          }



        fun updateInvoice( request: UpdateInvoiceRequest,     id: Int?,  ): LiveData<Resource<UpdateInvoiceResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.updateInvoice(request,    id,  )
            emit(response)
          }



        fun updateDefaultLinealFootCost( request: UpdateDefaultLinealFootCostRequest,     id: Int?,  ): LiveData<Resource<UpdateDefaultLinealFootCostResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.updateDefaultLinealFootCost(request,    id,  )
            emit(response)
          }



        fun updateTriggerType( request: UpdateTriggerTypeRequest,     id: Int?,  ): LiveData<Resource<UpdateTriggerTypeResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.updateTriggerType(request,    id,  )
            emit(response)
          }



        fun updateJob( request: UpdateJobRequest,     id: Int?,  ): LiveData<Resource<UpdateJobResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.updateJob(request,    id,  )
            emit(response)
          }



        fun updateLineItems( request: UpdateLineItemsRequest,     id: Int?,  ): LiveData<Resource<UpdateLineItemsResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.updateLineItems(request,    id,  )
            emit(response)
          }



        fun updatePhoto( request: UpdatePhotoRequest,     id: Int?,  ): LiveData<Resource<UpdatePhotoResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.updatePhoto(request,    id,  )
            emit(response)
          }



        fun updateApiKeys( request: UpdateApiKeysRequest,     id: Int?,  ): LiveData<Resource<UpdateApiKeysResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.updateApiKeys(request,    id,  )
            emit(response)
          }



        fun updateChangeOrderDescription( request: UpdateChangeOrderDescriptionRequest,     id: Int?,  ): LiveData<Resource<UpdateChangeOrderDescriptionResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.updateChangeOrderDescription(request,    id,  )
            emit(response)
          }



        fun updateAnalyticLog( request: UpdateAnalyticLogRequest,     id: Int?,  ): LiveData<Resource<UpdateAnalyticLogResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.updateAnalyticLog(request,    id,  )
            emit(response)
          }



        fun updatePosts( request: UpdatePostsRequest,     id: Int?,  ): LiveData<Resource<UpdatePostsResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.updatePosts(request,    id,  )
            emit(response)
          }



        fun updateEmployee( request: UpdateEmployeeRequest,     id: Int?,  ): LiveData<Resource<UpdateEmployeeResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.updateEmployee(request,    id,  )
            emit(response)
          }



        fun deleteDefaultSquareFootCost(    id: Int?,  ): LiveData<Resource<DeleteDefaultSquareFootCostResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.deleteDefaultSquareFootCost(    id,  )
            emit(response)
          }



        fun deleteSetting(    id: Int?,  ): LiveData<Resource<DeleteSettingResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.deleteSetting(    id,  )
            emit(response)
          }



        fun deleteCost(    id: Int?,  ): LiveData<Resource<DeleteCostResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.deleteCost(    id,  )
            emit(response)
          }



        fun deleteRoom(    id: Int?,  ): LiveData<Resource<DeleteRoomResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.deleteRoom(    id,  )
            emit(response)
          }



        fun deleteLabor(    id: Int?,  ): LiveData<Resource<DeleteLaborResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.deleteLabor(    id,  )
            emit(response)
          }



        fun deleteLineItemEntry(    id: Int?,  ): LiveData<Resource<DeleteLineItemEntryResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.deleteLineItemEntry(    id,  )
            emit(response)
          }



        fun deleteCompanySettings(    id: Int?,  ): LiveData<Resource<DeleteCompanySettingsResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.deleteCompanySettings(    id,  )
            emit(response)
          }



        fun deleteCms(    id: Int?,  ): LiveData<Resource<DeleteCmsResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.deleteCms(    id,  )
            emit(response)
          }



        fun deleteTeamMember(    id: Int?,  ): LiveData<Resource<DeleteTeamMemberResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.deleteTeamMember(    id,  )
            emit(response)
          }



        fun deleteDefaultMaterial(    id: Int?,  ): LiveData<Resource<DeleteDefaultMaterialResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.deleteDefaultMaterial(    id,  )
            emit(response)
          }

        fun deleteLinealFootCosts(    id: Int?,  ): LiveData<Resource<CommonResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.deleteLinealFootCosts(    id,  )
            emit(response)
          }

        fun deleteSquareFootCost(    id: Int?,  ): LiveData<Resource<CommonResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.deleteSquareFootCost(    id,  )
            emit(response)
          }



        fun deleteProject(    id: Int?,  ): LiveData<Resource<DeleteProjectResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.deleteProject(    id,  )
            emit(response)
          }



        fun deleteUser(    id: Int?,  ): LiveData<Resource<DeleteUserResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.deleteUser(    id,  )
            emit(response)
          }



        fun deleteProfile(    id: Int?,  ): LiveData<Resource<DeleteProfileResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.deleteProfile(    id,  )
            emit(response)
          }



        fun deleteLinealFootCost(    id: Int?,  ): LiveData<Resource<DeleteLinealFootCostResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.deleteLinealFootCost(    id,  )
            emit(response)
          }



        fun deleteCustomer(    id: Int?,  ): LiveData<Resource<DeleteCustomerResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.deleteCustomer(    id,  )
            emit(response)
          }



        fun deletePermission(    id: Int?,  ): LiveData<Resource<DeletePermissionResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.deletePermission(    id,  )
            emit(response)
          }



        fun deleteToken(    id: Int?,  ): LiveData<Resource<DeleteTokenResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.deleteToken(    id,  )
            emit(response)
          }



        fun deleteSqftCosts(    id: Int?,  ): LiveData<Resource<DeleteSqftCostsResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.deleteSqftCosts(    id,  )
            emit(response)
          }



        fun deleteEmail(    id: Int?,  ): LiveData<Resource<DeleteEmailResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.deleteEmail(    id,  )
            emit(response)
          }



        fun deleteAlerts(    id: Int?,  ): LiveData<Resource<DeleteAlertsResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.deleteAlerts(    id,  )
            emit(response)
          }







        fun deleteChat(    id: Int?,  ): LiveData<Resource<DeleteChatResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.deleteChat(    id,  )
            emit(response)
          }



        fun deleteMaterial(    id: Int?,  ): LiveData<Resource<DeleteMaterialResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.deleteMaterial(    id,  )
            emit(response)
          }



        fun deleteInvoice(    id: Int?,  ): LiveData<Resource<DeleteInvoiceResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.deleteInvoice(    id,  )
            emit(response)
          }



        fun deleteDefaultLinealFootCost(    id: Int?,  ): LiveData<Resource<DeleteDefaultLinealFootCostResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.deleteDefaultLinealFootCost(    id,  )
            emit(response)
          }



        fun deleteTriggerType(    id: Int?,  ): LiveData<Resource<DeleteTriggerTypeResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.deleteTriggerType(    id,  )
            emit(response)
          }



        fun deleteJob(    id: Int?,  ): LiveData<Resource<DeleteJobResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.deleteJob(    id,  )
            emit(response)
          }







        fun deletePhoto(    id: Int?,  ): LiveData<Resource<DeletePhotoResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.deletePhoto(    id,  )
            emit(response)
          }



        fun deleteApiKeys(    id: Int?,  ): LiveData<Resource<DeleteApiKeysResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.deleteApiKeys(    id,  )
            emit(response)
          }



        fun deleteChangeOrderDescription(    id: Int?,  ): LiveData<Resource<DeleteChangeOrderDescriptionResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.deleteChangeOrderDescription(    id,  )
            emit(response)
          }



        fun deleteAnalyticLog(    id: Int?,  ): LiveData<Resource<DeleteAnalyticLogResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.deleteAnalyticLog(    id,  )
            emit(response)
          }



        fun deletePosts(    id: Int?,  ): LiveData<Resource<DeletePostsResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.deletePosts(    id,  )
            emit(response)
          }



        fun deleteEmployee(    id: Int?,  ): LiveData<Resource<DeleteEmployeeResponse>> =
          liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.deleteEmployee(    id,  )
            emit(response)
          }


    // Subscription repository functions
    fun createSubscription(request: CreateSubscriptionRequest): LiveData<Resource<CreateSubscriptionResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.createSubscription(request)
            emit(response)
        }

    fun getPlans(): LiveData<Resource<GetPlansResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getPlans()
            emit(response)
        }

    fun getUserSubscriptions(userId: Int): LiveData<Resource<GetUserSubscriptionsResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getUserSubscriptions(userId)
            emit(response)
        }

    fun cancelSubscription(request: CancelSubscriptionRequest): LiveData<Resource<CancelSubscriptionResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.cancelSubscription(request)
            emit(response)
        }

    fun getPaymentHistory(userId: Int): LiveData<Resource<GetPaymentHistoryResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getPaymentHistory(userId)
            emit(response)
        }



    fun getSubscriptionStatus(subscriptionId: Int): LiveData<Resource<GetSubscriptionStatusResponse>> =
        liveData(Dispatchers.IO) {
            emit(Resource.loading(null))
            val response = remoteDataSource.getSubscriptionStatus(subscriptionId)
            emit(response)
        }
}
