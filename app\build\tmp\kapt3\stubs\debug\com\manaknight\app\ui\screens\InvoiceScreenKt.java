package com.manaknight.app.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000B\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0010\u0006\n\u0000\u001a\u0010\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u0003H\u0007\u001a\b\u0010\u0004\u001a\u00020\u0001H\u0007\u001a\u001a\u0010\u0005\u001a\u00020\u00012\b\u0010\u0006\u001a\u0004\u0018\u00010\u00072\u0006\u0010\b\u001a\u00020\tH\u0007\u001a \u0010\n\u001a\u00020\u00012\u0006\u0010\u000b\u001a\u00020\u00072\u0006\u0010\f\u001a\u00020\u00072\u0006\u0010\r\u001a\u00020\u000eH\u0007\u001a\u0018\u0010\u000f\u001a\u00020\u00012\u0006\u0010\u0010\u001a\u00020\u00112\u0006\u0010\b\u001a\u00020\tH\u0007\u001a\u0010\u0010\u0012\u001a\u00020\u00012\u0006\u0010\u0013\u001a\u00020\u0007H\u0007\u001a(\u0010\u0014\u001a\u00020\u00012\u0006\u0010\u0015\u001a\u00020\u000e2\u0006\u0010\u0016\u001a\u00020\u00172\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\u0018\u001a\u00020\u0019H\u0007\u001a\b\u0010\u001a\u001a\u00020\u0001H\u0007\u001a \u0010\u001b\u001a\u00020\u00012\u0006\u0010\u001c\u001a\u00020\u000e2\u0006\u0010\u0013\u001a\u00020\u00072\u0006\u0010\u001d\u001a\u00020\u0007H\u0007\u001a\b\u0010\u001e\u001a\u00020\u0001H\u0007\u001a\b\u0010\u001f\u001a\u00020\u0001H\u0007\u001a\u0010\u0010 \u001a\u00020\u00012\u0006\u0010!\u001a\u00020\"H\u0007\u00a8\u0006#"}, d2 = {"ClientDetails", "", "clientDetails", "Lcom/manaknight/app/model/remote/profitPro/ClientDetailRespModel;", "DueText", "HeaderCard", "name", "", "navController", "Landroidx/navigation/NavController;", "InvoiceCard", "invoiceNumber", "create_at", "status", "", "InvoiceContent", "apiResponse", "Lcom/manaknight/app/model/remote/profitPro/AllLineItemsResponseModel;", "InvoiceDescription", "description", "InvoiceScreen", "projectId", "baasViewModel", "Lcom/manaknight/app/viewmodels/BaasViewModel;", "dialog", "Landroid/app/Dialog;", "JobDetailsList", "JobDetailsRow", "itemNumber", "amount", "PreviewInvoiceScreen", "PreviewJobDetailsList", "TotalAmount", "totalAmount", "", "app_debug"})
public final class InvoiceScreenKt {
    
    @androidx.compose.runtime.Composable()
    public static final void InvoiceScreen(int projectId, @org.jetbrains.annotations.NotNull()
    com.manaknight.app.viewmodels.BaasViewModel baasViewModel, @org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController, @org.jetbrains.annotations.NotNull()
    android.app.Dialog dialog) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void InvoiceContent(@org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.AllLineItemsResponseModel apiResponse, @org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void HeaderCard(@org.jetbrains.annotations.Nullable()
    java.lang.String name, @org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void InvoiceCard(@org.jetbrains.annotations.NotNull()
    java.lang.String invoiceNumber, @org.jetbrains.annotations.NotNull()
    java.lang.String create_at, int status) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void ClientDetails(@org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.ClientDetailRespModel clientDetails) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void InvoiceDescription(@org.jetbrains.annotations.NotNull()
    java.lang.String description) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void TotalAmount(double totalAmount) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void DueText() {
    }
    
    @androidx.compose.ui.tooling.preview.Preview(showBackground = true)
    @androidx.compose.runtime.Composable()
    public static final void PreviewInvoiceScreen() {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void JobDetailsRow(int itemNumber, @org.jetbrains.annotations.NotNull()
    java.lang.String description, @org.jetbrains.annotations.NotNull()
    java.lang.String amount) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void JobDetailsList() {
    }
    
    @androidx.compose.ui.tooling.preview.Preview(showBackground = true)
    @androidx.compose.runtime.Composable()
    public static final void PreviewJobDetailsList() {
    }
}