package com.manaknight.app.ui.fragments

//package com.manaknight.app.ui.fragments.home

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.foundation.layout.Column

import androidx.compose.ui.platform.ComposeView
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.manaknight.app.ui.screens.LineItemsScreen
import com.manaknight.app.utils.ProgressDialog.Companion.progressDialog
import org.koin.androidx.viewmodel.ext.android.viewModel
import org.koin.android.ext.android.inject
import Manaknight.R
import android.app.Dialog
import androidx.compose.foundation.BorderStroke
import androidx.fragment.app.activityViewModels
import com.manaknight.app.extensions.snackBar
import com.manaknight.app.network.Status

import com.manaknight.app.viewmodels.BaasViewModel

class LineItemsComposeFragment : Fragment() {

    private val baasViewModel: BaasViewModel by viewModel() // Changed to activityViewModels()
    private val args by navArgs<LineItemsComposeFragmentArgs>()
    private lateinit var dialog: Dialog
    private val pref by inject<com.manaknight.app.data.local.AppPreferences>()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        dialog = progressDialog(requireContext())
        return ComposeView(requireContext()).apply {
            setContent {
                LineItemsScreen(
                    projectID = args.projectID,
                    baasViewModel = baasViewModel,
                    navController = findNavController(),
                    dialog = dialog,
                    onNavigateToPreview = { projectID ->
                        val action = LineItemsComposeFragmentDirections.actionLineItemViewComposeToPreviewProjectDetailsFragment(projectID)
                        findNavController().navigate(action)
                    },
                    deleteItem={id-> deleteItem(id)},
                    onSaveCredit={amount, reason-> handleSaveCredit(amount, reason)}
                )
            }
        }
    }

    private fun handleSaveCredit(amount: Double, reason: String) {
//        projectId = args.projectID,
        baasViewModel.createDraw(  amount = amount.toString(), description = reason)
            .observe(viewLifecycleOwner) {
                when (it.status) {
                    Status.ERROR -> {

                            snackBar("Server Error")

                        dialog.dismiss()
                    }
                    Status.LOADING -> {
                        dialog.show()
                    }

                    Status.SUCCESS -> {
                        dialog.dismiss()
                        snackBar("Draw created successfully.")
//                        getAllLineItem()
                    }
                }
            }
    }




    private fun deleteItem(id: Int) {

        baasViewModel.deleteLineItems(id)
            .observe(viewLifecycleOwner) {
                when (it.status) {
                    Status.ERROR -> {
                        //snackBar("Server Error")
                        if (it.message == "Draws already initialized") {
//                            moveToDrawScreen()
                        } else {
                            snackBar("Server Error")
                        }
                        dialog.dismiss()
                    }
                    Status.LOADING -> {
                        dialog.show()
                    }

                    Status.SUCCESS -> {
                        dialog.dismiss()
                        snackBar("Item deleted successfully.")
//                        getAllLineItem()
                    }
                }
            }
    }


}