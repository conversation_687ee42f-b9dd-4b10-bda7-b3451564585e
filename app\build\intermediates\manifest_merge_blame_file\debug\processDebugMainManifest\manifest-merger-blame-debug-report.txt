1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.manaknight.app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="28"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
11-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:6:5-76
11-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:6:22-74
12    <uses-permission android:name="android.permission.VIBRATE" />
12-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:7:5-66
12-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:7:22-63
13    <uses-permission android:name="android.permission.INTERNET" />
13-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:8:5-67
13-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:8:22-64
14    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
14-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:9:5-79
14-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:9:22-76
15    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
15-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:10:5-79
15-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:10:22-76
16    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
16-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:11:5-81
16-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:11:22-78
17    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
17-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:12:5-76
17-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:12:22-73
18    <uses-permission android:name="android.permission.CAMERA" />
18-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:13:5-65
18-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:13:22-62
19    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
19-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:14:5-80
19-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:14:22-78
20    <uses-permission android:name="android.permission.ACTIVITY_RECOGNITION" />
20-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:15:5-79
20-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:15:22-76
21    <uses-permission android:name="android.permission.WAKE_LOCK" />
21-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:16:5-68
21-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:16:22-65
22    <uses-permission android:name="android.permission.ACTIVITY_RECOGNITION" />
22-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:15:5-79
22-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:15:22-76
23    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
23-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:18:5-81
23-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:18:22-78
24    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
24-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:19:5-80
24-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:19:22-77
25    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
25-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:20:5-81
25-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:20:22-79
26    <uses-permission android:name="com.android.vending.BILLING" />
26-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:21:5-67
26-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:21:22-64
27
28    <queries>
28-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:26:5-28:15
29        <package android:name="com.facebook.katana" />
29-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:27:9-55
29-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:27:18-52
30        <!-- Needs to be explicitly declared on Android R+ -->
31        <package android:name="com.google.android.apps.maps" />
31-->[com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bca5bb2c25cf61ac91ecd47130eaf591\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:33:9-64
31-->[com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bca5bb2c25cf61ac91ecd47130eaf591\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:33:18-61
32
33        <intent>
33-->[androidx.camera:camera-extensions:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\54b9491a3c7a79413b727cd19f2cf1e8\transformed\jetified-camera-extensions-1.2.2\AndroidManifest.xml:23:9-25:18
34            <action android:name="androidx.camera.extensions.action.VENDOR_ACTION" />
34-->[androidx.camera:camera-extensions:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\54b9491a3c7a79413b727cd19f2cf1e8\transformed\jetified-camera-extensions-1.2.2\AndroidManifest.xml:24:13-86
34-->[androidx.camera:camera-extensions:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\54b9491a3c7a79413b727cd19f2cf1e8\transformed\jetified-camera-extensions-1.2.2\AndroidManifest.xml:24:21-83
35        </intent>
36        <intent>
36-->[com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\23890166d6a8b14dee3091bd6ffdea5c\transformed\jetified-billing-6.0.1\AndroidManifest.xml:13:9-15:18
37            <action android:name="com.android.vending.billing.InAppBillingService.BIND" />
37-->[com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\23890166d6a8b14dee3091bd6ffdea5c\transformed\jetified-billing-6.0.1\AndroidManifest.xml:14:13-91
37-->[com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\23890166d6a8b14dee3091bd6ffdea5c\transformed\jetified-billing-6.0.1\AndroidManifest.xml:14:21-88
38        </intent>
39    </queries>
40
41    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
41-->[com.google.android.libraries.places:places:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\245b102fe60f0031ab708afcac9ce0b9\transformed\jetified-places-3.0.0\AndroidManifest.xml:11:5-76
41-->[com.google.android.libraries.places:places:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\245b102fe60f0031ab708afcac9ce0b9\transformed\jetified-places-3.0.0\AndroidManifest.xml:11:22-73
42
43    <uses-feature
43-->[com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bca5bb2c25cf61ac91ecd47130eaf591\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:26:5-28:35
44        android:glEsVersion="0x00020000"
44-->[com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bca5bb2c25cf61ac91ecd47130eaf591\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:27:9-41
45        android:required="true" /> <!-- Required by older versions of Google Play services to create IID tokens -->
45-->[com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bca5bb2c25cf61ac91ecd47130eaf591\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:28:9-32
46    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
46-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:28:5-82
46-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:28:22-79
47    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
47-->[com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\168037942f327435dcf9fbba3d0dde22\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:25:5-79
47-->[com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\168037942f327435dcf9fbba3d0dde22\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:25:22-76
48    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
48-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:26:5-110
48-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:26:22-107
49
50    <permission
50-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b7543783d7e597efaf2f46cf5f3847\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
51        android:name="com.manaknight.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
51-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b7543783d7e597efaf2f46cf5f3847\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
52        android:protectionLevel="signature" />
52-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b7543783d7e597efaf2f46cf5f3847\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
53
54    <uses-permission android:name="com.manaknight.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
54-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b7543783d7e597efaf2f46cf5f3847\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
54-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b7543783d7e597efaf2f46cf5f3847\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
55
56    <application
56-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:30:5-84:19
57        android:name="com.manaknight.app.App"
57-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:31:9-46
58        android:allowBackup="false"
58-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:32:9-36
59        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
59-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b7543783d7e597efaf2f46cf5f3847\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
60        android:dataExtractionRules="@xml/data_extraction_rules"
60-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:33:9-65
61        android:debuggable="true"
62        android:extractNativeLibs="false"
63        android:fullBackupContent="@xml/backup_rules"
63-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:34:9-54
64        android:icon="@mipmap/ic_launcher"
64-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:35:9-43
65        android:label="@string/app_name"
65-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:36:9-41
66        android:requestLegacyExternalStorage="true"
66-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:39:9-52
67        android:roundIcon="@mipmap/ic_launcher_round"
67-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:37:9-54
68        android:supportsRtl="true"
68-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:38:9-35
69        android:testOnly="true"
70        android:theme="@style/AppTheme"
70-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:40:9-40
71        android:usesCleartextTraffic="true" >
71-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:41:9-44
72        <activity
72-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:43:9-60:20
73            android:name="com.manaknight.app.MainActivity"
73-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:44:13-59
74            android:exported="true"
74-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:45:13-36
75            android:hardwareAccelerated="true"
75-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:49:13-47
76            android:launchMode="singleTask"
76-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:50:13-44
77            android:screenOrientation="portrait"
77-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:46:13-49
78            android:windowSoftInputMode="adjustResize" >
78-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:48:13-55
79            <intent-filter>
79-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:51:13-55:29
80                <action android:name="android.intent.action.MAIN" />
80-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:52:17-69
80-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:52:25-66
81
82                <category android:name="android.intent.category.LAUNCHER" />
82-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:54:17-77
82-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:54:27-74
83            </intent-filter>
84
85            <meta-data
85-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:57:13-59:36
86                android:name="android.app.lib_name"
86-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:58:17-52
87                android:value="" />
87-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:59:17-33
88        </activity>
89
90        <meta-data
90-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:62:9-64:58
91            android:name="com.google.firebase.messaging.default_notification_icon"
91-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:63:13-83
92            android:resource="@drawable/ic_loc_active" />
92-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:64:13-55
93
94        <!-- <provider -->
95        <!-- android:name="androidx.core.content.FileProvider" -->
96        <!-- android:authorities="com.manaknight.app.provider" -->
97        <!-- android:exported="false" -->
98        <!-- android:grantUriPermissions="true"> -->
99        <!-- <meta-data -->
100        <!-- android:name="android.support.FILE_PROVIDER_PATHS" -->
101        <!-- android:resource="@xml/file_provider" /> -->
102        <!-- </provider> -->
103
104        <service
104-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:76:9-83:19
105            android:name="com.manaknight.app.fcm.MyFirebasePushNotifications"
105-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:77:13-78
106            android:exported="false" >
106-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:78:13-37
107            <intent-filter>
107-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:80:13-82:29
108                <action android:name="com.google.firebase.MESSAGING_EVENT" />
108-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:81:17-78
108-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:81:25-75
109            </intent-filter>
110        </service>
111
112        <activity
112-->[com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:12:9-14:57
113            android:name="com.stripe.android.view.AddPaymentMethodActivity"
113-->[com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:13:13-76
114            android:theme="@style/StripeDefaultTheme" />
114-->[com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:14:13-54
115        <activity
115-->[com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:15:9-17:57
116            android:name="com.stripe.android.view.PaymentMethodsActivity"
116-->[com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:16:13-74
117            android:theme="@style/StripeDefaultTheme" />
117-->[com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:17:13-54
118        <activity
118-->[com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:18:9-20:57
119            android:name="com.stripe.android.view.PaymentFlowActivity"
119-->[com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:19:13-71
120            android:theme="@style/StripeDefaultTheme" />
120-->[com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:20:13-54
121        <activity
121-->[com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:21:9-23:57
122            android:name="com.stripe.android.view.PaymentAuthWebViewActivity"
122-->[com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:22:13-78
123            android:theme="@style/StripeDefaultTheme" />
123-->[com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:23:13-54
124        <activity
124-->[com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:24:9-26:57
125            android:name="com.stripe.android.view.PaymentRelayActivity"
125-->[com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:25:13-72
126            android:theme="@style/StripeDefaultTheme" />
126-->[com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:26:13-54
127        <activity
127-->[com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:27:9-29:57
128            android:name="com.stripe.android.view.Stripe3ds2CompletionActivity"
128-->[com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:28:13-80
129            android:theme="@style/StripeDefaultTheme" />
129-->[com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:29:13-54
130        <activity
130-->[com.stripe:stripe-3ds2-android:2.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\7f344a36bb06c1b19b3d8dc344e8ef3c\transformed\jetified-stripe-3ds2-android-2.7.2\AndroidManifest.xml:12:9-14:54
131            android:name="com.stripe.android.stripe3ds2.views.ChallengeActivity"
131-->[com.stripe:stripe-3ds2-android:2.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\7f344a36bb06c1b19b3d8dc344e8ef3c\transformed\jetified-stripe-3ds2-android-2.7.2\AndroidManifest.xml:13:13-81
132            android:theme="@style/Stripe3DS2Theme" />
132-->[com.stripe:stripe-3ds2-android:2.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\7f344a36bb06c1b19b3d8dc344e8ef3c\transformed\jetified-stripe-3ds2-android-2.7.2\AndroidManifest.xml:14:13-51
133        <activity
133-->[com.stripe:stripe-3ds2-android:2.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\7f344a36bb06c1b19b3d8dc344e8ef3c\transformed\jetified-stripe-3ds2-android-2.7.2\AndroidManifest.xml:15:9-17:54
134            android:name="com.stripe.android.stripe3ds2.views.ChallengeProgressDialogActivity"
134-->[com.stripe:stripe-3ds2-android:2.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\7f344a36bb06c1b19b3d8dc344e8ef3c\transformed\jetified-stripe-3ds2-android-2.7.2\AndroidManifest.xml:16:13-95
135            android:theme="@style/Stripe3DS2Theme" />
135-->[com.stripe:stripe-3ds2-android:2.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\7f344a36bb06c1b19b3d8dc344e8ef3c\transformed\jetified-stripe-3ds2-android-2.7.2\AndroidManifest.xml:17:13-51
136        <activity
136-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\dfce37622a563a24f0c8e9b3351fbc6a\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:27:9-29:72
137            android:name="com.karumi.dexter.DexterActivity"
137-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\dfce37622a563a24f0c8e9b3351fbc6a\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:28:13-60
138            android:theme="@style/Dexter.Internal.Theme.Transparent" />
138-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\dfce37622a563a24f0c8e9b3351fbc6a\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:29:13-69
139        <activity
139-->[com.google.android.libraries.places:places:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\245b102fe60f0031ab708afcac9ce0b9\transformed\jetified-places-3.0.0\AndroidManifest.xml:15:9-21:20
140            android:name="com.google.android.libraries.places.widget.AutocompleteActivity"
140-->[com.google.android.libraries.places:places:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\245b102fe60f0031ab708afcac9ce0b9\transformed\jetified-places-3.0.0\AndroidManifest.xml:16:13-91
141            android:exported="false"
141-->[com.google.android.libraries.places:places:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\245b102fe60f0031ab708afcac9ce0b9\transformed\jetified-places-3.0.0\AndroidManifest.xml:17:13-37
142            android:label="@string/places_autocomplete_label"
142-->[com.google.android.libraries.places:places:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\245b102fe60f0031ab708afcac9ce0b9\transformed\jetified-places-3.0.0\AndroidManifest.xml:18:13-62
143            android:theme="@style/PlacesAutocompleteOverlay"
143-->[com.google.android.libraries.places:places:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\245b102fe60f0031ab708afcac9ce0b9\transformed\jetified-places-3.0.0\AndroidManifest.xml:19:13-61
144            android:windowSoftInputMode="adjustResize" >
144-->[com.google.android.libraries.places:places:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\245b102fe60f0031ab708afcac9ce0b9\transformed\jetified-places-3.0.0\AndroidManifest.xml:20:13-55
145        </activity>
146        <activity
146-->[com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f6fa1e240580d875b9faf8d016b434a\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:23:9-27:75
147            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
147-->[com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f6fa1e240580d875b9faf8d016b434a\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:24:13-93
148            android:excludeFromRecents="true"
148-->[com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f6fa1e240580d875b9faf8d016b434a\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:25:13-46
149            android:exported="false"
149-->[com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f6fa1e240580d875b9faf8d016b434a\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:26:13-37
150            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
150-->[com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f6fa1e240580d875b9faf8d016b434a\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:27:13-72
151        <!--
152            Service handling Google Sign-In user revocation. For apps that do not integrate with
153            Google Sign-In, this service will never be started.
154        -->
155        <service
155-->[com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f6fa1e240580d875b9faf8d016b434a\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:33:9-37:51
156            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
156-->[com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f6fa1e240580d875b9faf8d016b434a\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:34:13-89
157            android:exported="true"
157-->[com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f6fa1e240580d875b9faf8d016b434a\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:35:13-36
158            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
158-->[com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f6fa1e240580d875b9faf8d016b434a\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:36:13-107
159            android:visibleToInstantApps="true" /> <!-- Needs to be explicitly declared on P+ -->
159-->[com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f6fa1e240580d875b9faf8d016b434a\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:37:13-48
160        <uses-library
160-->[com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bca5bb2c25cf61ac91ecd47130eaf591\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:39:9-41:40
161            android:name="org.apache.http.legacy"
161-->[com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bca5bb2c25cf61ac91ecd47130eaf591\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:40:13-50
162            android:required="false" />
162-->[com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bca5bb2c25cf61ac91ecd47130eaf591\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:41:13-37
163
164        <meta-data
164-->[com.google.android.gms:play-services-fitness:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4018ae0a1966300199c2b3e05a6d81e9\transformed\jetified-play-services-fitness-20.0.0\AndroidManifest.xml:23:9-25:38
165            android:name="com.google.gms.fitness.sdk.version"
165-->[com.google.android.gms:play-services-fitness:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4018ae0a1966300199c2b3e05a6d81e9\transformed\jetified-play-services-fitness-20.0.0\AndroidManifest.xml:24:13-62
166            android:value="20.0.0" />
166-->[com.google.android.gms:play-services-fitness:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4018ae0a1966300199c2b3e05a6d81e9\transformed\jetified-play-services-fitness-20.0.0\AndroidManifest.xml:25:13-35
167
168        <service
168-->[com.google.android.gms:play-services-mlkit-text-recognition-common:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b4bd5f6b063655bfefc8564cd709cc40\transformed\jetified-play-services-mlkit-text-recognition-common-18.0.0\AndroidManifest.xml:9:9-15:19
169            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
169-->[com.google.android.gms:play-services-mlkit-text-recognition-common:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b4bd5f6b063655bfefc8564cd709cc40\transformed\jetified-play-services-mlkit-text-recognition-common-18.0.0\AndroidManifest.xml:10:13-91
170            android:directBootAware="true"
170-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c505a1ac40223f3f4347c0c22e87fce\transformed\jetified-common-18.5.0\AndroidManifest.xml:17:13-43
171            android:exported="false" >
171-->[com.google.android.gms:play-services-mlkit-text-recognition-common:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b4bd5f6b063655bfefc8564cd709cc40\transformed\jetified-play-services-mlkit-text-recognition-common-18.0.0\AndroidManifest.xml:11:13-37
172            <meta-data
172-->[com.google.android.gms:play-services-mlkit-text-recognition-common:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b4bd5f6b063655bfefc8564cd709cc40\transformed\jetified-play-services-mlkit-text-recognition-common-18.0.0\AndroidManifest.xml:12:13-14:85
173                android:name="com.google.firebase.components:com.google.mlkit.vision.text.internal.TextRegistrar"
173-->[com.google.android.gms:play-services-mlkit-text-recognition-common:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b4bd5f6b063655bfefc8564cd709cc40\transformed\jetified-play-services-mlkit-text-recognition-common-18.0.0\AndroidManifest.xml:13:17-114
174                android:value="com.google.firebase.components.ComponentRegistrar" />
174-->[com.google.android.gms:play-services-mlkit-text-recognition-common:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b4bd5f6b063655bfefc8564cd709cc40\transformed\jetified-play-services-mlkit-text-recognition-common-18.0.0\AndroidManifest.xml:14:17-82
175            <meta-data
175-->[com.google.mlkit:vision-common:17.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c3691d46c450c8405e6a322b681b222a\transformed\jetified-vision-common-17.2.1\AndroidManifest.xml:12:13-14:85
176                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
176-->[com.google.mlkit:vision-common:17.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c3691d46c450c8405e6a322b681b222a\transformed\jetified-vision-common-17.2.1\AndroidManifest.xml:13:17-124
177                android:value="com.google.firebase.components.ComponentRegistrar" />
177-->[com.google.mlkit:vision-common:17.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c3691d46c450c8405e6a322b681b222a\transformed\jetified-vision-common-17.2.1\AndroidManifest.xml:14:17-82
178            <meta-data
178-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c505a1ac40223f3f4347c0c22e87fce\transformed\jetified-common-18.5.0\AndroidManifest.xml:20:13-22:85
179                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
179-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c505a1ac40223f3f4347c0c22e87fce\transformed\jetified-common-18.5.0\AndroidManifest.xml:21:17-120
180                android:value="com.google.firebase.components.ComponentRegistrar" />
180-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c505a1ac40223f3f4347c0c22e87fce\transformed\jetified-common-18.5.0\AndroidManifest.xml:22:17-82
181        </service>
182
183        <provider
183-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c505a1ac40223f3f4347c0c22e87fce\transformed\jetified-common-18.5.0\AndroidManifest.xml:9:9-13:38
184            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
184-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c505a1ac40223f3f4347c0c22e87fce\transformed\jetified-common-18.5.0\AndroidManifest.xml:10:13-78
185            android:authorities="com.manaknight.app.mlkitinitprovider"
185-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c505a1ac40223f3f4347c0c22e87fce\transformed\jetified-common-18.5.0\AndroidManifest.xml:11:13-69
186            android:exported="false"
186-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c505a1ac40223f3f4347c0c22e87fce\transformed\jetified-common-18.5.0\AndroidManifest.xml:12:13-37
187            android:initOrder="99" />
187-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c505a1ac40223f3f4347c0c22e87fce\transformed\jetified-common-18.5.0\AndroidManifest.xml:13:13-35
188
189        <service
189-->[com.google.firebase:firebase-auth-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\15d4f429e740d2d9a84daaaefe71c783\transformed\jetified-firebase-auth-ktx-21.1.0\AndroidManifest.xml:8:9-14:19
190            android:name="com.google.firebase.components.ComponentDiscoveryService"
190-->[com.google.firebase:firebase-auth-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\15d4f429e740d2d9a84daaaefe71c783\transformed\jetified-firebase-auth-ktx-21.1.0\AndroidManifest.xml:9:13-84
191            android:directBootAware="true"
191-->[com.google.firebase:firebase-common:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\422e0883ce3188039e67d46c46566547\transformed\jetified-firebase-common-20.3.0\AndroidManifest.xml:34:13-43
192            android:exported="false" >
192-->[com.google.firebase:firebase-auth-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\15d4f429e740d2d9a84daaaefe71c783\transformed\jetified-firebase-auth-ktx-21.1.0\AndroidManifest.xml:10:13-37
193            <meta-data
193-->[com.google.firebase:firebase-auth-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\15d4f429e740d2d9a84daaaefe71c783\transformed\jetified-firebase-auth-ktx-21.1.0\AndroidManifest.xml:11:13-13:85
194                android:name="com.google.firebase.components:com.google.firebase.auth.ktx.FirebaseAuthKtxRegistrar"
194-->[com.google.firebase:firebase-auth-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\15d4f429e740d2d9a84daaaefe71c783\transformed\jetified-firebase-auth-ktx-21.1.0\AndroidManifest.xml:12:17-116
195                android:value="com.google.firebase.components.ComponentRegistrar" />
195-->[com.google.firebase:firebase-auth-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\15d4f429e740d2d9a84daaaefe71c783\transformed\jetified-firebase-auth-ktx-21.1.0\AndroidManifest.xml:13:17-82
196            <meta-data
196-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:67:13-69:85
197                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
197-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:68:17-109
198                android:value="com.google.firebase.components.ComponentRegistrar" />
198-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:69:17-82
199            <meta-data
199-->[com.google.firebase:firebase-messaging-ktx:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\c80935087eb2baf2ce8d5fda4c1695fb\transformed\jetified-firebase-messaging-ktx-23.1.1\AndroidManifest.xml:28:13-30:85
200                android:name="com.google.firebase.components:com.google.firebase.messaging.ktx.FirebaseMessagingKtxRegistrar"
200-->[com.google.firebase:firebase-messaging-ktx:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\c80935087eb2baf2ce8d5fda4c1695fb\transformed\jetified-firebase-messaging-ktx-23.1.1\AndroidManifest.xml:29:17-126
201                android:value="com.google.firebase.components.ComponentRegistrar" />
201-->[com.google.firebase:firebase-messaging-ktx:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\c80935087eb2baf2ce8d5fda4c1695fb\transformed\jetified-firebase-messaging-ktx-23.1.1\AndroidManifest.xml:30:17-82
202            <meta-data
202-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:55:13-57:85
203                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
203-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:56:17-119
204                android:value="com.google.firebase.components.ComponentRegistrar" />
204-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:57:17-82
205            <meta-data
205-->[com.google.firebase:firebase-crashlytics-ktx:18.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\7a08c1f471d4aed10f5a62d4bdfd7cc0\transformed\jetified-firebase-crashlytics-ktx-18.3.5\AndroidManifest.xml:26:13-28:85
206                android:name="com.google.firebase.components:com.google.firebase.crashlytics.ktx.FirebaseCrashlyticsKtxRegistrar"
206-->[com.google.firebase:firebase-crashlytics-ktx:18.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\7a08c1f471d4aed10f5a62d4bdfd7cc0\transformed\jetified-firebase-crashlytics-ktx-18.3.5\AndroidManifest.xml:27:17-130
207                android:value="com.google.firebase.components.ComponentRegistrar" />
207-->[com.google.firebase:firebase-crashlytics-ktx:18.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\7a08c1f471d4aed10f5a62d4bdfd7cc0\transformed\jetified-firebase-crashlytics-ktx-18.3.5\AndroidManifest.xml:28:17-82
208            <meta-data
208-->[com.google.firebase:firebase-analytics-ktx:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d0398be7ec17550451ee5b14d880cfc\transformed\jetified-firebase-analytics-ktx-21.2.0\AndroidManifest.xml:11:13-13:85
209                android:name="com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsKtxRegistrar"
209-->[com.google.firebase:firebase-analytics-ktx:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d0398be7ec17550451ee5b14d880cfc\transformed\jetified-firebase-analytics-ktx-21.2.0\AndroidManifest.xml:12:17-126
210                android:value="com.google.firebase.components.ComponentRegistrar" />
210-->[com.google.firebase:firebase-analytics-ktx:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d0398be7ec17550451ee5b14d880cfc\transformed\jetified-firebase-analytics-ktx-21.2.0\AndroidManifest.xml:13:17-82
211            <meta-data
211-->[com.google.firebase:firebase-common-ktx:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b2a903c9b0acab1e7698691d1a2c986\transformed\jetified-firebase-common-ktx-20.3.0\AndroidManifest.xml:14:13-16:85
212                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonKtxRegistrar"
212-->[com.google.firebase:firebase-common-ktx:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b2a903c9b0acab1e7698691d1a2c986\transformed\jetified-firebase-common-ktx-20.3.0\AndroidManifest.xml:15:17-113
213                android:value="com.google.firebase.components.ComponentRegistrar" />
213-->[com.google.firebase:firebase-common-ktx:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b2a903c9b0acab1e7698691d1a2c986\transformed\jetified-firebase-common-ktx-20.3.0\AndroidManifest.xml:16:17-82
214            <meta-data
214-->[com.google.firebase:firebase-crashlytics:18.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\967d8355743d170a894a29b4b1a80382\transformed\jetified-firebase-crashlytics-18.3.5\AndroidManifest.xml:17:13-19:85
215                android:name="com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar"
215-->[com.google.firebase:firebase-crashlytics:18.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\967d8355743d170a894a29b4b1a80382\transformed\jetified-firebase-crashlytics-18.3.5\AndroidManifest.xml:18:17-115
216                android:value="com.google.firebase.components.ComponentRegistrar" />
216-->[com.google.firebase:firebase-crashlytics:18.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\967d8355743d170a894a29b4b1a80382\transformed\jetified-firebase-crashlytics-18.3.5\AndroidManifest.xml:19:17-82
217            <meta-data
217-->[com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\168037942f327435dcf9fbba3d0dde22\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:31:13-33:85
218                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
218-->[com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\168037942f327435dcf9fbba3d0dde22\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:32:17-139
219                android:value="com.google.firebase.components.ComponentRegistrar" />
219-->[com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\168037942f327435dcf9fbba3d0dde22\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:33:17-82
220            <meta-data
220-->[com.google.firebase:firebase-installations:17.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\f175c9d0e7eca696c235389c9c14325d\transformed\jetified-firebase-installations-17.1.2\AndroidManifest.xml:17:13-19:85
221                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
221-->[com.google.firebase:firebase-installations:17.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\f175c9d0e7eca696c235389c9c14325d\transformed\jetified-firebase-installations-17.1.2\AndroidManifest.xml:18:17-127
222                android:value="com.google.firebase.components.ComponentRegistrar" />
222-->[com.google.firebase:firebase-installations:17.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\f175c9d0e7eca696c235389c9c14325d\transformed\jetified-firebase-installations-17.1.2\AndroidManifest.xml:19:17-82
223            <meta-data
223-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\b6cf1c5e33857039c8761daab9336c00\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:27:13-29:85
224                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
224-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\b6cf1c5e33857039c8761daab9336c00\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:28:17-115
225                android:value="com.google.firebase.components.ComponentRegistrar" />
225-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\b6cf1c5e33857039c8761daab9336c00\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:29:17-82
226        </service>
227
228        <activity
228-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:27:9-44:20
229            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
229-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:28:13-80
230            android:excludeFromRecents="true"
230-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:29:13-46
231            android:exported="true"
231-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:30:13-36
232            android:launchMode="singleTask"
232-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:31:13-44
233            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
233-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:32:13-72
234            <intent-filter>
234-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:33:13-43:29
235                <action android:name="android.intent.action.VIEW" />
235-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:34:17-69
235-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:34:25-66
236
237                <category android:name="android.intent.category.DEFAULT" />
237-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:36:17-76
237-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:36:27-73
238                <category android:name="android.intent.category.BROWSABLE" />
238-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:37:17-78
238-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:37:27-75
239
240                <data
240-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:39:17-42:51
241                    android:host="firebase.auth"
241-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:40:21-49
242                    android:path="/"
242-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:41:21-37
243                    android:scheme="genericidp" />
243-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:42:21-48
244            </intent-filter>
245        </activity>
246        <activity
246-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:45:9-62:20
247            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
247-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:46:13-79
248            android:excludeFromRecents="true"
248-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:47:13-46
249            android:exported="true"
249-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:48:13-36
250            android:launchMode="singleTask"
250-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:49:13-44
251            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
251-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:50:13-72
252            <intent-filter>
252-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:51:13-61:29
253                <action android:name="android.intent.action.VIEW" />
253-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:34:17-69
253-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:34:25-66
254
255                <category android:name="android.intent.category.DEFAULT" />
255-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:36:17-76
255-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:36:27-73
256                <category android:name="android.intent.category.BROWSABLE" />
256-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:37:17-78
256-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:37:27-75
257
258                <data
258-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:39:17-42:51
259                    android:host="firebase.auth"
259-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:40:21-49
260                    android:path="/"
260-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:41:21-37
261                    android:scheme="recaptcha" />
261-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:42:21-48
262            </intent-filter>
263        </activity>
264
265        <receiver
265-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:31:9-38:20
266            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
266-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:32:13-78
267            android:exported="true"
267-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:33:13-36
268            android:permission="com.google.android.c2dm.permission.SEND" >
268-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:34:13-73
269            <intent-filter>
269-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:35:13-37:29
270                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
270-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:36:17-81
270-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:36:25-78
271            </intent-filter>
272        </receiver>
273        <!--
274             FirebaseMessagingService performs security checks at runtime,
275             but set to not exported to explicitly avoid allowing another app to call it.
276        -->
277        <service
277-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:44:9-51:19
278            android:name="com.google.firebase.messaging.FirebaseMessagingService"
278-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:45:13-82
279            android:directBootAware="true"
279-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:46:13-43
280            android:exported="false" >
280-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:47:13-37
281            <intent-filter android:priority="-500" >
281-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:80:13-82:29
282                <action android:name="com.google.firebase.MESSAGING_EVENT" />
282-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:81:17-78
282-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:81:25-75
283            </intent-filter>
284        </service>
285
286        <activity
286-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5296fb8d536108245faa30c6d8197de1\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
287            android:name="com.google.android.gms.common.api.GoogleApiActivity"
287-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5296fb8d536108245faa30c6d8197de1\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:19-85
288            android:exported="false"
288-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5296fb8d536108245faa30c6d8197de1\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:22:19-43
289            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
289-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5296fb8d536108245faa30c6d8197de1\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:21:19-78
290
291        <provider
291-->[com.google.firebase:firebase-common:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\422e0883ce3188039e67d46c46566547\transformed\jetified-firebase-common-20.3.0\AndroidManifest.xml:25:9-30:39
292            android:name="com.google.firebase.provider.FirebaseInitProvider"
292-->[com.google.firebase:firebase-common:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\422e0883ce3188039e67d46c46566547\transformed\jetified-firebase-common-20.3.0\AndroidManifest.xml:26:13-77
293            android:authorities="com.manaknight.app.firebaseinitprovider"
293-->[com.google.firebase:firebase-common:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\422e0883ce3188039e67d46c46566547\transformed\jetified-firebase-common-20.3.0\AndroidManifest.xml:27:13-72
294            android:directBootAware="true"
294-->[com.google.firebase:firebase-common:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\422e0883ce3188039e67d46c46566547\transformed\jetified-firebase-common-20.3.0\AndroidManifest.xml:28:13-43
295            android:exported="false"
295-->[com.google.firebase:firebase-common:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\422e0883ce3188039e67d46c46566547\transformed\jetified-firebase-common-20.3.0\AndroidManifest.xml:29:13-37
296            android:initOrder="100" />
296-->[com.google.firebase:firebase-common:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\422e0883ce3188039e67d46c46566547\transformed\jetified-firebase-common-20.3.0\AndroidManifest.xml:30:13-36
297
298        <uses-library
298-->[androidx.camera:camera-extensions:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\54b9491a3c7a79413b727cd19f2cf1e8\transformed\jetified-camera-extensions-1.2.2\AndroidManifest.xml:29:9-31:40
299            android:name="androidx.camera.extensions.impl"
299-->[androidx.camera:camera-extensions:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\54b9491a3c7a79413b727cd19f2cf1e8\transformed\jetified-camera-extensions-1.2.2\AndroidManifest.xml:30:13-59
300            android:required="false" />
300-->[androidx.camera:camera-extensions:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\54b9491a3c7a79413b727cd19f2cf1e8\transformed\jetified-camera-extensions-1.2.2\AndroidManifest.xml:31:13-37
301
302        <service
302-->[androidx.camera:camera-camera2:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\ee28ca93c07bf81d90ff8f6019728918\transformed\jetified-camera-camera2-1.2.2\AndroidManifest.xml:24:9-33:19
303            android:name="androidx.camera.core.impl.MetadataHolderService"
303-->[androidx.camera:camera-camera2:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\ee28ca93c07bf81d90ff8f6019728918\transformed\jetified-camera-camera2-1.2.2\AndroidManifest.xml:25:13-75
304            android:enabled="false"
304-->[androidx.camera:camera-camera2:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\ee28ca93c07bf81d90ff8f6019728918\transformed\jetified-camera-camera2-1.2.2\AndroidManifest.xml:26:13-36
305            android:exported="false" >
305-->[androidx.camera:camera-camera2:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\ee28ca93c07bf81d90ff8f6019728918\transformed\jetified-camera-camera2-1.2.2\AndroidManifest.xml:27:13-37
306            <meta-data
306-->[androidx.camera:camera-camera2:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\ee28ca93c07bf81d90ff8f6019728918\transformed\jetified-camera-camera2-1.2.2\AndroidManifest.xml:30:13-32:89
307                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
307-->[androidx.camera:camera-camera2:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\ee28ca93c07bf81d90ff8f6019728918\transformed\jetified-camera-camera2-1.2.2\AndroidManifest.xml:31:17-103
308                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
308-->[androidx.camera:camera-camera2:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\ee28ca93c07bf81d90ff8f6019728918\transformed\jetified-camera-camera2-1.2.2\AndroidManifest.xml:32:17-86
309        </service>
310
311        <receiver
311-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:29:9-33:20
312            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
312-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:30:13-85
313            android:enabled="true"
313-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:31:13-35
314            android:exported="false" >
314-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:32:13-37
315        </receiver>
316
317        <service
317-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:35:9-38:40
318            android:name="com.google.android.gms.measurement.AppMeasurementService"
318-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:36:13-84
319            android:enabled="true"
319-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:37:13-35
320            android:exported="false" />
320-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:38:13-37
321        <service
321-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:39:9-43:72
322            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
322-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:40:13-87
323            android:enabled="true"
323-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:41:13-35
324            android:exported="false"
324-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:42:13-37
325            android:permission="android.permission.BIND_JOB_SERVICE" />
325-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:43:13-69
326
327        <activity
327-->[androidx.compose.ui:ui-tooling-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\549ede32db9c3acb303de4fa6d2a7dfa\transformed\jetified-ui-tooling-release\AndroidManifest.xml:23:9-25:39
328            android:name="androidx.compose.ui.tooling.PreviewActivity"
328-->[androidx.compose.ui:ui-tooling-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\549ede32db9c3acb303de4fa6d2a7dfa\transformed\jetified-ui-tooling-release\AndroidManifest.xml:24:13-71
329            android:exported="true" />
329-->[androidx.compose.ui:ui-tooling-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\549ede32db9c3acb303de4fa6d2a7dfa\transformed\jetified-ui-tooling-release\AndroidManifest.xml:25:13-36
330
331        <provider
331-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\7ada1d27f1fad82fa455b667be34e866\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
332            android:name="androidx.startup.InitializationProvider"
332-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\7ada1d27f1fad82fa455b667be34e866\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
333            android:authorities="com.manaknight.app.androidx-startup"
333-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\7ada1d27f1fad82fa455b667be34e866\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
334            android:exported="false" >
334-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\7ada1d27f1fad82fa455b667be34e866\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
335            <meta-data
335-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\7ada1d27f1fad82fa455b667be34e866\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
336                android:name="androidx.emoji2.text.EmojiCompatInitializer"
336-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\7ada1d27f1fad82fa455b667be34e866\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
337                android:value="androidx.startup" />
337-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\7ada1d27f1fad82fa455b667be34e866\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
338            <meta-data
338-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\078811cffc78e28bc7870a96ed097257\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
339                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
339-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\078811cffc78e28bc7870a96ed097257\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
340                android:value="androidx.startup" />
340-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\078811cffc78e28bc7870a96ed097257\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
341            <meta-data
341-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
342                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
342-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
343                android:value="androidx.startup" />
343-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
344        </provider>
345
346        <uses-library
346-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\415c161ff15d16996587df02a2a7fdc1\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
347            android:name="androidx.window.extensions"
347-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\415c161ff15d16996587df02a2a7fdc1\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
348            android:required="false" />
348-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\415c161ff15d16996587df02a2a7fdc1\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
349        <uses-library
349-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\415c161ff15d16996587df02a2a7fdc1\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
350            android:name="androidx.window.sidecar"
350-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\415c161ff15d16996587df02a2a7fdc1\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
351            android:required="false" />
351-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\415c161ff15d16996587df02a2a7fdc1\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
352
353        <meta-data
353-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\3eddf5b8a9d33eb11d1830cb5e4fd1aa\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
354            android:name="com.google.android.gms.version"
354-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\3eddf5b8a9d33eb11d1830cb5e4fd1aa\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
355            android:value="@integer/google_play_services_version" />
355-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\3eddf5b8a9d33eb11d1830cb5e4fd1aa\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
356
357        <receiver
357-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
358            android:name="androidx.profileinstaller.ProfileInstallReceiver"
358-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
359            android:directBootAware="false"
359-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
360            android:enabled="true"
360-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
361            android:exported="true"
361-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
362            android:permission="android.permission.DUMP" >
362-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
363            <intent-filter>
363-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
364                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
364-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
364-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
365            </intent-filter>
366            <intent-filter>
366-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
367                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
367-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
367-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
368            </intent-filter>
369            <intent-filter>
369-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
370                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
370-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
370-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
371            </intent-filter>
372            <intent-filter>
372-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
373                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
373-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
373-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
374            </intent-filter>
375        </receiver>
376
377        <meta-data
377-->[com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\23890166d6a8b14dee3091bd6ffdea5c\transformed\jetified-billing-6.0.1\AndroidManifest.xml:19:9-21:37
378            android:name="com.google.android.play.billingclient.version"
378-->[com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\23890166d6a8b14dee3091bd6ffdea5c\transformed\jetified-billing-6.0.1\AndroidManifest.xml:20:13-73
379            android:value="6.0.1" />
379-->[com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\23890166d6a8b14dee3091bd6ffdea5c\transformed\jetified-billing-6.0.1\AndroidManifest.xml:21:13-34
380
381        <activity
381-->[com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\23890166d6a8b14dee3091bd6ffdea5c\transformed\jetified-billing-6.0.1\AndroidManifest.xml:23:9-27:75
382            android:name="com.android.billingclient.api.ProxyBillingActivity"
382-->[com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\23890166d6a8b14dee3091bd6ffdea5c\transformed\jetified-billing-6.0.1\AndroidManifest.xml:24:13-78
383            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
383-->[com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\23890166d6a8b14dee3091bd6ffdea5c\transformed\jetified-billing-6.0.1\AndroidManifest.xml:25:13-96
384            android:exported="false"
384-->[com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\23890166d6a8b14dee3091bd6ffdea5c\transformed\jetified-billing-6.0.1\AndroidManifest.xml:26:13-37
385            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
385-->[com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\23890166d6a8b14dee3091bd6ffdea5c\transformed\jetified-billing-6.0.1\AndroidManifest.xml:27:13-72
386
387        <service
387-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e0f5c7c79df0748d229f2beda36ab74d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
388            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
388-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e0f5c7c79df0748d229f2beda36ab74d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
389            android:exported="false" >
389-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e0f5c7c79df0748d229f2beda36ab74d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
390            <meta-data
390-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e0f5c7c79df0748d229f2beda36ab74d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
391                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
391-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e0f5c7c79df0748d229f2beda36ab74d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
392                android:value="cct" />
392-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e0f5c7c79df0748d229f2beda36ab74d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
393        </service>
394        <service
394-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e3b38192b92eeb3fc9e8187da50ce6a9\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
395            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
395-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e3b38192b92eeb3fc9e8187da50ce6a9\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
396            android:exported="false"
396-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e3b38192b92eeb3fc9e8187da50ce6a9\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
397            android:permission="android.permission.BIND_JOB_SERVICE" >
397-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e3b38192b92eeb3fc9e8187da50ce6a9\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
398        </service>
399
400        <receiver
400-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e3b38192b92eeb3fc9e8187da50ce6a9\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
401            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
401-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e3b38192b92eeb3fc9e8187da50ce6a9\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
402            android:exported="false" /> <!-- The activities will be merged into the manifest of the hosting app. -->
402-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e3b38192b92eeb3fc9e8187da50ce6a9\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
403        <activity
403-->[com.google.android.play:core-common:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\dc72e552015300076bb41fda69eedbd2\transformed\jetified-core-common-2.0.2\AndroidManifest.xml:14:9-18:65
404            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
404-->[com.google.android.play:core-common:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\dc72e552015300076bb41fda69eedbd2\transformed\jetified-core-common-2.0.2\AndroidManifest.xml:15:13-93
405            android:exported="false"
405-->[com.google.android.play:core-common:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\dc72e552015300076bb41fda69eedbd2\transformed\jetified-core-common-2.0.2\AndroidManifest.xml:16:13-37
406            android:stateNotNeeded="true"
406-->[com.google.android.play:core-common:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\dc72e552015300076bb41fda69eedbd2\transformed\jetified-core-common-2.0.2\AndroidManifest.xml:17:13-42
407            android:theme="@style/Theme.PlayCore.Transparent" />
407-->[com.google.android.play:core-common:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\dc72e552015300076bb41fda69eedbd2\transformed\jetified-core-common-2.0.2\AndroidManifest.xml:18:13-62
408    </application>
409
410</manifest>
