
package com.manaknight.app.network

import com.manaknight.app.model.remote.*
import com.manaknight.app.model.remote.profitPro.AllLineItemsResponseModel
import com.manaknight.app.model.remote.profitPro.CreateLineItemReqModel
import com.manaknight.app.model.remote.profitPro.CommonResponse
import com.manaknight.app.model.remote.profitPro.CompanyRequest
import com.manaknight.app.model.remote.profitPro.CreatePercentageDrawRequest
import com.manaknight.app.model.remote.profitPro.CreatePriceDrawRequest
import com.manaknight.app.model.remote.profitPro.MaterialReqModel
import com.manaknight.app.model.remote.profitPro.CustomerModel
import com.manaknight.app.model.remote.profitPro.CustomerResponseModel
import com.manaknight.app.model.remote.profitPro.DefaultModel
import com.manaknight.app.model.remote.profitPro.DrawInfoRespModel
import com.manaknight.app.model.remote.profitPro.LinearFootReqModel
import com.manaknight.app.model.remote.profitPro.LinearResponseModel
import com.manaknight.app.model.remote.profitPro.MaterialRequestModel
import com.manaknight.app.model.remote.profitPro.MaterialResponseModel
import com.manaknight.app.model.remote.profitPro.ProjectModel
import com.manaknight.app.model.remote.profitPro.ProjectTrackingResponse
import com.manaknight.app.model.remote.profitPro.SendInvoiceRequest
import com.manaknight.app.model.remote.profitPro.UpdateDrawRequest
import com.manaknight.app.model.remote.profitPro.UpdateDrawRequest2
import com.manaknight.app.model.remote.profitPro.UpdateLineItemReqModel
import okhttp3.MultipartBody
import retrofit2.Response
import retrofit2.http.*

interface ApiService {



    @POST("/v3/api/custom/profitpro/company/initialize-user")
    suspend fun signupCompanySeup(@Body request: CompanyRequest): Response<CommonResponse>

    @POST("/v3/api/custom/profitpro/company/onboarding-save-defaults")
    suspend fun updateCompanyDefault(@Body request: DefaultModel): Response<CommonResponse>


    @GET("/v3/api/custom/profitpro/company/search-customer")
  suspend fun searchCustomers(@Query("prefix") searchText: String?, @Query("limit") limit: Int?): Response<CustomerResponseModel>

    @POST("/v4/api/records/default_material")
    suspend fun createMaterial(@Body request: MaterialRequestModel): Response<CommonResponse>

    @PUT("/v4/api/records/default_material/{id}")
    suspend fun updateMaterial(@Body request: MaterialRequestModel, @Path("id") id: Int?): Response<CommonResponse>

    @POST("/v4/api/records/customer")
    suspend fun createCustomer(@Body request: CustomerModel): Response<CommonResponse>

    @PUT("/v4/api/records/customer/{id}")
    suspend fun updateCustomer(@Body request: CustomerModel, @Path("id") id: Int?): Response<CommonResponse>

    @GET("/v4/api/records/customer")
    suspend fun getCustomerList(          @Query("order") order: String?,
                                          @Query("size") size: String?,
                                          @Query("filter") filter: String?,
                                          @Query("join") join: String?,  ): Response<GetCustomerListResponse>



    @POST("/v3/api/custom/profitpro/company/create-estimation")
    suspend fun createNewEstimation(@Body request: ProjectModel): Response<CommonResponse>


    @GET("/v4/api/records/default_material")
    suspend fun getDefaultMaterialList(@Query("filter") filter: String?): Response<MaterialResponseModel>


    @POST("/v4/api/records/default_material")
    suspend fun createDefaultMaterial(@Body request: MaterialReqModel): Response<CommonResponse>

    @POST("/v3/api/custom/profitpro/company/add-line-item")
    suspend fun addLineItem(@Body request: CreateLineItemReqModel): Response<CommonResponse>

    @POST("/v3/api/custom/profitpro/company/edit-line-item/{item_id}")
    suspend fun updateLineItem(@Path("item_id") itemID: Int, @Body request: UpdateLineItemReqModel): Response<CommonResponse>

    @GET("/v3/api/custom/profitpro/company/get-costs")
    suspend fun getSquareFootLinealFootCosts(@Query("type") filter: String?): Response<LinearResponseModel>

    @POST("/v3/api/custom/profitpro/company/add-lineal-foot-cost")
    suspend fun addLinealFootCost(@Body request: LinearFootReqModel): Response<CommonResponse>

    @POST("/v3/api/custom/profitpro/company/add-square-foot-cost")
    suspend fun addSquareFootCost(@Body request: LinearFootReqModel): Response<CommonResponse>

    @GET("/v3/api/custom/profitpro/company/get-project-details/{project_id}")
    suspend fun getSingleProjectDetails(@Path("project_id") projectId: Int): Response<AllLineItemsResponseModel>

    @GET("/v4/api/records/project")
    suspend fun getAllProjects(@Query("filter") filter: String,
                               @Query("join") join: String): Response<ProjectResponseModel>

    @GET("/v3/api/custom/profitpro/company/initialize-draws/{project_id}")
    suspend fun initializeDraws(@Path("project_id") projectId: Int): Response<CommonResponse>

    @GET("/v3/api/custom/profitpro/company/get-draws-totals/{project_id}")
    suspend fun getAllDraws(@Path("project_id") projectId: Int): Response<DrawInfoRespModel>

    @DELETE("/v4/api/records/draws/{id}")
    suspend fun deleteDraws(@Path("id") id: Int?): Response<CommonResponse>


    @POST("/v3/api/custom/profitpro/company/create-draws")
    suspend fun createPriceDraw(@Body request: CreatePriceDrawRequest): Response<CommonResponse>

    @POST("/v3/api/custom/profitpro/company/create-draws")
    suspend fun createPercentageDraw(@Body request: CreatePercentageDrawRequest): Response<CommonResponse>


    @POST("/v3/api/custom/profitpro/send-invoice")
    suspend fun sendInvoice(@Body request: SendInvoiceRequest): Response<CommonResponse>


    @POST("/v3/api/custom/profitpro/company/update-draws/{id}")
    suspend fun updatePriceDraw(@Body request: UpdateDrawRequest, @Path("id") id: Int?): Response<CommonResponse>

    @POST("/v3/api/custom/profitpro/company/update-draws/{id}")
    suspend fun updatePercentageDraw(@Body request: UpdateDrawRequest2, @Path("id") id: Int?): Response<CommonResponse>

    @DELETE("/v4/api/records/line_items/{id}")
    suspend fun deleteLineItems(@Path("id") id: Int?): Response<CommonResponse>























    @POST("/v1/api/rest/user/GETALL")
    suspend fun getAllUser(): Response<FriendListResponse>

  @POST("/v3/api/lambda/realtime/room")
    suspend fun createRoomRequests(@Body request: CreateRoomRequests): Response<CreateRoomResponse>


  @POST("/v3/api/lambda/realtime/chat")
    suspend fun getChats(@Body request: ChatRequest): Response<ChatResponse>

    @GET("/v3/api/lambda/realtime/room/poll")
    suspend fun getStartPool(@Query("user_id") customDate: Int?): Response<SingleChatMessageResponse>


    @GET("/v4/api/records/room")
    suspend fun getAllRoom(@Query("user_id") id: Int?): Response<ChatRoomResponse>


    @POST("/v5/api/deployments/wireframe/ask-gpt-custom")
    suspend fun sendMessageToBot(@Body json: String): Response<ChatBotTextResponse>


    @POST("/v5/api/deployments/wireframe/ask-gpt-custom")
    suspend fun sendMessageToBot(@Body request: ChatBotRequest): Response<ChatBotTextResponse>


    @POST("/v3/api/lambda/realtime/send")
    suspend fun sendTextMessage(@Body request: ChatTextRequest): Response<ChatTextResponse>




      @POST("/v3/api/custom/profitpro/company/change-order")
      suspend fun createChangeOrder(@Body request: CreateChangeOrderRequest,         @Path("project_id") projectId: Any,   ): Response<CreateChangeOrderResponse>



      @GET("/v3/api/custom/profitpro/company/finalize-project")
      suspend fun finalizeProject(   ): Response<FinalizeProjectResponse>



      @GET("/v3/api/custom/profitpro/company/project-review")
      suspend fun getProjectReview(         @Path("project_id") projectId: Any,   ): Response<GetProjectReviewResponse>



      @POST("/v3/api/custom/profitpro/company/update-draws")
      suspend fun updateDraws(@Body request: UpdateDrawsRequest,         @Path("project_id") projectId: Int,   ): Response<UpdateDrawsResponse>














      @GET("/v3/api/custom/profitpro/company/get-material-details")
      suspend fun trackingMaterial(         @Path("project_id") projectId: Int,   ): Response<TrackingMaterialResponse>



      @GET("/v3/api/custom/profitpro/company/get-tracking/{project_id}")
      suspend fun getProjectTrackingDetails(         @Path("project_id") projectId: Int,   ): Response<ProjectTrackingResponse>


      @GET("/v3/api/custom/profitpro/company/get-labor-details")
      suspend fun trackingLabour(         @Path("project_id") projectId: Int,   ): Response<TrackingLabourResponse>



      @GET("/v3/api/custom/profitpro/company/get-draws")
      suspend fun trackingDraws(         @Path("project_id") projectId: Int,          @Query("status") status: String?,  ): Response<TrackingDrawsResponse>



      @GET("/v3/api/custom/profitpro/company/line-details")
      suspend fun getLineDetails(         @Path("line_id") lineId: Any,   ): Response<GetLineDetailsResponse>






      @POST("/v3/api/custom/profitpro/company/finlize-account")
      suspend fun finalizingOnboarding(   ): Response<FinalizingOnboardingResponse>













      @GET("/v3/api/custom/profitpro/company/initialize-user")
      suspend fun initializeUser(   ): Response<InitializeUserResponse>



      @POST("/v3/api/custom/profitpro/company/onboarding-save-defaults")
      suspend fun saveDefaultsOnbording(@Body request: SaveDefaultsOnbordingRequest,   ): Response<SaveDefaultsOnbordingResponse>



      @GET("/v3/api/custom/profitpro/company/get-projects")
      suspend fun getProjects(          @Query("type") type: String?,
       @Query("time_period") timePeriod: String?,  ): Response<GetProjectsResponse>



      @POST("/v3/api/custom/profitpro/company/onboarding")
      suspend fun onboarding(@Body request: OnboardingRequest,   ): Response<OnboardingResponse>



      @GET("/v3/api/custom/profitpro/company/overview")
      suspend fun companyOverview(   ): Response<CompanyOverviewResponse>



      @GET("/v3/api/custom/profitpro/company/details")
      suspend fun companyDetails(   ): Response<CompanyDetailsResponse>



      @GET("/v3/api/profitpro/dashboard/project-stats")
      suspend fun getProjectStats(   ): Response<GetProjectStatsResponse>



      @POST("/v2/api/lambda/check")
      suspend fun lambdaCheck(@Body request: LambdaCheckRequest,   ): Response<LambdaCheckResponse>



      @POST("/v2/api/lambda/2fa/login")
      suspend fun twoFALogin(@Body request: TwoFALoginRequest,   ): Response<TwoFALoginResponse>



      @POST("/v2/api/lambda/2fa/signin")
      suspend fun twoFASignin(@Body request: TwoFASigninRequest,   ): Response<TwoFASigninResponse>



      @POST("/v2/api/lambda/2fa/authorize")
      suspend fun twoFAAuthorize(@Body request: TwoFAAuthorizeRequest,   ): Response<TwoFAAuthorizeResponse>



      @POST("/v2/api/lambda/2fa/enable")
      suspend fun twoFAEnable(@Body request: TwoFAEnableRequest,   ): Response<TwoFAEnableResponse>



      @POST("/v2/api/lambda/2fa/disable")
      suspend fun twoFADisable(@Body request: TwoFADisableRequest,   ): Response<TwoFADisableResponse>



      @POST("/v2/api/lambda/2fa/verify")
      suspend fun twoFAVerify(@Body request: TwoFAVerifyRequest,   ): Response<TwoFAVerifyResponse>



      @POST("/v2/api/lambda/2fa/auth")
      suspend fun twoFAAuth(@Body request: TwoFAAuthRequest,   ): Response<TwoFAAuthResponse>



      @POST("/v2/api/lambda/analytics/")
      suspend fun analyticsLog(@Body request: AnalyticsLogRequest,   ): Response<AnalyticsLogResponse>



      @GET("/v2/api/lambda/analytics/data")
      suspend fun getAnalytics(   ): Response<GetAnalyticsResponse>



      @POST("/v2/api/lambda/heatmap")
      suspend fun logHeatmapAnalytics(@Body request: LogHeatmapAnalyticsRequest,   ): Response<LogHeatmapAnalyticsResponse>



      @GET("/v2/api/lambda/heatmap/data")
      suspend fun getHeatmapData(          @Query("custom_date") customDate: String?,  ): Response<GetHeatmapDataResponse>



      @GET("/v2/api/lambda/user-sessions/data")
      suspend fun userSessionsData(          @Query("custom_date") customDate: String?,  ): Response<UserSessionsDataResponse>



      @POST("/v2/api/lambda/analytics/user-sessions/")
      suspend fun createUserSessionsAnalytics(@Body request: CreateUserSessionsAnalyticsRequest,   ): Response<CreateUserSessionsAnalyticsResponse>



      @POST("/v2/api/lambda/apple/login/mobile")
      suspend fun appleLoginMobileEndpoint(@Body request: AppleLoginMobileEndpointRequest,   ): Response<AppleLoginMobileEndpointResponse>



      @GET("/v2/api/lambda/apple/login")
      suspend fun appleLogin(   ): Response<AppleLoginResponse>



      @POST("/v2/api/lambda/apple/code")
      suspend fun appleAuthCode(@Body request: AppleAuthCodeRequest,   ): Response<AppleAuthCodeResponse>



      @GET("/v2/api/lambda/google/code")
      suspend fun googleCode(          @Query("state") state: String,  ): Response<GoogleCodeResponse>



      @GET("/v2/api/lambda/google/code/mobile")
      suspend fun googleCodeMobile(          @Query("role") role: String?,
       @Query("is_refresh") isRefresh: Boolean?,
       @Query("code") code: String?,  ): Response<GoogleCodeMobileResponse>



      @GET("/v2/api/lambda/google/login")
      suspend fun googleLogin(          @Query("role") role: String?,
       @Query("company_id") companyId: String?,
       @Query("is_refresh") isRefresh: Boolean?,  ): Response<GoogleLoginResponse>



      @GET("/v2/api/lambda/blog/all")
      suspend fun blogAll(          @Query("limit") limit: Int,
       @Query("offset") offset: Int,  ): Response<BlogAllResponse>



      @GET("/v2/api/lambda/blog/similar/{id}")
      suspend fun blogSimilar(          @Query("top") top: Int,  ): Response<BlogSimilarResponse>



      @GET("/v2/api/lambda/blog/filter")
      suspend fun blogFilter(          @Query("categories") categories: ArrayList<Map<String, Any>>,
       @Query("tags") tags: ArrayList<Map<String, Any>>,
       @Query("rule") rule: String?,
       @Query("search") search: String?,
       @Query("limit") limit: Int,
       @Query("page") page: Int,  ): Response<BlogFilterResponse>



      @POST("/v2/api/lambda/blog/create")
      suspend fun blogCreate(@Body request: BlogCreateRequest,   ): Response<BlogCreateResponse>



      @POST("/v2/api/lambda/blog/edit/{id}")
      suspend fun blogEdit(@Body request: BlogEditRequest,         @Path("id") id: String?,   ): Response<BlogEditResponse>



      @DELETE("/v2/api/lambda/blog/delete/{id}")
      suspend fun blogDelete(         @Path("id") id: String?,   ): Response<BlogDeleteResponse>



      @GET("/v2/api/lambda/blog/single/{id}")
      suspend fun blogSingle(         @Path("id") id: String?,   ): Response<BlogSingleResponse>



      @POST("/v2/api/lambda/blog/tags")
      suspend fun blogTags(@Body request: BlogTagsRequest,   ): Response<BlogTagsResponse>



      @POST("/v2/api/lambda/blog/tags/{id}")
      suspend fun blogTagsUpdate(@Body request: BlogTagsUpdateRequest,   ): Response<BlogTagsUpdateResponse>



      @GET("/v2/api/lambda/blog/tags")
      suspend fun blogTagsRetrieve(          @Query("limit") limit: Int,
       @Query("page") page: Int,
       @Query("name") name: String?,  ): Response<BlogTagsRetrieveResponse>



      @DELETE("/v2/api/lambda/blog/tags/{id}")
      suspend fun blogTagsDeleteByID(         @Path("id") id: String?,   ): Response<BlogTagsDeleteByIDResponse>



      @POST("/v2/api/lambda/blog/category")
      suspend fun createBlogCategory(@Body request: CreateBlogCategoryRequest,   ): Response<CreateBlogCategoryResponse>



      @POST("/v2/api/lambda/blog/category/{id}")
      suspend fun updateBlogCategory(@Body request: UpdateBlogCategoryRequest,   ): Response<UpdateBlogCategoryResponse>



      @GET("/v2/api/lambda/blog/category")
      suspend fun getBlogCategory(          @Query("limit") limit: Int,
       @Query("page") page: Int,
       @Query("name") name: String?,  ): Response<GetBlogCategoryResponse>



      @GET("/v2/api/lambda/blog/subcategory/{id}")
      suspend fun getBlogSubcategory(         @Path("id") id: String?,   ): Response<GetBlogSubcategoryResponse>



      @DELETE("/v2/api/lambda/blog/category/{id}")
      suspend fun deleteBlogCategory(         @Path("id") id: String?,   ): Response<DeleteBlogCategoryResponse>



      @GET("/v2/api/lambda/test/{width?}/{height?}/")
      suspend fun captchaTest(         @Path("width") width: Int,
       @Path("height") height: Int,   ): Response<CaptchaTestResponse>



      @GET("/v2/api/lambda/captcha/{width?}/{height?}/")
      suspend fun captchaGenerate(         @Path("width") width: Int,
       @Path("height") height: Int,   ): Response<CaptchaGenerateResponse>



      @POST("/v2/api/lambda/google-captcha/")
      suspend fun googleCaptchaVerify(@Body request: GoogleCaptchaVerifyRequest,   ): Response<GoogleCaptchaVerifyResponse>



      @POST("/v2/api/lambda/cms")
      suspend fun createCMSLambda(@Body request: CreateCMSLambdaRequest,   ): Response<CreateCMSLambdaResponse>



      @PUT("/v2/api/lambda/cms/{id}")
      suspend fun updateCMSLambda(@Body request: UpdateCMSLambdaRequest,         @Path("id") id: String?,   ): Response<UpdateCMSLambdaResponse>



      @DELETE("/v2/api/lambda/cms/{id}")
      suspend fun deleteCMSLambda(         @Path("id") id: String?,   ): Response<DeleteCMSLambdaResponse>



      @GET("/v2/api/lambda/cms/id/{id}")
      suspend fun getCMSByIDLambda(         @Path("id") id: String?,   ): Response<GetCMSByIDLambdaResponse>



      @GET("/v2/api/lambda/cms/page/{page}/{key}")
      suspend fun getCMSByPageAndKeyLambda(         @Path("page") page: String?,
       @Path("key") key: String?,   ): Response<GetCMSByPageAndKeyLambdaResponse>



      @GET("/v2/api/lambda/cms/page/{page}")
      suspend fun getCMSByPageLambda(         @Path("page") page: String?,   ): Response<GetCMSByPageLambdaResponse>



      @GET("/v2/api/lambda/cms/all")
      suspend fun getAllCMSLambda(   ): Response<GetAllCMSLambdaResponse>



      @POST("/v2/api/lambda/register")
      suspend fun registerLambda(@Body request: RegisterLambdaRequest,   ): Response<RegisterLambdaResponse>



      @POST("/v2/api/lambda/login")
      suspend fun loginLambda(@Body request: LoginLambdaRequest,   ): Response<LoginLambdaResponse>



      @POST("/v2/api/lambda/marketing-login")
      suspend fun marketingLoginLambda(@Body request: MarketingLoginLambdaRequest,   ): Response<MarketingLoginLambdaResponse>



      @GET("/v2/api/lambda/profile")
      suspend fun profile(   ): Response<ProfileResponse>



      @POST("/v2/api/lambda/profile")
      suspend fun profileUpdate(@Body request: ProfileUpdateRequest,   ): Response<ProfileUpdateResponse>


      @Multipart
      @POST("/v2/api/lambda/upload")
      suspend fun uploadImageLocalDefault(           @Part file: MultipartBody.Part ): Response<UploadImageLocalDefaultResponse>


      @Multipart
      @POST("/v2/api/lambda/s3/upload")
      suspend fun uploadimages3(           @Part file: MultipartBody.Part ): Response<UploadImageS3Response>



      @GET("/v2/api/lambda/preference")
      suspend fun preferenceFetch(   ): Response<PreferenceFetchResponse>



      @POST("/v2/api/lambda/preference")
      suspend fun preferenceUpdate(@Body request: PreferenceUpdateRequest,   ): Response<PreferenceUpdateResponse>



      @GET("/v4/api/records/sow")
      suspend fun getSowTree(          @Query("order") order: String?,
       @Query("page") page: String?,  ): Response<GetSowTreeResponse>



      @GET("/v4/api/records/alerts")
      suspend fun appAlertsList(          @Query("order") order: String?,
       @Query("page") page: String?,
       @Query("filter") filter: String?,  ): Response<AppAlertsListResponse>



      @PUT("/v4/api/records/alerts/{id}")
      suspend fun appAlertsUpdate(@Body request: AppAlertsUpdateRequest,         @Path("id") id: String?,   ): Response<AppAlertsUpdateResponse>



      @POST("/v2/api/lambda/ecom/product/")
      suspend fun retrieveProductDefault(@Body request: RetrieveProductDefaultRequest,   ): Response<RetrieveProductDefaultResponse>



      @GET("/v2/api/lambda/ecom/product/{product_identifier}")
      suspend fun ecomProductByIDDefault(   ): Response<EcomProductByIDDefaultResponse>



      @POST("/v3/api/custom/lambda/ecom/product/add")
      suspend fun addEcomProductLambda(@Body request: AddEcomProductLambdaRequest,   ): Response<AddEcomProductLambdaResponse>



      @PUT("/v2/api/lambda/ecom/product/{id}")
      suspend fun editEcomProductLambda(@Body request: EditEcomProductLambdaRequest,         @Path("id") id: Number?,   ): Response<EditEcomProductLambdaResponse>



      @DELETE("/v2/api/lambda/ecom/product/{id}")
      suspend fun deleteEcomProductLambda(         @Path("id") id: Number?,   ): Response<DeleteEcomProductLambdaResponse>



      @GET("/v2/api/lambda/ecom/cart")
      suspend fun getCartItems(          @Query("user_id") userId: String?,  ): Response<GetCartItemsResponse>



      @POST("/v2/api/lambda/ecom/cart/item")
      suspend fun ecomAddCart(@Body request: EcomAddCartRequest,   ): Response<EcomAddCartResponse>



      @POST("/v2/api/lambda/ecom/cart/update")
      suspend fun ecomDeleteCartItem(          @Query("user_id") userId: String?,
       @Query("data") data: String?,  ): Response<EcomDeleteCartItemResponse>



      @POST("/v2/api/lambda/ecom/product/review")
      suspend fun ecomGetProductReview(          @Query("productId") productid: Int?,  ): Response<EcomGetProductReviewResponse>



      @POST("/v2/api/lambda/ecom/product/review/add")
      suspend fun ecomAddProductReview(          @Query("review") review: String?,
       @Query("productId") productid: Int?,  ): Response<EcomAddProductReviewResponse>



      @POST("/v2/api/lambda/forgot")
      suspend fun forgotPassword(@Body request: ForgotPasswordRequest,   ): Response<ForgotPasswordResponse>



      @POST("/v2/api/lambda/mobile/forgot")
      suspend fun forgotPasswordMobile(@Body request: ForgotPasswordMobileRequest,   ): Response<ForgotPasswordMobileResponse>



      @POST("/v2/api/lambda/reset")
      suspend fun resetPassword(@Body request: ResetPasswordRequest,   ): Response<ResetPasswordResponse>



      @POST("/v2/api/lambda/mobile/reset")
      suspend fun resetPasswordMobile(@Body request: ResetPasswordMobileRequest,   ): Response<ResetPasswordMobileResponse>



      @POST("/v2/api/lambda/stripe/mobile/intent/")
      suspend fun getStripeData(@Body request: GetStripeDataRequest,   ): Response<GetStripeDataResponse>



      @GET("/v4/api/records/default_square_foot_cost/{id}")
      suspend fun getOneDefaultSquareFootCost(         @Path("id") id: Int?,          @Query("join") join: String?,  ): Response<GetOneDefaultSquareFootCostResponse>



      @GET("/v4/api/records/setting/{id}")
      suspend fun getOneSetting(         @Path("id") id: Int?,          @Query("join") join: String?,  ): Response<GetOneSettingResponse>



      @GET("/v4/api/records/cost/{id}")
      suspend fun getOneCost(         @Path("id") id: Int?,          @Query("join") join: String?,  ): Response<GetOneCostResponse>



      @GET("/v4/api/records/room/{id}")
      suspend fun getOneRoom(         @Path("id") id: Int?,          @Query("join") join: String?,  ): Response<GetOneRoomResponse>



      @GET("/v4/api/records/labor/{id}")
      suspend fun getOneLabor(         @Path("id") id: Int?,          @Query("join") join: String?,  ): Response<GetOneLaborResponse>



      @GET("/v4/api/records/line_item_entry/{id}")
      suspend fun getOneLineItemEntry(         @Path("id") id: Int?,          @Query("join") join: String?,  ): Response<GetOneLineItemEntryResponse>



      @GET("/v4/api/records/company_settings/{id}")
      suspend fun getOneCompanySettings(         @Path("id") id: Int?,          @Query("join") join: String?,  ): Response<GetOneCompanySettingsResponse>



      @GET("/v4/api/records/cms/{id}")
      suspend fun getOneCms(         @Path("id") id: Int?,          @Query("join") join: String?,  ): Response<GetOneCmsResponse>



      @GET("/v4/api/records/team_member/{id}")
      suspend fun getOneTeamMember(         @Path("id") id: Int?,          @Query("join") join: String?,  ): Response<GetOneTeamMemberResponse>



      @GET("/v4/api/records/default_material/{id}")
      suspend fun getOneDefaultMaterial(         @Path("id") id: Int?,          @Query("join") join: String?,  ): Response<GetOneDefaultMaterialResponse>



      @GET("/v4/api/records/project/{id}")
      suspend fun getOneProject(         @Path("id") id: Int?,          @Query("join") join: String?,  ): Response<GetOneProjectResponse>



      @GET("/v4/api/records/user/{id}")
      suspend fun getOneUser(         @Path("id") id: Int?,          @Query("join") join: String?,  ): Response<GetOneUserResponse>



      @GET("/v4/api/records/profile/{id}")
      suspend fun getOneProfile(         @Path("id") id: Int?,          @Query("join") join: String?,  ): Response<GetOneProfileResponse>



      @GET("/v4/api/records/lineal_foot_cost/{id}")
      suspend fun getOneLinealFootCost(         @Path("id") id: Int?,          @Query("join") join: String?,  ): Response<GetOneLinealFootCostResponse>



      @GET("/v4/api/records/customer/{id}")
      suspend fun getOneCustomer(         @Path("id") id: Int?,          @Query("join") join: String?,  ): Response<GetOneCustomerResponse>



      @GET("/v4/api/records/permission/{id}")
      suspend fun getOnePermission(         @Path("id") id: Int?,          @Query("join") join: String?,  ): Response<GetOnePermissionResponse>



      @GET("/v4/api/records/token/{id}")
      suspend fun getOneToken(         @Path("id") id: Int?,          @Query("join") join: String?,  ): Response<GetOneTokenResponse>



      @GET("/v4/api/records/sqft_costs/{id}")
      suspend fun getOneSqftCosts(         @Path("id") id: Int?,          @Query("join") join: String?,  ): Response<GetOneSqftCostsResponse>



      @GET("/v4/api/records/email/{id}")
      suspend fun getOneEmail(         @Path("id") id: Int?,          @Query("join") join: String?,  ): Response<GetOneEmailResponse>



      @GET("/v4/api/records/alerts/{id}")
      suspend fun getOneAlerts(         @Path("id") id: Int?,          @Query("join") join: String?,  ): Response<GetOneAlertsResponse>



      @GET("/v4/api/records/draws/{id}")
      suspend fun getOneDraws(         @Path("id") id: Int?,          @Query("join") join: String?,  ): Response<GetOneDrawsResponse>



      @GET("/v4/api/records/chat/{id}")
      suspend fun getOneChat(         @Path("id") id: Int?,          @Query("join") join: String?,  ): Response<GetOneChatResponse>



      @GET("/v4/api/records/material/{id}")
      suspend fun getOneMaterial(         @Path("id") id: Int?,          @Query("join") join: String?,  ): Response<GetOneMaterialResponse>



      @GET("/v4/api/records/invoice/{id}")
      suspend fun getOneInvoice(         @Path("id") id: Int?,          @Query("join") join: String?,  ): Response<GetOneInvoiceResponse>



      @GET("/v4/api/records/default_lineal_foot_cost/{id}")
      suspend fun getOneDefaultLinealFootCost(         @Path("id") id: Int?,          @Query("join") join: String?,  ): Response<GetOneDefaultLinealFootCostResponse>



      @GET("/v4/api/records/trigger_type/{id}")
      suspend fun getOneTriggerType(         @Path("id") id: Int?,          @Query("join") join: String?,  ): Response<GetOneTriggerTypeResponse>



      @GET("/v4/api/records/job/{id}")
      suspend fun getOneJob(         @Path("id") id: Int?,          @Query("join") join: String?,  ): Response<GetOneJobResponse>



      @GET("/v4/api/records/line_items/{id}")
      suspend fun getOneLineItems(         @Path("id") id: Int?,          @Query("join") join: String?,  ): Response<GetOneLineItemsResponse>



      @GET("/v4/api/records/photo/{id}")
      suspend fun getOnePhoto(         @Path("id") id: Int?,          @Query("join") join: String?,  ): Response<GetOnePhotoResponse>



      @GET("/v4/api/records/api_keys/{id}")
      suspend fun getOneApiKeys(         @Path("id") id: Int?,          @Query("join") join: String?,  ): Response<GetOneApiKeysResponse>



      @GET("/v4/api/records/change_order_description/{id}")
      suspend fun getOneChangeOrderDescription(         @Path("id") id: Int?,          @Query("join") join: String?,  ): Response<GetOneChangeOrderDescriptionResponse>



      @GET("/v4/api/records/analytic_log/{id}")
      suspend fun getOneAnalyticLog(         @Path("id") id: Int?,          @Query("join") join: String?,  ): Response<GetOneAnalyticLogResponse>



      @GET("/v4/api/records/posts/{id}")
      suspend fun getOnePosts(         @Path("id") id: Int?,          @Query("join") join: String?,  ): Response<GetOnePostsResponse>



      @GET("/v4/api/records/employee/{id}")
      suspend fun getOneEmployee(         @Path("id") id: Int?,          @Query("join") join: String?,  ): Response<GetOneEmployeeResponse>



      @GET("/v4/api/records/default_square_foot_cost")
      suspend fun getDefaultSquareFootCostList(          @Query("order") order: String?,
       @Query("size") size: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetDefaultSquareFootCostListResponse>



      @GET("/v4/api/records/setting")
      suspend fun getSettingList(          @Query("order") order: String?,
       @Query("size") size: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetSettingListResponse>



      @GET("/v4/api/records/cost")
      suspend fun getCostList(          @Query("order") order: String?,
       @Query("size") size: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetCostListResponse>



      @GET("/v4/api/records/room")
      suspend fun getRoomList(          @Query("order") order: String?,
       @Query("size") size: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetRoomListResponse>



      @GET("/v4/api/records/labor")
      suspend fun getLaborList(          @Query("order") order: String?,
       @Query("size") size: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetLaborListResponse>



      @GET("/v4/api/records/line_item_entry")
      suspend fun getLineItemEntryList(          @Query("order") order: String?,
       @Query("size") size: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetLineItemEntryListResponse>



      @GET("/v4/api/records/company_settings")
      suspend fun getCompanySettingsList(          @Query("order") order: String?,
       @Query("size") size: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetCompanySettingsListResponse>



      @GET("/v4/api/records/cms")
      suspend fun getCmsList(          @Query("order") order: String?,
       @Query("size") size: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetCmsListResponse>



      @GET("/v4/api/records/team_member")
      suspend fun getTeamMemberList(          @Query("order") order: String?,
       @Query("size") size: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetTeamMemberListResponse>




      @GET("/v4/api/records/project")
      suspend fun getProjectList(          @Query("order") order: String?,
       @Query("size") size: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetProjectListResponse>



      @GET("/v4/api/records/user")
      suspend fun getUserList(          @Query("order") order: String?,
       @Query("size") size: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetUserListResponse>



      @GET("/v4/api/records/profile")
      suspend fun getProfileList(          @Query("order") order: String?,
       @Query("size") size: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetProfileListResponse>



      @GET("/v4/api/records/lineal_foot_cost")
      suspend fun getLinealFootCostList(          @Query("order") order: String?,
       @Query("size") size: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetLinealFootCostListResponse>







      @GET("/v4/api/records/permission")
      suspend fun getPermissionList(          @Query("order") order: String?,
       @Query("size") size: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetPermissionListResponse>



      @GET("/v4/api/records/token")
      suspend fun getTokenList(          @Query("order") order: String?,
       @Query("size") size: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetTokenListResponse>



      @GET("/v4/api/records/sqft_costs")
      suspend fun getSqftCostsList(          @Query("order") order: String?,
       @Query("size") size: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetSqftCostsListResponse>



      @GET("/v4/api/records/email")
      suspend fun getEmailList(          @Query("order") order: String?,
       @Query("size") size: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetEmailListResponse>



      @GET("/v4/api/records/alerts")
      suspend fun getAlertsList(          @Query("order") order: String?,
       @Query("size") size: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetAlertsListResponse>



      @GET("/v4/api/records/draws")
      suspend fun getDrawsList(          @Query("order") order: String?,
       @Query("size") size: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetDrawsListResponse>



      @GET("/v4/api/records/chat")
      suspend fun getChatList(          @Query("order") order: String?,
       @Query("size") size: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetChatListResponse>



      @GET("/v4/api/records/material")
      suspend fun getMaterialList(          @Query("order") order: String?,
       @Query("size") size: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetMaterialListResponse>



      @GET("/v4/api/records/invoice")
      suspend fun getInvoiceList(          @Query("order") order: String?,
       @Query("size") size: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetInvoiceListResponse>



      @GET("/v4/api/records/default_lineal_foot_cost")
      suspend fun getDefaultLinealFootCostList(          @Query("order") order: String?,
       @Query("size") size: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetDefaultLinealFootCostListResponse>



      @GET("/v4/api/records/trigger_type")
      suspend fun getTriggerTypeList(          @Query("order") order: String?,
       @Query("size") size: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetTriggerTypeListResponse>



      @GET("/v4/api/records/job")
      suspend fun getJobList(          @Query("order") order: String?,
       @Query("size") size: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetJobListResponse>



      @GET("/v4/api/records/line_items")
      suspend fun getLineItemsList(          @Query("order") order: String?,
       @Query("size") size: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetLineItemsListResponse>



      @GET("/v4/api/records/photo")
      suspend fun getPhotoList(          @Query("order") order: String?,
       @Query("size") size: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetPhotoListResponse>



      @GET("/v4/api/records/api_keys")
      suspend fun getApiKeysList(          @Query("order") order: String?,
       @Query("size") size: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetApiKeysListResponse>



      @GET("/v4/api/records/change_order_description")
      suspend fun getChangeOrderDescriptionList(          @Query("order") order: String?,
       @Query("size") size: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetChangeOrderDescriptionListResponse>



      @GET("/v4/api/records/analytic_log")
      suspend fun getAnalyticLogList(          @Query("order") order: String?,
       @Query("size") size: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetAnalyticLogListResponse>



      @GET("/v4/api/records/posts")
      suspend fun getPostsList(          @Query("order") order: String?,
       @Query("size") size: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetPostsListResponse>



      @GET("/v4/api/records/employee")
      suspend fun getEmployeeList(          @Query("order") order: String?,
       @Query("size") size: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetEmployeeListResponse>



      @GET("/v4/api/records/default_square_foot_cost")
      suspend fun getDefaultSquareFootCostPaginated(          @Query("order") order: String?,
       @Query("page") page: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetDefaultSquareFootCostPaginatedResponse>



      @GET("/v4/api/records/setting")
      suspend fun getSettingPaginated(          @Query("order") order: String?,
       @Query("page") page: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetSettingPaginatedResponse>



      @GET("/v4/api/records/cost")
      suspend fun getCostPaginated(          @Query("order") order: String?,
       @Query("page") page: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetCostPaginatedResponse>



      @GET("/v4/api/records/room")
      suspend fun getRoomPaginated(          @Query("order") order: String?,
       @Query("page") page: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetRoomPaginatedResponse>



      @GET("/v4/api/records/labor")
      suspend fun getLaborPaginated(          @Query("order") order: String?,
       @Query("page") page: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetLaborPaginatedResponse>



      @GET("/v4/api/records/line_item_entry")
      suspend fun getLineItemEntryPaginated(          @Query("order") order: String?,
       @Query("page") page: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetLineItemEntryPaginatedResponse>



      @GET("/v4/api/records/company_settings")
      suspend fun getCompanySettingsPaginated(          @Query("order") order: String?,
       @Query("page") page: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetCompanySettingsPaginatedResponse>



      @GET("/v4/api/records/cms")
      suspend fun getCmsPaginated(          @Query("order") order: String?,
       @Query("page") page: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetCmsPaginatedResponse>



      @GET("/v4/api/records/team_member")
      suspend fun getTeamMemberPaginated(          @Query("order") order: String?,
       @Query("page") page: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetTeamMemberPaginatedResponse>



      @GET("/v4/api/records/default_material")
      suspend fun getDefaultMaterialPaginated(          @Query("order") order: String?,
       @Query("page") page: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetDefaultMaterialPaginatedResponse>



      @GET("/v4/api/records/project")
      suspend fun getProjectPaginated(          @Query("order") order: String?,
       @Query("page") page: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetProjectPaginatedResponse>



      @GET("/v4/api/records/user")
      suspend fun getUserPaginated(          @Query("order") order: String?,
       @Query("page") page: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetUserPaginatedResponse>



      @GET("/v4/api/records/profile")
      suspend fun getProfilePaginated(          @Query("order") order: String?,
       @Query("page") page: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetProfilePaginatedResponse>



      @GET("/v4/api/records/lineal_foot_cost")
      suspend fun getLinealFootCostPaginated(          @Query("order") order: String?,
       @Query("page") page: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetLinealFootCostPaginatedResponse>



      @GET("/v4/api/records/customer")
      suspend fun getCustomerPaginated(          @Query("order") order: String?,
       @Query("page") page: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetCustomerPaginatedResponse>



      @GET("/v4/api/records/permission")
      suspend fun getPermissionPaginated(          @Query("order") order: String?,
       @Query("page") page: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetPermissionPaginatedResponse>



      @GET("/v4/api/records/token")
      suspend fun getTokenPaginated(          @Query("order") order: String?,
       @Query("page") page: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetTokenPaginatedResponse>



      @GET("/v4/api/records/sqft_costs")
      suspend fun getSqftCostsPaginated(          @Query("order") order: String?,
       @Query("page") page: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetSqftCostsPaginatedResponse>



      @GET("/v4/api/records/email")
      suspend fun getEmailPaginated(          @Query("order") order: String?,
       @Query("page") page: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetEmailPaginatedResponse>



      @GET("/v4/api/records/alerts")
      suspend fun getAlertsPaginated(          @Query("order") order: String?,
       @Query("page") page: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetAlertsPaginatedResponse>



      @GET("/v4/api/records/draws")
      suspend fun getDrawsPaginated(          @Query("order") order: String?,
       @Query("page") page: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetDrawsPaginatedResponse>



      @GET("/v4/api/records/chat")
      suspend fun getChatPaginated(          @Query("order") order: String?,
       @Query("page") page: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetChatPaginatedResponse>



      @GET("/v4/api/records/material")
      suspend fun getMaterialPaginated(          @Query("order") order: String?,
       @Query("page") page: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetMaterialPaginatedResponse>



      @GET("/v4/api/records/invoice")
      suspend fun getInvoicePaginated(          @Query("order") order: String?,
       @Query("page") page: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetInvoicePaginatedResponse>



      @GET("/v4/api/records/default_lineal_foot_cost")
      suspend fun getDefaultLinealFootCostPaginated(          @Query("order") order: String?,
       @Query("page") page: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetDefaultLinealFootCostPaginatedResponse>



      @GET("/v4/api/records/trigger_type")
      suspend fun getTriggerTypePaginated(          @Query("order") order: String?,
       @Query("page") page: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetTriggerTypePaginatedResponse>



      @GET("/v4/api/records/job")
      suspend fun getJobPaginated(          @Query("order") order: String?,
       @Query("page") page: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetJobPaginatedResponse>



      @GET("/v4/api/records/line_items")
      suspend fun getLineItemsPaginated(          @Query("order") order: String?,
       @Query("page") page: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetLineItemsPaginatedResponse>



      @GET("/v4/api/records/photo")
      suspend fun getPhotoPaginated(          @Query("order") order: String?,
       @Query("page") page: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetPhotoPaginatedResponse>



      @GET("/v4/api/records/api_keys")
      suspend fun getApiKeysPaginated(          @Query("order") order: String?,
       @Query("page") page: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetApiKeysPaginatedResponse>



      @GET("/v4/api/records/change_order_description")
      suspend fun getChangeOrderDescriptionPaginated(          @Query("order") order: String?,
       @Query("page") page: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetChangeOrderDescriptionPaginatedResponse>



      @GET("/v4/api/records/analytic_log")
      suspend fun getAnalyticLogPaginated(          @Query("order") order: String?,
       @Query("page") page: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetAnalyticLogPaginatedResponse>



      @GET("/v4/api/records/posts")
      suspend fun getPostsPaginated(          @Query("order") order: String?,
       @Query("page") page: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetPostsPaginatedResponse>



      @GET("/v4/api/records/employee")
      suspend fun getEmployeePaginated(          @Query("order") order: String?,
       @Query("page") page: String?,
       @Query("filter") filter: String?,
       @Query("join") join: String?,  ): Response<GetEmployeePaginatedResponse>



      @POST("/v4/api/records/default_square_foot_cost")
      suspend fun createDefaultSquareFootCost(@Body request: CreateDefaultSquareFootCostRequest,   ): Response<CreateDefaultSquareFootCostResponse>



      @POST("/v4/api/records/setting")
      suspend fun createSetting(@Body request: CreateSettingRequest,   ): Response<CreateSettingResponse>



      @POST("/v4/api/records/cost")
      suspend fun createCost(@Body request: CreateCostRequest,   ): Response<CreateCostResponse>



      @POST("/v4/api/records/room")
      suspend fun createRoom(@Body request: CreateRoomRequest,   ): Response<CreateRoomResponse>



      @POST("/v4/api/records/labor")
      suspend fun createLabor(@Body request: CreateLaborRequest,   ): Response<CreateLaborResponse>



      @POST("/v4/api/records/line_item_entry")
      suspend fun createLineItemEntry(@Body request: CreateLineItemEntryRequest,   ): Response<CreateLineItemEntryResponse>



      @POST("/v4/api/records/company_settings")
      suspend fun createCompanySettings(@Body request: CreateCompanySettingsRequest,   ): Response<CreateCompanySettingsResponse>



      @POST("/v4/api/records/cms")
      suspend fun createCms(@Body request: CreateCmsRequest,   ): Response<CreateCmsResponse>



      @POST("/v4/api/records/team_member")
      suspend fun createTeamMember(@Body request: CreateTeamMemberRequest,   ): Response<CreateTeamMemberResponse>




    @POST("/v4/api/records/draws")
    suspend fun createDraw(@Body request: CreateDrawRequest,   ): Response<CommonResponse>



      @POST("/v4/api/records/project")
      suspend fun createProject(@Body request: CreateProjectRequest,   ): Response<CreateProjectResponse>



      @POST("/v4/api/records/user")
      suspend fun createUser(@Body request: CreateUserRequest,   ): Response<CreateUserResponse>



      @POST("/v4/api/records/profile")
      suspend fun createProfile(@Body request: CreateProfileRequest,   ): Response<CreateProfileResponse>



      @POST("/v4/api/records/lineal_foot_cost")
      suspend fun createLinealFootCost(@Body request: CreateLinealFootCostRequest,   ): Response<CreateLinealFootCostResponse>






      @POST("/v4/api/records/permission")
      suspend fun createPermission(@Body request: CreatePermissionRequest,   ): Response<CreatePermissionResponse>



      @POST("/v4/api/records/token")
      suspend fun createToken(@Body request: CreateTokenRequest,   ): Response<CreateTokenResponse>



      @POST("/v4/api/records/sqft_costs")
      suspend fun createSqftCosts(@Body request: CreateSqftCostsRequest,   ): Response<CreateSqftCostsResponse>



      @POST("/v4/api/records/email")
      suspend fun createEmail(@Body request: CreateEmailRequest,   ): Response<CreateEmailResponse>



      @POST("/v4/api/records/alerts")
      suspend fun createAlerts(@Body request: CreateAlertsRequest,   ): Response<CreateAlertsResponse>






      @POST("/v4/api/records/chat")
      suspend fun createChat(@Body request: CreateChatRequest,   ): Response<CreateChatResponse>



      @POST("/v4/api/records/material")
      suspend fun createMaterial(@Body request: CreateMaterialRequest,   ): Response<CreateMaterialResponse>



      @POST("/v4/api/records/invoice")
      suspend fun createInvoice(@Body request: CreateInvoiceRequest,   ): Response<CreateInvoiceResponse>



      @POST("/v4/api/records/default_lineal_foot_cost")
      suspend fun createDefaultLinealFootCost(@Body request: CreateDefaultLinealFootCostRequest,   ): Response<CreateDefaultLinealFootCostResponse>



      @POST("/v4/api/records/trigger_type")
      suspend fun createTriggerType(@Body request: CreateTriggerTypeRequest,   ): Response<CreateTriggerTypeResponse>



      @POST("/v4/api/records/job")
      suspend fun createJob(@Body request: CreateJobRequest,   ): Response<CreateJobResponse>



      @POST("/v4/api/records/line_items")
      suspend fun createLineItems(@Body request: CreateLineItemsRequest,   ): Response<CreateLineItemsResponse>



      @POST("/v4/api/records/photo")
      suspend fun createPhoto(@Body request: CreatePhotoRequest,   ): Response<CreatePhotoResponse>



      @POST("/v4/api/records/api_keys")
      suspend fun createApiKeys(@Body request: CreateApiKeysRequest,   ): Response<CreateApiKeysResponse>



      @POST("/v4/api/records/change_order_description")
      suspend fun createChangeOrderDescription(@Body request: CreateChangeOrderDescriptionRequest,   ): Response<CreateChangeOrderDescriptionResponse>



      @POST("/v4/api/records/analytic_log")
      suspend fun createAnalyticLog(@Body request: CreateAnalyticLogRequest,   ): Response<CreateAnalyticLogResponse>



      @POST("/v4/api/records/posts")
      suspend fun createPosts(@Body request: CreatePostsRequest,   ): Response<CreatePostsResponse>



      @POST("/v4/api/records/employee")
      suspend fun createEmployee(@Body request: CreateEmployeeRequest,   ): Response<CreateEmployeeResponse>



      @PUT("/v4/api/records/default_square_foot_cost/{id}")
      suspend fun updateDefaultSquareFootCost(@Body request: UpdateDefaultSquareFootCostRequest,         @Path("id") id: Int?,   ): Response<UpdateDefaultSquareFootCostResponse>



      @PUT("/v4/api/records/setting/{id}")
      suspend fun updateSetting(@Body request: UpdateSettingRequest,         @Path("id") id: Int?,   ): Response<UpdateSettingResponse>



      @PUT("/v4/api/records/cost/{id}")
      suspend fun updateCost(@Body request: UpdateCostRequest,         @Path("id") id: Int?,   ): Response<UpdateCostResponse>



      @PUT("/v4/api/records/room/{id}")
      suspend fun updateRoom(@Body request: UpdateRoomRequest,         @Path("id") id: Int?,   ): Response<UpdateRoomResponse>



      @PUT("/v4/api/records/labor/{id}")
      suspend fun updateLabor(@Body request: UpdateLaborRequest,         @Path("id") id: Int?,   ): Response<UpdateLaborResponse>



      @PUT("/v4/api/records/line_item_entry/{id}")
      suspend fun updateLineItemEntry(@Body request: UpdateLineItemEntryRequest,         @Path("id") id: Int?,   ): Response<UpdateLineItemEntryResponse>



      @PUT("/v4/api/records/company_settings/{id}")
      suspend fun updateCompanySettings(@Body request: UpdateCompanySettingsRequest,         @Path("id") id: Int?,   ): Response<UpdateCompanySettingsResponse>



      @PUT("/v4/api/records/cms/{id}")
      suspend fun updateCms(@Body request: UpdateCmsRequest,         @Path("id") id: Int?,   ): Response<UpdateCmsResponse>



      @PUT("/v4/api/records/team_member/{id}")
      suspend fun updateTeamMember(@Body request: UpdateTeamMemberRequest,         @Path("id") id: Int?,   ): Response<UpdateTeamMemberResponse>



      @PUT("/v4/api/records/default_material/{id}")
      suspend fun updateDefaultMaterial(@Body request: UpdateDefaultMaterialRequest,         @Path("id") id: Int?,   ): Response<UpdateDefaultMaterialResponse>



      @PUT("/v3/api/custom/profitpro/company/update-lineal-foot-cost/{id}")
      suspend fun updateLinealFootCost(@Body request: LinearFootReqModel,         @Path("id") id: Int?,   ): Response<CommonResponse>


      @PUT("/v3/api/custom/profitpro/company/update-square-foot-cost/{id}")
      suspend fun updateSquareFootCost(@Body request: LinearFootReqModel,         @Path("id") id: Int?,   ): Response<CommonResponse>



      @PUT("/v4/api/records/project/{id}")
      suspend fun updateProject(@Body request: UpdateProjectRequest,         @Path("id") id: Int?,   ): Response<UpdateProjectResponse>



      @PUT("/v4/api/records/user/{id}")
      suspend fun updateUser(@Body request: UpdateUserRequest,         @Path("id") id: Int?,   ): Response<UpdateUserResponse>



      @PUT("/v4/api/records/profile/{id}")
      suspend fun updateProfile(@Body request: UpdateProfileRequest,         @Path("id") id: Int?,   ): Response<UpdateProfileResponse>



      @PUT("/v4/api/records/lineal_foot_cost/{id}")
      suspend fun updateLinealFootCost(@Body request: UpdateLinealFootCostRequest,         @Path("id") id: Int?,   ): Response<UpdateLinealFootCostResponse>







      @PUT("/v4/api/records/permission/{id}")
      suspend fun updatePermission(@Body request: UpdatePermissionRequest,         @Path("id") id: Int?,   ): Response<UpdatePermissionResponse>



      @PUT("/v4/api/records/token/{id}")
      suspend fun updateToken(@Body request: UpdateTokenRequest,         @Path("id") id: Int?,   ): Response<UpdateTokenResponse>



      @PUT("/v4/api/records/sqft_costs/{id}")
      suspend fun updateSqftCosts(@Body request: UpdateSqftCostsRequest,         @Path("id") id: Int?,   ): Response<UpdateSqftCostsResponse>



      @PUT("/v4/api/records/email/{id}")
      suspend fun updateEmail(@Body request: UpdateEmailRequest,         @Path("id") id: Int?,   ): Response<UpdateEmailResponse>



      @PUT("/v4/api/records/alerts/{id}")
      suspend fun updateAlerts(@Body request: UpdateAlertsRequest,         @Path("id") id: Int?,   ): Response<UpdateAlertsResponse>



      @PUT("/v4/api/records/draws/{id}")
      suspend fun updateDraws(@Body request: UpdateDrawsRequest,         @Path("id") id: Int?,   ): Response<UpdateDrawsResponse>



      @PUT("/v4/api/records/chat/{id}")
      suspend fun updateChat(@Body request: UpdateChatRequest,         @Path("id") id: Int?,   ): Response<UpdateChatResponse>



      @PUT("/v4/api/records/material/{id}")
      suspend fun updateMaterial(@Body request: UpdateMaterialRequest,         @Path("id") id: Int?,   ): Response<UpdateMaterialResponse>



      @PUT("/v4/api/records/invoice/{id}")
      suspend fun updateInvoice(@Body request: UpdateInvoiceRequest,         @Path("id") id: Int?,   ): Response<UpdateInvoiceResponse>



      @PUT("/v4/api/records/default_lineal_foot_cost/{id}")
      suspend fun updateDefaultLinealFootCost(@Body request: UpdateDefaultLinealFootCostRequest,         @Path("id") id: Int?,   ): Response<UpdateDefaultLinealFootCostResponse>



      @PUT("/v4/api/records/trigger_type/{id}")
      suspend fun updateTriggerType(@Body request: UpdateTriggerTypeRequest,         @Path("id") id: Int?,   ): Response<UpdateTriggerTypeResponse>



      @PUT("/v4/api/records/job/{id}")
      suspend fun updateJob(@Body request: UpdateJobRequest,         @Path("id") id: Int?,   ): Response<UpdateJobResponse>



      @PUT("/v4/api/records/line_items/{id}")
      suspend fun updateLineItems(@Body request: UpdateLineItemsRequest,         @Path("id") id: Int?,   ): Response<UpdateLineItemsResponse>



      @PUT("/v4/api/records/photo/{id}")
      suspend fun updatePhoto(@Body request: UpdatePhotoRequest,         @Path("id") id: Int?,   ): Response<UpdatePhotoResponse>



      @PUT("/v4/api/records/api_keys/{id}")
      suspend fun updateApiKeys(@Body request: UpdateApiKeysRequest,         @Path("id") id: Int?,   ): Response<UpdateApiKeysResponse>



      @PUT("/v4/api/records/change_order_description/{id}")
      suspend fun updateChangeOrderDescription(@Body request: UpdateChangeOrderDescriptionRequest,         @Path("id") id: Int?,   ): Response<UpdateChangeOrderDescriptionResponse>



      @PUT("/v4/api/records/analytic_log/{id}")
      suspend fun updateAnalyticLog(@Body request: UpdateAnalyticLogRequest,         @Path("id") id: Int?,   ): Response<UpdateAnalyticLogResponse>



      @PUT("/v4/api/records/posts/{id}")
      suspend fun updatePosts(@Body request: UpdatePostsRequest,         @Path("id") id: Int?,   ): Response<UpdatePostsResponse>



      @PUT("/v4/api/records/employee/{id}")
      suspend fun updateEmployee(@Body request: UpdateEmployeeRequest,         @Path("id") id: Int?,   ): Response<UpdateEmployeeResponse>



      @DELETE("/v4/api/records/default_square_foot_cost/{id}")
      suspend fun deleteDefaultSquareFootCost(         @Path("id") id: Int?,   ): Response<DeleteDefaultSquareFootCostResponse>



      @DELETE("/v4/api/records/setting/{id}")
      suspend fun deleteSetting(         @Path("id") id: Int?,   ): Response<DeleteSettingResponse>



      @DELETE("/v4/api/records/cost/{id}")
      suspend fun deleteCost(         @Path("id") id: Int?,   ): Response<DeleteCostResponse>



      @DELETE("/v4/api/records/room/{id}")
      suspend fun deleteRoom(         @Path("id") id: Int?,   ): Response<DeleteRoomResponse>



      @DELETE("/v4/api/records/labor/{id}")
      suspend fun deleteLabor(         @Path("id") id: Int?,   ): Response<DeleteLaborResponse>



      @DELETE("/v4/api/records/line_item_entry/{id}")
      suspend fun deleteLineItemEntry(         @Path("id") id: Int?,   ): Response<DeleteLineItemEntryResponse>



      @DELETE("/v4/api/records/company_settings/{id}")
      suspend fun deleteCompanySettings(         @Path("id") id: Int?,   ): Response<DeleteCompanySettingsResponse>



      @DELETE("/v4/api/records/cms/{id}")
      suspend fun deleteCms(         @Path("id") id: Int?,   ): Response<DeleteCmsResponse>



      @DELETE("/v4/api/records/team_member/{id}")
      suspend fun deleteTeamMember(         @Path("id") id: Int?,   ): Response<DeleteTeamMemberResponse>



      @DELETE("/v4/api/records/default_material/{id}")
      suspend fun deleteDefaultMaterial(         @Path("id") id: Int?,   ): Response<DeleteDefaultMaterialResponse>


      @DELETE("/v3/api/custom/profitpro/company/delete-lineal-foot-cost/{id}")
      suspend fun deleteLinealFootCosts(         @Path("id") id: Int?,   ): Response<CommonResponse>

      @DELETE("/v3/api/custom/profitpro/company/delete-square-foot-cost/{id}")
      suspend fun deleteSquareFootCost(         @Path("id") id: Int?,   ): Response<CommonResponse>



      @DELETE("/v4/api/records/project/{id}")
      suspend fun deleteProject(         @Path("id") id: Int?,   ): Response<DeleteProjectResponse>



      @DELETE("/v4/api/records/user/{id}")
      suspend fun deleteUser(         @Path("id") id: Int?,   ): Response<DeleteUserResponse>



      @DELETE("/v4/api/records/profile/{id}")
      suspend fun deleteProfile(         @Path("id") id: Int?,   ): Response<DeleteProfileResponse>



      @DELETE("/v4/api/records/lineal_foot_cost/{id}")
      suspend fun deleteLinealFootCost(         @Path("id") id: Int?,   ): Response<DeleteLinealFootCostResponse>



      @DELETE("/v4/api/records/customer/{id}")
      suspend fun deleteCustomer(         @Path("id") id: Int?,   ): Response<DeleteCustomerResponse>



      @DELETE("/v4/api/records/permission/{id}")
      suspend fun deletePermission(         @Path("id") id: Int?,   ): Response<DeletePermissionResponse>



      @DELETE("/v4/api/records/token/{id}")
      suspend fun deleteToken(         @Path("id") id: Int?,   ): Response<DeleteTokenResponse>



      @DELETE("/v4/api/records/sqft_costs/{id}")
      suspend fun deleteSqftCosts(         @Path("id") id: Int?,   ): Response<DeleteSqftCostsResponse>



      @DELETE("/v4/api/records/email/{id}")
      suspend fun deleteEmail(         @Path("id") id: Int?,   ): Response<DeleteEmailResponse>



      @DELETE("/v4/api/records/alerts/{id}")
      suspend fun deleteAlerts(         @Path("id") id: Int?,   ): Response<DeleteAlertsResponse>







      @DELETE("/v4/api/records/chat/{id}")
      suspend fun deleteChat(         @Path("id") id: Int?,   ): Response<DeleteChatResponse>



      @DELETE("/v4/api/records/material/{id}")
      suspend fun deleteMaterial(         @Path("id") id: Int?,   ): Response<DeleteMaterialResponse>



      @DELETE("/v4/api/records/invoice/{id}")
      suspend fun deleteInvoice(         @Path("id") id: Int?,   ): Response<DeleteInvoiceResponse>



      @DELETE("/v4/api/records/default_lineal_foot_cost/{id}")
      suspend fun deleteDefaultLinealFootCost(         @Path("id") id: Int?,   ): Response<DeleteDefaultLinealFootCostResponse>



      @DELETE("/v4/api/records/trigger_type/{id}")
      suspend fun deleteTriggerType(         @Path("id") id: Int?,   ): Response<DeleteTriggerTypeResponse>



      @DELETE("/v4/api/records/job/{id}")
      suspend fun deleteJob(         @Path("id") id: Int?,   ): Response<DeleteJobResponse>







      @DELETE("/v4/api/records/photo/{id}")
      suspend fun deletePhoto(         @Path("id") id: Int?,   ): Response<DeletePhotoResponse>



      @DELETE("/v4/api/records/api_keys/{id}")
      suspend fun deleteApiKeys(         @Path("id") id: Int?,   ): Response<DeleteApiKeysResponse>



      @DELETE("/v4/api/records/change_order_description/{id}")
      suspend fun deleteChangeOrderDescription(         @Path("id") id: Int?,   ): Response<DeleteChangeOrderDescriptionResponse>



      @DELETE("/v4/api/records/analytic_log/{id}")
      suspend fun deleteAnalyticLog(         @Path("id") id: Int?,   ): Response<DeleteAnalyticLogResponse>



      @DELETE("/v4/api/records/posts/{id}")
      suspend fun deletePosts(         @Path("id") id: Int?,   ): Response<DeletePostsResponse>



      @DELETE("/v4/api/records/employee/{id}")
      suspend fun deleteEmployee(         @Path("id") id: Int?,   ): Response<DeleteEmployeeResponse>


    // Subscription API endpoints
    @POST("/v4/api/records/subscription")
    suspend fun createSubscription(@Body request: CreateSubscriptionRequest): Response<CreateSubscriptionResponse>

    @GET("/v4/api/records/plan")
    suspend fun getPlans(): Response<GetPlansResponse>

    @GET("/v4/api/records/subscription")
    suspend fun getUserSubscriptions(@Query("user_id") userId: Int): Response<GetUserSubscriptionsResponse>

    @PUT("/v4/api/records/subscription/{id}")
    suspend fun cancelSubscription(@Body request: CancelSubscriptionRequest, @Path("id") subscriptionId: Int): Response<CancelSubscriptionResponse>

    // Payment History API endpoints
    @GET("/v4/api/records/payment_history")
    suspend fun getPaymentHistory(@Query("user_id") userId: Int): Response<GetPaymentHistoryResponse>

    @GET("/v4/api/records/subscription/{id}/status")
    suspend fun getSubscriptionStatus(@Path("id") subscriptionId: Int): Response<GetSubscriptionStatusResponse>

}
