package com.manaknight.app.viewmodels;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0084\u0011\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u000b\n\u0002\b8\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b%\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\u0010$\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0004\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0010\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\f\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\r\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u000f\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\u0018\u00002\u00020\u0001:\u0002\u009b\bB\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0097\u0003\u0010{\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020|0\u00070=2\b\u0010}\u001a\u0004\u0018\u00010~2\b\u0010\u007f\u001a\u0004\u0018\u00010~2\t\u0010\u0080\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u0081\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u0082\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u0083\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u0084\u0001\u001a\u0004\u0018\u00010C2\t\u0010\u0085\u0001\u001a\u0004\u0018\u00010C2\t\u0010\u0086\u0001\u001a\u0004\u0018\u00010C2\t\u0010\u0087\u0001\u001a\u0004\u0018\u00010C2\t\u0010\u0088\u0001\u001a\u0004\u0018\u00010C2\t\u0010\u0089\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u008a\u0001\u001a\u0004\u0018\u00010C2\t\u0010\u008b\u0001\u001a\u0004\u0018\u00010C2\t\u0010\u008c\u0001\u001a\u0004\u0018\u00010C2\t\u0010\u008d\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u008e\u0001\u001a\u0004\u0018\u00010C2\t\u0010\u008f\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u0090\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u0091\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u0092\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u0093\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u0094\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u0095\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u0096\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u0097\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u0098\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u0099\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u009a\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u009b\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u009c\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u009d\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u009e\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u009f\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00a0\u0001\u001a\u0004\u0018\u00010C\u00a2\u0006\u0003\u0010\u00a1\u0001J\u001d\u0010\u00a2\u0001\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070=2\b\u0010\u00a3\u0001\u001a\u00030\u00a4\u0001JE\u0010\u00a5\u0001\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070=2\t\u0010\u0083\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00a6\u0001\u001a\u0004\u0018\u00010)2\t\u0010\u00a7\u0001\u001a\u0004\u0018\u00010)2\t\u0010\u00a8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00a9\u0001JM\u0010\u00aa\u0001\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070=2\u000b\b\u0002\u0010\u0083\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00a6\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00a7\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00a8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00a9\u0001JE\u0010\u00ab\u0001\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070=2\t\u0010\u0083\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00a6\u0001\u001a\u0004\u0018\u00010)2\t\u0010\u00a7\u0001\u001a\u0004\u0018\u00010)2\t\u0010\u00a8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00a9\u0001JM\u0010\u00ac\u0001\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070=2\u000b\b\u0002\u0010\u0083\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00a6\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00a7\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00a8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00a9\u0001J\u00b5\u0002\u0010\u00ad\u0001\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00ae\u00010\u00070=2\t\u0010\u00af\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00b0\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00b1\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00b2\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00b3\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00b4\u0001\u001a\u0004\u0018\u00010~25\u0010\u00b5\u0001\u001a0\u0012\u0012\u0012\u0010\u0012\u0004\u0012\u00020~\u0012\u0005\u0012\u00030\u00b8\u00010\u00b7\u00010\u00b6\u0001j\u0017\u0012\u0012\u0012\u0010\u0012\u0004\u0012\u00020~\u0012\u0005\u0012\u00030\u00b8\u00010\u00b7\u0001`\u00b9\u00012\n\u0010\u00ba\u0001\u001a\u0005\u0018\u00010\u00bb\u000125\u0010\u00bc\u0001\u001a0\u0012\u0012\u0012\u0010\u0012\u0004\u0012\u00020~\u0012\u0005\u0012\u00030\u00b8\u00010\u00b7\u00010\u00b6\u0001j\u0017\u0012\u0012\u0012\u0010\u0012\u0004\u0012\u00020~\u0012\u0005\u0012\u00030\u00b8\u00010\u00b7\u0001`\u00b9\u00012\t\u0010\u00bd\u0001\u001a\u0004\u0018\u00010~25\u0010\u00be\u0001\u001a0\u0012\u0012\u0012\u0010\u0012\u0004\u0012\u00020~\u0012\u0005\u0012\u00030\u00b8\u00010\u00b7\u00010\u00b6\u0001j\u0017\u0012\u0012\u0012\u0010\u0012\u0004\u0012\u00020~\u0012\u0005\u0012\u00030\u00b8\u00010\u00b7\u0001`\u00b9\u00012\n\u0010\u00bf\u0001\u001a\u0005\u0018\u00010\u00bb\u00012\u0015\u0010\u0082\u0001\u001a\u0010\u0012\u0004\u0012\u00020~\u0012\u0005\u0012\u00030\u00b8\u00010\u00b7\u0001J5\u0010\u00c0\u0001\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00c1\u00010\u00070=2\t\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00c3\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~J0\u0010\u00c5\u0001\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00c6\u00010\u00070=2\t\u0010\u00c7\u0001\u001a\u0004\u0018\u00010)2\t\u0010\u00c8\u0001\u001a\u0004\u0018\u00010~\u00a2\u0006\u0003\u0010\u00c9\u0001J*\u0010\u00ca\u0001\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00cb\u00010\u00070=2\t\u0010\u00cc\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00cd\u0001\u001a\u0004\u0018\u00010~J\u0014\u0010\u00ce\u0001\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00cf\u00010\u00070=JK\u0010\u00d0\u0001\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00d1\u00010\u00070=2\t\u0010\u00d2\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00d3\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00d4\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00d5\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00d6\u0001\u001a\u0004\u0018\u00010~J&\u0010\u00d7\u0001\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00d8\u00010\u00070=2\u0007\u0010\u00d9\u0001\u001a\u00020)2\u0007\u0010\u00da\u0001\u001a\u00020)J\u00d0\u0001\u0010\u00db\u0001\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00dc\u00010\u00070=2\t\u0010\u00dd\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00de\u0001\u001a\u0004\u0018\u00010~2\u0015\u0010\u00df\u0001\u001a\u0010\u0012\u0004\u0012\u00020~\u0012\u0005\u0012\u00030\u00b8\u00010\u00b7\u000125\u0010\u00e0\u0001\u001a0\u0012\u0012\u0012\u0010\u0012\u0004\u0012\u00020~\u0012\u0005\u0012\u00030\u00b8\u00010\u00b7\u00010\u00b6\u0001j\u0017\u0012\u0012\u0012\u0010\u0012\u0004\u0012\u00020~\u0012\u0005\u0012\u00030\u00b8\u00010\u00b7\u0001`\u00b9\u000125\u0010\u00e1\u0001\u001a0\u0012\u0012\u0012\u0010\u0012\u0004\u0012\u00020~\u0012\u0005\u0012\u00030\u00b8\u00010\u00b7\u00010\u00b6\u0001j\u0017\u0012\u0012\u0012\u0010\u0012\u0004\u0012\u00020~\u0012\u0005\u0012\u00030\u00b8\u00010\u00b7\u0001`\u00b9\u00012\t\u0010\u00e2\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u009f\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00e3\u0001\u001a\u0004\u0018\u00010~J\u001f\u0010\u00e4\u0001\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00e5\u00010\u00070=2\t\u0010\u00c8\u0001\u001a\u0004\u0018\u00010~J\u00d0\u0001\u0010\u00e6\u0001\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00e7\u00010\u00070=2\t\u0010\u00dd\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00e2\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u009f\u0001\u001a\u0004\u0018\u00010~2\u0015\u0010\u00df\u0001\u001a\u0010\u0012\u0004\u0012\u00020~\u0012\u0005\u0012\u00030\u00b8\u00010\u00b7\u000125\u0010\u00e0\u0001\u001a0\u0012\u0012\u0012\u0010\u0012\u0004\u0012\u00020~\u0012\u0005\u0012\u00030\u00b8\u00010\u00b7\u00010\u00b6\u0001j\u0017\u0012\u0012\u0012\u0010\u0012\u0004\u0012\u00020~\u0012\u0005\u0012\u00030\u00b8\u00010\u00b7\u0001`\u00b9\u000125\u0010\u00e1\u0001\u001a0\u0012\u0012\u0012\u0010\u0012\u0004\u0012\u00020~\u0012\u0005\u0012\u00030\u00b8\u00010\u00b7\u00010\u00b6\u0001j\u0017\u0012\u0012\u0012\u0010\u0012\u0004\u0012\u00020~\u0012\u0005\u0012\u00030\u00b8\u00010\u00b7\u0001`\u00b9\u00012\t\u0010\u00e3\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00c8\u0001\u001a\u0004\u0018\u00010~J\u00aa\u0001\u0010\u00e8\u0001\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00e9\u00010\u00070=25\u0010\u00e1\u0001\u001a0\u0012\u0012\u0012\u0010\u0012\u0004\u0012\u00020~\u0012\u0005\u0012\u00030\u00b8\u00010\u00b7\u00010\u00b6\u0001j\u0017\u0012\u0012\u0012\u0010\u0012\u0004\u0012\u00020~\u0012\u0005\u0012\u00030\u00b8\u00010\u00b7\u0001`\u00b9\u000125\u0010\u00e0\u0001\u001a0\u0012\u0012\u0012\u0010\u0012\u0004\u0012\u00020~\u0012\u0005\u0012\u00030\u00b8\u00010\u00b7\u00010\u00b6\u0001j\u0017\u0012\u0012\u0012\u0010\u0012\u0004\u0012\u00020~\u0012\u0005\u0012\u00030\u00b8\u00010\u00b7\u0001`\u00b9\u00012\t\u0010\u00ea\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00eb\u0001\u001a\u0004\u0018\u00010~2\u0007\u0010\u00d9\u0001\u001a\u00020)2\u0007\u0010\u00c3\u0001\u001a\u00020)J\u001d\u0010\u00ec\u0001\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00ed\u00010\u00070=2\u0007\u0010\u00ee\u0001\u001a\u00020)J\u001f\u0010\u00ef\u0001\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00f0\u00010\u00070=2\t\u0010\u00c8\u0001\u001a\u0004\u0018\u00010~J\u001f\u0010\u00f1\u0001\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00f2\u00010\u00070=2\t\u0010\u0083\u0001\u001a\u0004\u0018\u00010~J\u001f\u0010\u00f3\u0001\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00f4\u00010\u00070=2\t\u0010\u00c8\u0001\u001a\u0004\u0018\u00010~J1\u0010\u00f5\u0001\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00f6\u00010\u00070=2\u0007\u0010\u00d9\u0001\u001a\u00020)2\u0007\u0010\u00c3\u0001\u001a\u00020)2\t\u0010\u0083\u0001\u001a\u0004\u0018\u00010~J\u001f\u0010\u00f7\u0001\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00f8\u00010\u00070=2\t\u0010\u0083\u0001\u001a\u0004\u0018\u00010~J\u0013\u0010\u00f9\u0001\u001a\u00030\u00fa\u00012\t\u0010\u00fb\u0001\u001a\u0004\u0018\u00010\u000bJ%\u0010\u00fc\u0001\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\u00070=2\u0007\u0010\u00fd\u0001\u001a\u00020)2\u0007\u0010\u00fe\u0001\u001a\u00020~J&\u0010\u00ff\u0001\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u0080\u00020\u00070=2\u0007\u0010\u0081\u0002\u001a\u00020)2\u0007\u0010\u0092\u0001\u001a\u00020)J&\u0010\u0082\u0002\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u0083\u00020\u00070=2\u0007\u0010\u0081\u0002\u001a\u00020)2\u0007\u0010\u0092\u0001\u001a\u00020)J\b\u0010\u0084\u0002\u001a\u00030\u00fa\u0001J\b\u0010\u0085\u0002\u001a\u00030\u00fa\u0001J\b\u0010\u0086\u0002\u001a\u00030\u00fa\u0001J\b\u0010\u0087\u0002\u001a\u00030\u00fa\u0001J\b\u0010\u0088\u0002\u001a\u00030\u00fa\u0001J\b\u0010\u0089\u0002\u001a\u00030\u00fa\u0001J\b\u0010\u008a\u0002\u001a\u00030\u00fa\u0001J\b\u0010\u008b\u0002\u001a\u00030\u00fa\u0001J\b\u0010\u008c\u0002\u001a\u00030\u00fa\u0001J\b\u0010\u008d\u0002\u001a\u00030\u00fa\u0001J\b\u0010\u008e\u0002\u001a\u00030\u00fa\u0001J\b\u0010\u008f\u0002\u001a\u00030\u00fa\u0001J\b\u0010\u0090\u0002\u001a\u00030\u00fa\u0001J\b\u0010\u0091\u0002\u001a\u00030\u00fa\u0001J\b\u0010\u0092\u0002\u001a\u00030\u00fa\u0001J\u0014\u0010\u0093\u0002\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u0094\u00020\u00070=J\u0014\u0010\u0095\u0002\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u0096\u00020\u00070=JN\u0010\u0097\u0002\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u0098\u00020\u00070=2\u000b\b\u0002\u0010\u00af\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u0099\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u008f\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c7\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u009a\u0002J\u0082\u0001\u0010\u009b\u0002\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u009c\u00020\u00070=2\u000b\b\u0002\u0010\u00af\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00b5\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u009d\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u009e\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u009f\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00d6\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00a0\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00a1\u0002\u001a\u0004\u0018\u00010~\u00a2\u0006\u0003\u0010\u00a2\u0002J;\u0010\u00a3\u0002\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00a4\u00020\u00070=2\u000b\b\u0002\u0010\u00a5\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00a6\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u009f\u0001\u001a\u0004\u0018\u00010~J*\u0010\u00a7\u0002\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00a8\u00020\u00070=2\t\u0010\u0083\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00a9\u0002\u001a\u0004\u0018\u00010~J@\u0010\u00aa\u0002\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00ab\u00020\u00070=2\t\u0010\u00c3\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00a5\u0002\u001a\u0004\u0018\u00010~2\t\u0010\u0080\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00a6\u0002\u001a\u0004\u0018\u00010~J)\u0010\u00ac\u0002\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00ad\u00020\u00070=2\t\u0010\u0082\u0001\u001a\u0004\u0018\u00010~2\b\u0010\u00ae\u0002\u001a\u00030\u00b8\u0001J4\u0010\u00af\u0002\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00b0\u00020\u00070=2\u000b\b\u0002\u0010\u009f\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b1\u0002\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00b2\u0002JA\u0010\u00b3\u0002\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00b4\u00020\u00070=2\u000b\b\u0002\u0010\u00b5\u0002\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00b6\u0002\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00b7\u0002\u001a\u0004\u0018\u00010~\u00a2\u0006\u0003\u0010\u00b8\u0002JH\u0010\u00b9\u0002\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00ba\u00020\u00070=2\u000b\b\u0002\u0010\u00c3\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00bb\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00bc\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00bd\u0002\u001a\u0004\u0018\u00010~J=\u0010\u00be\u0002\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00bf\u00020\u00070=2\t\u0010\u00c0\u0002\u001a\u0004\u0018\u00010~2\t\u0010\u00c1\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00af\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00c2\u0002Ju\u0010\u00c3\u0002\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00c4\u00020\u00070=2\u000b\b\u0002\u0010\u00c5\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u0083\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c6\u0002\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00c7\u0002\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00a7\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00c8\u0002\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00c9\u0002\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00ca\u0002JZ\u0010\u00cb\u0002\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070=2\u000b\b\u0002\u0010\u0083\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00cc\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00cd\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00ce\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00af\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00cf\u0002JW\u0010\u00d0\u0002\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00d1\u00020\u00070=2\u000b\b\u0002\u0010\u0083\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00a6\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00a7\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00af\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00a8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00d2\u0002JM\u0010\u00d3\u0002\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070=2\u000b\b\u0002\u0010\u00af\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00a6\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u0083\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00a8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00d4\u0002JW\u0010\u00d5\u0002\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00d6\u00020\u00070=2\u000b\b\u0002\u0010\u0083\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00a6\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00a7\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00af\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00a8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00d2\u0002JZ\u0010\u00d7\u0002\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070=2\u000b\b\u0002\u0010\u00ae\u0002\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00d8\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u009f\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00d9\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b1\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00da\u0002JG\u0010\u00db\u0002\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00dc\u00020\u00070=2\n\b\u0002\u0010}\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00dd\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00de\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00df\u0002\u001a\u0004\u0018\u00010~JA\u0010\u00e0\u0002\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00e1\u00020\u00070=2\u000b\b\u0002\u0010\u00af\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00c0\u0002\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00c1\u0002\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00e2\u0002Jb\u0010\u00e3\u0002\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00e4\u00020\u00070=2\u000b\b\u0002\u0010\u00e5\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u0083\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00cc\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00ce\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00e6\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00e7\u0002\u001a\u0004\u0018\u00010~J\u008f\u0001\u0010\u00e8\u0002\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00e9\u00020\u00070=2\u000b\b\u0002\u0010\u00ea\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00eb\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00ec\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00ed\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00ee\u0002\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00ef\u0002\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00f0\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00f1\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b1\u0001\u001a\u0004\u0018\u00010~\u00a2\u0006\u0003\u0010\u00f2\u0002JN\u0010\u00f3\u0002\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00f4\u00020\u00070=2\u000b\b\u0002\u0010\u0083\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00f5\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00d8\u0002\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00f6\u0002\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00f7\u0002Jb\u0010\u00f8\u0002\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00f9\u00020\u00070=2\u000b\b\u0002\u0010\u00a9\u0002\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u0083\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00a6\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00a7\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b1\u0002\u001a\u0004\u0018\u00010)2\t\u0010\u0081\u0001\u001a\u0004\u0018\u00010~\u00a2\u0006\u0003\u0010\u00fa\u0002JY\u0010\u00fb\u0002\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00fc\u00020\u00070=2\u000b\b\u0002\u0010\u00a8\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00ae\u0002\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u009f\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00fd\u0002\u001a\u0004\u0018\u00010~2\t\u0010\u00fe\u0002\u001a\u0004\u0018\u00010~\u00a2\u0006\u0003\u0010\u00ff\u0002J[\u0010\u0080\u0003\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u0081\u00030\u00070=2\u000b\b\u0002\u0010\u0083\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c7\u0002\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00c9\u0002\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00a7\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00c8\u0002\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u0082\u0003JA\u0010\u0083\u0003\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u0084\u00030\u00070=2\u000b\b\u0002\u0010\u0083\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c6\u0002\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00ae\u0002\u001a\u0004\u0018\u00010~\u00a2\u0006\u0003\u0010\u0085\u0003Jj\u0010\u0083\u0003\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070=2\u000b\b\u0002\u0010\u0083\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00cc\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00af\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u0086\u0003\u001a\u0004\u0018\u00010~2\u0007\u0010\u00a6\u0001\u001a\u00020)2\t\b\u0002\u0010\u00a8\u0001\u001a\u00020)2\u0007\u0010\u0087\u0003\u001a\u00020)\u00a2\u0006\u0003\u0010\u0088\u0003Jj\u0010\u0089\u0003\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070=2\u000b\b\u0002\u0010\u0083\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00cc\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00af\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u0086\u0003\u001a\u0004\u0018\u00010~2\u0007\u0010\u00a6\u0001\u001a\u00020)2\t\b\u0002\u0010\u00a8\u0001\u001a\u00020)2\u0007\u0010\u0087\u0003\u001a\u00020)\u00a2\u0006\u0003\u0010\u0088\u0003J$\u0010\u008a\u0003\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070=2\t\u0010\u008b\u0003\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u008c\u0003J:\u0010\u008d\u0003\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070=2\t\u0010\u008e\u0003\u001a\u0004\u0018\u00010)2\t\u0010\u009f\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u008f\u0003\u001a\u0004\u0018\u00010~\u00a2\u0006\u0003\u0010\u0090\u0003J.\u0010\u0091\u0003\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u0092\u00030\u00070=2\u000b\b\u0002\u0010\u00d6\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u0093\u0003\u001a\u0004\u0018\u00010~Jh\u0010\u0094\u0003\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u0095\u00030\u00070=2\u000b\b\u0002\u0010\u00b5\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u0096\u0003\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00af\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u0081\u0002\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u0092\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u0080\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u0097\u0003JN\u0010\u0098\u0003\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u0099\u00030\u00070=2\u000b\b\u0002\u0010\u00b1\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u0080\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u0082\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u009a\u0003\u001a\u0004\u0018\u00010~\u00a2\u0006\u0003\u0010\u009b\u0003J:\u0010\u009c\u0003\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070=2\t\u0010\u008e\u0003\u001a\u0004\u0018\u00010)2\t\u0010\u009f\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00d8\u0002\u001a\u0004\u0018\u00010~\u00a2\u0006\u0003\u0010\u0090\u0003JN\u0010\u009d\u0003\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u009e\u00030\u00070=2\u000b\b\u0002\u0010\u00af\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u009f\u0003\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00a0\u0003\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00a1\u0003\u001a\u0004\u0018\u00010~\u00a2\u0006\u0003\u0010\u009b\u0003Jd\u0010\u00a2\u0003\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00a3\u00030\u00070=2\u000b\b\u0002\u0010\u00a4\u0003\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00a5\u0003\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00af\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00b1\u0001\u001a\u0004\u0018\u00010)2\t\u0010\u00c9\u0002\u001a\u0004\u0018\u00010~2\t\u0010\u00a6\u0003\u001a\u0004\u0018\u00010~\u00a2\u0006\u0003\u0010\u00a7\u0003Jh\u0010\u00a8\u0003\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00a9\u00030\u00070=2\u000b\b\u0002\u0010\u00af\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00aa\u0003\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00ab\u0003\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00b6\u0002\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00ac\u0003\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00ad\u0003\u001a\u0004\u0018\u00010~\u00a2\u0006\u0003\u0010\u00a7\u0003J.\u0010\u00ae\u0003\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00a9\u00030\u00070=2\u0007\u0010\u00af\u0003\u001a\u00020)2\t\u0010\u00b0\u0003\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00b1\u0003J.\u0010\u00b2\u0003\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00b3\u00030\u00070=2\u000b\b\u0002\u0010\u00b4\u0003\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b5\u0003\u001a\u0004\u0018\u00010~J[\u0010\u00b6\u0003\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00b7\u00030\u00070=2\u000b\b\u0002\u0010\u0083\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c7\u0002\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00c9\u0002\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00a7\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00c8\u0002\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u0082\u0003J8\u0010\u00b8\u0003\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00b9\u00030\u00070=2\u0007\u0010\u00af\u0001\u001a\u00020)2\u0007\u0010\u00b1\u0001\u001a\u00020)2\u0007\u0010\u00ba\u0003\u001a\u00020~2\u0007\u0010\u00d8\u0002\u001a\u00020~JZ\u0010\u00bb\u0003\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00100\u00070=2\u000b\b\u0002\u0010\u00ae\u0002\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00af\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u0083\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00a6\u0003\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00bc\u0003\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00bd\u0003JZ\u0010\u00be\u0003\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00100\u00070=2\u000b\b\u0002\u0010\u00ae\u0002\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00af\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u0083\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00a6\u0003\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00bc\u0003\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00bd\u0003Jh\u0010\u00bf\u0003\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00c0\u00030\u00070=2\u000b\b\u0002\u0010\u00c1\u0003\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u0080\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u0082\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00af\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00b1\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00c2\u0003\u001a\u0004\u0018\u00010~\u00a2\u0006\u0003\u0010\u00c3\u0003J!\u0010\u00c4\u0003\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00c5\u00030\u00070=2\u000b\b\u0002\u0010\u0083\u0001\u001a\u0004\u0018\u00010~J\u00ea\u0001\u0010\u00c6\u0003\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00c7\u00030\u00070=2\u000b\b\u0002\u0010\u00c8\u0003\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00d6\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00d2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00d3\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00cc\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c9\u0003\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u0080\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00ca\u0003\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00cd\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00e5\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00cb\u0003\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00cc\u0003\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00cd\u0003\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00ce\u0003\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00cf\u0003\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00b1\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00d0\u0003J\u00a6\u0001\u0010\u00d1\u0003\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00d2\u00030\u00070=2\u0007\u0010\u00af\u0001\u001a\u00020)2\u0007\u0010\u00b0\u0001\u001a\u00020)2\t\u0010\u00b1\u0001\u001a\u0004\u0018\u00010~25\u0010\u00be\u0001\u001a0\u0012\u0012\u0012\u0010\u0012\u0004\u0012\u00020~\u0012\u0005\u0012\u00030\u00b8\u00010\u00b7\u00010\u00b6\u0001j\u0017\u0012\u0012\u0012\u0010\u0012\u0004\u0012\u00020~\u0012\u0005\u0012\u00030\u00b8\u00010\u00b7\u0001`\u00b9\u00012\u0007\u0010\u00d3\u0003\u001a\u00020)2\u0007\u0010\u00d4\u0003\u001a\u00020)2\t\u0010\u00d5\u0003\u001a\u0004\u0018\u00010~2\t\u0010\u00d6\u0003\u001a\u0004\u0018\u00010~2\t\u0010\u00d7\u0003\u001a\u0004\u0018\u00010~2\t\u0010\u00d8\u0003\u001a\u0004\u0018\u00010~J\'\u0010\u00d9\u0003\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00da\u00030\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u008c\u0003J\'\u0010\u00db\u0003\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00dc\u00030\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u008c\u0003J\'\u0010\u00dd\u0003\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00de\u00030\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u008c\u0003J\u001f\u0010\u00df\u0003\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00e0\u00030\u00070=2\t\u0010\u00c8\u0001\u001a\u0004\u0018\u00010~J\u001f\u0010\u00e1\u0003\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00e2\u00030\u00070=2\t\u0010\u00c8\u0001\u001a\u0004\u0018\u00010~J\'\u0010\u00e3\u0003\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00e4\u00030\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u008c\u0003J\'\u0010\u00e5\u0003\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00e6\u00030\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u008c\u0003J\'\u0010\u00e7\u0003\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00e8\u00030\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u008c\u0003J\'\u0010\u00e9\u0003\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00ea\u00030\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u008c\u0003J\'\u0010\u00eb\u0003\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00ec\u00030\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u008c\u0003J\'\u0010\u00ed\u0003\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00ee\u00030\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u008c\u0003J\'\u0010\u00ef\u0003\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00f0\u00030\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u008c\u0003J&\u0010\u00f1\u0003\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00120\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u008c\u0003J&\u0010\u00f2\u0003\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00120\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u008c\u0003J\'\u0010\u00f3\u0003\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00f4\u00030\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u008c\u0003J&\u0010\u00f5\u0003\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u008c\u0003J \u0010\u00f6\u0003\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00f7\u00030\u00070=2\n\u0010\u00c8\u0001\u001a\u0005\u0018\u00010\u00bb\u0001J\'\u0010\u00f8\u0003\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00f9\u00030\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u008c\u0003J\'\u0010\u00fa\u0003\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00fb\u00030\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u008c\u0003J\'\u0010\u00fc\u0003\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00fd\u00030\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u008c\u0003J\'\u0010\u00fe\u0003\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00ff\u00030\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u008c\u0003J\'\u0010\u0080\u0004\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u0081\u00040\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u008c\u0003J\'\u0010\u0082\u0004\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u0083\u00040\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u008c\u0003J&\u0010\u0084\u0004\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u008c\u0003J\'\u0010\u0085\u0004\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u0086\u00040\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u008c\u0003J&\u0010\u0087\u0004\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u008c\u0003J&\u0010\u0088\u0004\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u008c\u0003J\'\u0010\u0089\u0004\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u008a\u00040\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u008c\u0003J\'\u0010\u008b\u0004\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u008c\u00040\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u008c\u0003J\'\u0010\u008d\u0004\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u008e\u00040\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u008c\u0003J\'\u0010\u008f\u0004\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u0090\u00040\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u008c\u0003J\'\u0010\u0091\u0004\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u0092\u00040\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u008c\u0003J\'\u0010\u0093\u0004\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u0094\u00040\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u008c\u0003J\'\u0010\u0095\u0004\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u0096\u00040\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u008c\u0003J\'\u0010\u0097\u0004\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u0098\u00040\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u008c\u0003J\'\u0010\u0099\u0004\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u009a\u00040\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u008c\u0003J&\u0010\u009b\u0004\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u008c\u0003J&\u0010\u009c\u0004\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u008c\u0003J&\u0010\u009d\u0004\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00160\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u008c\u0003J&\u0010\u009e\u0004\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00160\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u008c\u0003J\'\u0010\u009f\u0004\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00a0\u00040\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u008c\u0003J\'\u0010\u00a1\u0004\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00a2\u00040\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u008c\u0003J\'\u0010\u00a3\u0004\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00a4\u00040\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u008c\u0003J;\u0010\u00a5\u0004\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00a6\u00040\u00070=2\t\u0010\u00af\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00a7\u0004\u001a\u0004\u0018\u00010)2\t\u0010\u0081\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00a8\u0004J0\u0010\u00a9\u0004\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00aa\u00040\u00070=2\t\u0010\u00ab\u0004\u001a\u0004\u0018\u00010~2\t\u0010\u00a7\u0004\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00b2\u0002J*\u0010\u00ac\u0004\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00ad\u00040\u00070=2\t\u0010\u00af\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u0082\u0001\u001a\u0004\u0018\u00010~J%\u0010\u00ae\u0004\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00af\u00040\u00070=2\t\u0010\u00a7\u0004\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u008c\u0003J\u0014\u0010\u00b0\u0004\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00b1\u00040\u00070=J7\u0010\u00b2\u0004\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00b3\u00040\u00070=2\u0015\u0010\u00b4\u0004\u001a\u0010\u0012\u0004\u0012\u00020~\u0012\u0005\u0012\u00030\u00b8\u00010\u00b7\u00012\n\u0010\u00c8\u0001\u001a\u0005\u0018\u00010\u00bb\u0001J<\u0010\u00b5\u0004\u001a\u00030\u00fa\u00012\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b6\u0004\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~J\u0013\u0010\u00b8\u0004\u001a\u00030\u00fa\u00012\u0007\u0010\u00af\u0001\u001a\u00020)H\u0002J\b\u0010\u00b9\u0004\u001a\u00030\u00fa\u0001J\u0011\u0010\u00ba\u0004\u001a\u00030\u00fa\u00012\u0007\u0010\u00bb\u0004\u001a\u00020)J\u0011\u0010\u00bc\u0004\u001a\u00030\u00fa\u00012\u0007\u0010\u00ae\u0002\u001a\u00020)J\u0013\u0010\u00bd\u0004\u001a\u00030\u00fa\u00012\u0007\u0010\u00af\u0001\u001a\u00020)H\u0002J\u0013\u0010\u00be\u0004\u001a\u00030\u00fa\u00012\t\u0010\u0080\u0001\u001a\u0004\u0018\u00010~J\u0013\u0010\u00bf\u0004\u001a\u00030\u00fa\u00012\u0007\u0010\u00c0\u0004\u001a\u00020-H\u0002J\u0013\u0010\u00c1\u0004\u001a\u00030\u00fa\u00012\u0007\u0010\u00af\u0001\u001a\u00020)H\u0002J\u0014\u0010\u00c2\u0004\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00c3\u00040\u00070=J\u0014\u0010\u00c4\u0004\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00c5\u00040\u00070=J*\u0010\u00c6\u0004\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00c7\u00040\u00070=2\t\u0010\u00cc\u0002\u001a\u0004\u0018\u00010~2\t\u0010\u00d6\u0001\u001a\u0004\u0018\u00010~J\u001f\u0010\u00c8\u0004\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00c9\u00040\u00070=2\t\u0010\u00cc\u0002\u001a\u0004\u0018\u00010~JH\u0010\u00ca\u0004\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00cb\u00040\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b6\u0004\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JH\u0010\u00cc\u0004\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00cd\u00040\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c3\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~J\u0014\u0010\u00ce\u0004\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00cf\u00040\u00070=J\u001d\u0010\u00d0\u0004\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00d1\u00040\u00070=2\u0007\u0010\u00ae\u0002\u001a\u00020)J\u001d\u0010\u00d2\u0004\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00d3\u00040\u00070=2\u0007\u0010\u00af\u0003\u001a\u00020)J\u0014\u0010\u00d4\u0004\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00d5\u00040\u00070=JH\u0010\u00d6\u0004\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00d7\u00040\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b6\u0004\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JH\u0010\u00d8\u0004\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00d9\u00040\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c3\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~J\u0014\u0010\u00da\u0004\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00db\u00040\u00070=JH\u0010\u00dc\u0004\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00dd\u00040\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b6\u0004\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JH\u0010\u00de\u0004\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00df\u00040\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c3\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~J1\u0010\u00e0\u0004\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00e1\u00040\u00070=2\u0007\u0010\u00d9\u0001\u001a\u00020)2\u0007\u0010\u00c3\u0001\u001a\u00020)2\t\u0010\u0083\u0001\u001a\u0004\u0018\u00010~J\u001f\u0010\u00e2\u0004\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00e3\u00040\u00070=2\t\u0010\u00c8\u0001\u001a\u0004\u0018\u00010~J\u001f\u0010\u00e4\u0004\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00e5\u00040\u00070=2\t\u0010\u00c8\u0001\u001a\u0004\u0018\u00010~J*\u0010\u00e6\u0004\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00e7\u00040\u00070=2\t\u0010\u00c3\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00a5\u0002\u001a\u0004\u0018\u00010~J\u001f\u0010\u00e8\u0004\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00e9\u00040\u00070=2\t\u0010\u00c3\u0001\u001a\u0004\u0018\u00010~J\u001f\u0010\u00ea\u0004\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00eb\u00040\u00070=2\t\u0010\u00af\u0001\u001a\u0004\u0018\u00010~JH\u0010\u00ec\u0004\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00ed\u00040\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b6\u0004\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JH\u0010\u00ee\u0004\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00ef\u00040\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c3\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JH\u0010\u00f0\u0004\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00f1\u00040\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b6\u0004\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JH\u0010\u00f2\u0004\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00f3\u00040\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c3\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~J\u001d\u0010\u00f4\u0004\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00f5\u00040\u00070=2\u0007\u0010\u00f6\u0004\u001a\u00020~JH\u0010\u00f7\u0004\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00f8\u00040\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b6\u0004\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JH\u0010\u00f9\u0004\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00fa\u00040\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c3\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JH\u0010\u00fb\u0004\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00fc\u00040\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b6\u0004\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JH\u0010\u00fd\u0004\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00fe\u00040\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c3\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JH\u0010\u00ff\u0004\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u0080\u00050\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b6\u0004\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JH\u0010\u0081\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u0082\u00050\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c3\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JH\u0010\u0083\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u0084\u00050\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b6\u0004\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JH\u0010\u0085\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u0086\u00050\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c3\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JH\u0010\u0087\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u0088\u00050\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b6\u0004\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JH\u0010\u0089\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u008a\u00050\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c3\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JG\u0010\u008b\u0005\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020!0\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b6\u0004\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JH\u0010\u008c\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u008d\u00050\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c3\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JH\u0010\u008e\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u008f\u00050\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b6\u0004\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JH\u0010\u0090\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u0091\u00050\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c3\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JH\u0010\u0092\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u0093\u00050\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b6\u0004\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JH\u0010\u0094\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u0095\u00050\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c3\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JH\u0010\u0096\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u0097\u00050\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b6\u0004\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JH\u0010\u0098\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u0099\u00050\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c3\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JH\u0010\u009a\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u009b\u00050\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b6\u0004\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JH\u0010\u009c\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u009d\u00050\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c3\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~J\u001f\u0010\u009e\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u009f\u00050\u00070=2\t\u0010\u00a0\u0005\u001a\u0004\u0018\u00010~JH\u0010\u00a1\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00a2\u00050\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b6\u0004\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JH\u0010\u00a3\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00a4\u00050\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c3\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JH\u0010\u00a5\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00a6\u00050\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b6\u0004\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JH\u0010\u00a7\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00a8\u00050\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c3\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JH\u0010\u00a9\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00aa\u00050\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b6\u0004\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JH\u0010\u00ab\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00ac\u00050\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c3\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~J\u001e\u0010\u00ad\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00ae\u00050\u00070=2\b\u0010\u00af\u0005\u001a\u00030\u00b8\u0001JH\u0010\u00b0\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00b1\u00050\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b6\u0004\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JH\u0010\u00b2\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00b3\u00050\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c3\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JH\u0010\u00b4\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00b5\u00050\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b6\u0004\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JH\u0010\u00b6\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00b7\u00050\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c3\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JH\u0010\u00b8\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00b9\u00050\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b6\u0004\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JH\u0010\u00ba\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00bb\u00050\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c3\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JH\u0010\u00bc\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00bd\u00050\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b6\u0004\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JH\u0010\u00be\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00bf\u00050\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c3\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~J4\u0010\u00c0\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00c1\u00050\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~\u00a2\u0006\u0003\u0010\u00c9\u0001J4\u0010\u00c2\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00c3\u00050\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~\u00a2\u0006\u0003\u0010\u00c9\u0001J4\u0010\u00c4\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00c5\u00050\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~\u00a2\u0006\u0003\u0010\u00c9\u0001J4\u0010\u00c6\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00c7\u00050\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~\u00a2\u0006\u0003\u0010\u00c9\u0001J4\u0010\u00c8\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00c9\u00050\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~\u00a2\u0006\u0003\u0010\u00c9\u0001J4\u0010\u00ca\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00cb\u00050\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~\u00a2\u0006\u0003\u0010\u00c9\u0001J4\u0010\u00cc\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00cd\u00050\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~\u00a2\u0006\u0003\u0010\u00c9\u0001J4\u0010\u00ce\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00cf\u00050\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~\u00a2\u0006\u0003\u0010\u00c9\u0001J4\u0010\u00d0\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00d1\u00050\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~\u00a2\u0006\u0003\u0010\u00c9\u0001J4\u0010\u00d2\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00d3\u00050\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~\u00a2\u0006\u0003\u0010\u00c9\u0001J4\u0010\u00d4\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00d5\u00050\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~\u00a2\u0006\u0003\u0010\u00c9\u0001J4\u0010\u00d6\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00d7\u00050\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~\u00a2\u0006\u0003\u0010\u00c9\u0001J4\u0010\u00d8\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00d9\u00050\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~\u00a2\u0006\u0003\u0010\u00c9\u0001J4\u0010\u00da\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00db\u00050\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~\u00a2\u0006\u0003\u0010\u00c9\u0001J4\u0010\u00dc\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00dd\u00050\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~\u00a2\u0006\u0003\u0010\u00c9\u0001J4\u0010\u00de\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00df\u00050\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~\u00a2\u0006\u0003\u0010\u00c9\u0001J4\u0010\u00e0\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00e1\u00050\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~\u00a2\u0006\u0003\u0010\u00c9\u0001J4\u0010\u00e2\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00e3\u00050\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~\u00a2\u0006\u0003\u0010\u00c9\u0001J4\u0010\u00e4\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00e5\u00050\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~\u00a2\u0006\u0003\u0010\u00c9\u0001J4\u0010\u00e6\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00e7\u00050\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~\u00a2\u0006\u0003\u0010\u00c9\u0001J4\u0010\u00e8\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00e9\u00050\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~\u00a2\u0006\u0003\u0010\u00c9\u0001J4\u0010\u00ea\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00eb\u00050\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~\u00a2\u0006\u0003\u0010\u00c9\u0001J4\u0010\u00ec\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00ed\u00050\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~\u00a2\u0006\u0003\u0010\u00c9\u0001J4\u0010\u00ee\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00ef\u00050\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~\u00a2\u0006\u0003\u0010\u00c9\u0001J4\u0010\u00f0\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00f1\u00050\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~\u00a2\u0006\u0003\u0010\u00c9\u0001J4\u0010\u00f2\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00f3\u00050\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~\u00a2\u0006\u0003\u0010\u00c9\u0001J4\u0010\u00f4\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00f5\u00050\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~\u00a2\u0006\u0003\u0010\u00c9\u0001J4\u0010\u00f6\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00f7\u00050\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~\u00a2\u0006\u0003\u0010\u00c9\u0001J4\u0010\u00f8\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00f9\u00050\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~\u00a2\u0006\u0003\u0010\u00c9\u0001J4\u0010\u00fa\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00fb\u00050\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~\u00a2\u0006\u0003\u0010\u00c9\u0001J4\u0010\u00fc\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00fd\u00050\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~\u00a2\u0006\u0003\u0010\u00c9\u0001J4\u0010\u00fe\u0005\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00ff\u00050\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~\u00a2\u0006\u0003\u0010\u00c9\u0001J4\u0010\u0080\u0006\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u0081\u00060\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~\u00a2\u0006\u0003\u0010\u00c9\u0001J4\u0010\u0082\u0006\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u0083\u00060\u00070=2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~\u00a2\u0006\u0003\u0010\u00c9\u0001J\u001c\u0010\u0084\u0006\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020#0\u00070=2\u0007\u0010\u00af\u0001\u001a\u00020)JH\u0010\u0085\u0006\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u0086\u00060\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b6\u0004\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JH\u0010\u0087\u0006\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u0088\u00060\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c3\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JH\u0010\u0089\u0006\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u008a\u00060\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b6\u0004\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JH\u0010\u008b\u0006\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u008c\u00060\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c3\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~J\u0013\u0010\u008d\u0006\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020%0\u00070=JH\u0010\u008e\u0006\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u008f\u00060\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b6\u0004\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JH\u0010\u0090\u0006\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u0091\u00060\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c3\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JH\u0010\u0092\u0006\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u0093\u00060\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b6\u0004\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JH\u0010\u0094\u0006\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u0095\u00060\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c3\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JH\u0010\u0096\u0006\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u0097\u00060\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b6\u0004\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JH\u0010\u0098\u0006\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u0099\u00060\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c3\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~J\u001e\u0010\u009a\u0006\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u009b\u00060\u00070=2\b\u0010\u00ae\u0002\u001a\u00030\u00b8\u0001J\u0014\u0010\u009c\u0006\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u009d\u00060\u00070=J\u001c\u0010\u009e\u0006\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020+0\u00070=2\u0007\u0010\u00af\u0003\u001a\u00020)J*\u0010\u009e\u0006\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u009f\u00060\u00070=2\t\u0010\u0080\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00a0\u0006\u001a\u0004\u0018\u00010~JH\u0010\u00a1\u0006\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00a2\u00060\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b6\u0004\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JH\u0010\u00a3\u0006\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00a4\u00060\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c3\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JH\u0010\u00a5\u0006\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00a6\u00060\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b6\u0004\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JH\u0010\u00a7\u0006\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00a8\u00060\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c3\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~J\u001c\u0010\u00a9\u0006\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000b0\u00070=2\u0007\u0010\u00ae\u0002\u001a\u00020)J\u001c\u0010\u00aa\u0006\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000b0\u00070=2\u0007\u0010\u00ae\u0002\u001a\u00020)J*\u0010\u00ab\u0006\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00ac\u00060\u00070=2\t\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00c3\u0001\u001a\u0004\u0018\u00010~JH\u0010\u00ad\u0006\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00ae\u00060\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b6\u0004\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JH\u0010\u00af\u0006\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00b0\u00060\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c3\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~J\u001e\u0010\u00b1\u0006\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00180\u00070=2\t\u0010\u0080\u0001\u001a\u0004\u0018\u00010~J\u001e\u0010\u00b2\u0006\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00180\u00070=2\t\u0010\u0080\u0001\u001a\u0004\u0018\u00010~J\u001d\u0010\u00b3\u0006\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00b4\u00060\u00070=2\u0007\u0010\u00af\u0003\u001a\u00020)J5\u0010\u00b5\u0006\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00b6\u00060\u00070=2\t\u0010\u00af\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00d8\u0002\u001a\u0004\u0018\u00010~2\t\u0010\u00b7\u0006\u001a\u0004\u0018\u00010~J\u001d\u0010\u00b8\u0006\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00b9\u00060\u00070=2\u0007\u0010\u00fd\u0001\u001a\u00020)JG\u0010\u00ba\u0006\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020/0\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b6\u0004\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JH\u0010\u00bb\u0006\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00bc\u00060\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c3\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JH\u0010\u00bd\u0006\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00be\u00060\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b6\u0004\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JH\u0010\u00bf\u0006\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00c0\u00060\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c3\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JH\u0010\u00c1\u0006\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00c2\u00060\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b6\u0004\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JH\u0010\u00c3\u0006\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00c4\u00060\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c3\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JH\u0010\u00c5\u0006\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00c6\u00060\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b6\u0004\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~JH\u0010\u00c7\u0006\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00c8\u00060\u00070=2\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c3\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~J\u001c\u0010\u00c9\u0006\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020;0\u00070=2\u0007\u0010\u00af\u0001\u001a\u00020)J6\u0010\u00ca\u0006\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00cb\u00060\u00070=2\u0015\u0010\u00cc\u0006\u001a\u0010\u0012\u0004\u0012\u00020~\u0012\u0005\u0012\u00030\u00b8\u00010\u00b7\u00012\t\u0010\u00cd\u0006\u001a\u0004\u0018\u00010~J\u001d\u0010\u00ce\u0006\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00cf\u00060\u00070=2\u0007\u0010\u00cc\u0001\u001a\u00020~J;\u0010\u00d0\u0006\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00d1\u00060\u00070=2\t\u0010\u00d6\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00d2\u0006\u001a\u0004\u0018\u00010C2\t\u0010\u00cd\u0001\u001a\u0004\u0018\u00010~\u00a2\u0006\u0003\u0010\u00d3\u0006J;\u0010\u00d4\u0006\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00d5\u00060\u00070=2\t\u0010\u00d6\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00d6\u0006\u001a\u0004\u0018\u00010~2\t\u0010\u00d2\u0006\u001a\u0004\u0018\u00010C\u00a2\u0006\u0003\u0010\u00d7\u0006J\u001c\u0010\u00d8\u0006\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070=2\u0007\u0010\u00ae\u0002\u001a\u00020)J\u0014\u0010\u00d9\u0006\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00da\u00060\u00070=J\u001f\u0010\u00db\u0006\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00dc\u00060\u00070=2\t\u0010\u00d6\u0001\u001a\u0004\u0018\u00010~J\u00d6\u0001\u0010\u00dd\u0006\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00de\u00060\u00070=2\t\u0010\u00af\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00b0\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00b2\u0001\u001a\u0004\u0018\u00010~2\u0015\u0010\u00df\u0006\u001a\u0010\u0012\u0004\u0012\u00020~\u0012\u0005\u0012\u00030\u00b8\u00010\u00b7\u00012\u0015\u0010\u00e0\u0006\u001a\u0010\u0012\u0004\u0012\u00020~\u0012\u0005\u0012\u00030\u00b8\u00010\u00b7\u00012\t\u0010\u00b1\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00bd\u0001\u001a\u0004\u0018\u00010~2\n\u0010\u00d5\u0003\u001a\u0005\u0018\u00010\u00bb\u00012\n\u0010\u00d3\u0003\u001a\u0005\u0018\u00010\u00bb\u00012\t\u0010\u00c3\u0001\u001a\u0004\u0018\u00010~2\n\u0010\u00d4\u0003\u001a\u0005\u0018\u00010\u00bb\u00012\t\u0010\u00e1\u0006\u001a\u0004\u0018\u00010~2\n\u0010\u00bf\u0001\u001a\u0005\u0018\u00010\u00bb\u00012\u0015\u0010\u0082\u0001\u001a\u0010\u0012\u0004\u0012\u00020~\u0012\u0005\u0012\u00030\u00b8\u00010\u00b7\u0001JF\u0010\u00e2\u0006\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00e3\u00060\u00070=2\t\u0010\u00cc\u0002\u001a\u0004\u0018\u00010~2\t\u0010\u00c9\u0003\u001a\u0004\u0018\u00010~2\t\u0010\u00d6\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00d2\u0006\u001a\u0004\u0018\u00010C\u00a2\u0006\u0003\u0010\u00e4\u0006JF\u0010\u00e5\u0006\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00e6\u00060\u00070=2\t\u0010\u00cc\u0002\u001a\u0004\u0018\u00010~2\t\u0010\u00c9\u0003\u001a\u0004\u0018\u00010~2\t\u0010\u00d6\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00d2\u0006\u001a\u0004\u0018\u00010C\u00a2\u0006\u0003\u0010\u00e4\u0006J1\u0010\u00e7\u0006\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00e8\u00060\u00070=2\u0007\u0010\u00c0\u0002\u001a\u00020)2\u0007\u0010\u00c1\u0002\u001a\u00020)2\t\u0010\u0083\u0001\u001a\u0004\u0018\u00010~J\u0014\u0010\u00e9\u0006\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00ea\u00060\u00070=J+\u0010\u00eb\u0006\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00ec\u00060\u00070=2\u0015\u0010\u00b4\u0004\u001a\u0010\u0012\u0004\u0012\u00020~\u0012\u0005\u0012\u00030\u00b8\u00010\u00b7\u0001J\u0014\u0010\u00ed\u0006\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00ee\u00060\u00070=J5\u0010\u00ef\u0006\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00f0\u00060\u00070=2\t\u0010\u00d2\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00d3\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00cb\u0003\u001a\u0004\u0018\u00010~J\b\u0010\u00f1\u0006\u001a\u00030\u00fa\u0001J}\u0010\u00f2\u0006\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00f3\u00060\u00070=2\t\u0010\u00cc\u0002\u001a\u0004\u0018\u00010~2\t\u0010\u00d6\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00ca\u0003\u001a\u0004\u0018\u00010C2\t\u0010\u00d2\u0006\u001a\u0004\u0018\u00010C2\t\u0010\u00c9\u0003\u001a\u0004\u0018\u00010~2\t\u0010\u00d2\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00d3\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00cb\u0003\u001a\u0004\u0018\u00010~2\t\u0010\u00cd\u0002\u001a\u0004\u0018\u00010~\u00a2\u0006\u0003\u0010\u00f4\u0006J\u0013\u0010\u00f5\u0006\u001a\u00030\u00fa\u00012\u0007\u0010\u00f6\u0006\u001a\u00020\u001dH\u0002J5\u0010\u00f7\u0006\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00f8\u00060\u00070=2\t\u0010\u00c1\u0003\u001a\u0004\u0018\u00010~2\t\u0010\u00cd\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00c9\u0003\u001a\u0004\u0018\u00010~J*\u0010\u00f9\u0006\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00fa\u00060\u00070=2\t\u0010\u00cd\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00c9\u0003\u001a\u0004\u0018\u00010~JB\u0010\u00fb\u0006\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00fc\u00060\u00070=2\n\u0010\u00c3\u0001\u001a\u0005\u0018\u00010\u00bb\u00012\n\u0010\u00d9\u0001\u001a\u0005\u0018\u00010\u00bb\u00012\t\u0010\u00fd\u0006\u001a\u0004\u0018\u00010~2\t\u0010\u00fe\u0006\u001a\u0004\u0018\u00010~J&\u0010\u00ff\u0006\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u0080\u00070\u00070=2\u0007\u0010\u00c0\u0002\u001a\u00020)2\u0007\u0010\u00c1\u0002\u001a\u00020)J\u001f\u0010\u0081\u0007\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u0082\u00070\u00070=2\t\u0010\u0083\u0007\u001a\u0004\u0018\u00010~JE\u0010\u0084\u0007\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070=2\t\u0010\u0085\u0007\u001a\u0004\u0018\u00010~2\t\u0010\u0086\u0003\u001a\u0004\u0018\u00010~2\t\u0010\u00cc\u0002\u001a\u0004\u0018\u00010~2\t\u0010\u0086\u0007\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u0087\u0007J\u001e\u0010\u0088\u0007\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u0089\u00070\u00070=2\b\u0010\u00a3\u0001\u001a\u00030\u008a\u0007J\u001d\u0010\u0088\u0007\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u0089\u00070\u00070=2\u0007\u0010\u00a3\u0001\u001a\u00020~J1\u0010\u008b\u0007\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u008c\u00070\u00070=2\u0007\u0010\u00f6\u0004\u001a\u00020~2\u0007\u0010\u00af\u0003\u001a\u00020)2\t\u0010\u0099\u0002\u001a\u0004\u0018\u00010~J\u0011\u0010\u008d\u0007\u001a\u00030\u00fa\u00012\u0007\u0010\u00bb\u0004\u001a\u00020)J<\u0010\u008e\u0007\u001a\u00030\u00fa\u00012\u000b\b\u0002\u0010\u00c2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b6\u0004\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c4\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b7\u0004\u001a\u0004\u0018\u00010~J\u0011\u0010\u008f\u0007\u001a\u00030\u00fa\u00012\u0007\u0010\u00c8\u0001\u001a\u00020)J\u0011\u0010\u0090\u0007\u001a\u00030\u00fa\u00012\u0007\u0010\u0091\u0007\u001a\u00020)J\u0011\u0010\u0092\u0007\u001a\u00030\u00fa\u00012\u0007\u0010\u0091\u0007\u001a\u00020)J\u001d\u0010\u0093\u0007\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070=2\b\u0010\u00a3\u0001\u001a\u00030\u0094\u0007J(\u0010\u0095\u0007\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u0096\u00070\u00070=2\u0007\u0010\u00ae\u0002\u001a\u00020)2\t\u0010\u00b1\u0001\u001a\u0004\u0018\u00010~J\u001d\u0010\u0097\u0007\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u0098\u00070\u00070=2\u0007\u0010\u00ae\u0002\u001a\u00020)J\u001d\u0010\u0099\u0007\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u009a\u00070\u00070=2\u0007\u0010\u00ae\u0002\u001a\u00020)J*\u0010\u009b\u0007\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u009c\u00070\u00070=2\t\u0010\u00cd\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00c1\u0003\u001a\u0004\u0018\u00010~J\u001f\u0010\u009d\u0007\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u009e\u00070\u00070=2\t\u0010\u00af\u0001\u001a\u0004\u0018\u00010~J\u001f\u0010\u009f\u0007\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00a0\u00070\u00070=2\t\u0010\u00af\u0001\u001a\u0004\u0018\u00010~J@\u0010\u00a1\u0007\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00a2\u00070\u00070=2\t\u0010\u00af\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u0080\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00cd\u0002\u001a\u0004\u0018\u00010~2\t\u0010\u00c1\u0003\u001a\u0004\u0018\u00010~J5\u0010\u00a3\u0007\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00a4\u00070\u00070=2\t\u0010\u00cc\u0002\u001a\u0004\u0018\u00010~2\t\u0010\u00c9\u0003\u001a\u0004\u0018\u00010~2\t\u0010\u00d6\u0001\u001a\u0004\u0018\u00010~J5\u0010\u00a5\u0007\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00a6\u00070\u00070=2\t\u0010\u00cc\u0002\u001a\u0004\u0018\u00010~2\t\u0010\u00c9\u0003\u001a\u0004\u0018\u00010~2\t\u0010\u00d6\u0001\u001a\u0004\u0018\u00010~J*\u0010\u00a7\u0007\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00a8\u00070\u00070=2\t\u0010\u00c1\u0003\u001a\u0004\u0018\u00010~2\t\u0010\u00a9\u0007\u001a\u0004\u0018\u00010~J[\u0010\u00aa\u0007\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00ab\u00070\u00070=2\u000b\b\u0002\u0010\u00af\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u0099\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u008f\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c7\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00ac\u0007J\u008f\u0001\u0010\u00ad\u0007\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00ae\u00070\u00070=2\u000b\b\u0002\u0010\u00af\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00b5\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u009d\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u009e\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u009f\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00d6\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00a0\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00a1\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00af\u0007JN\u0010\u00b0\u0007\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00b1\u00070\u00070=2\u000b\b\u0002\u0010\u00a5\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00a6\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u009f\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u0087\u0007J*\u0010\u00b2\u0007\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00b3\u00070\u00070=2\t\u0010\u0083\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00a9\u0002\u001a\u0004\u0018\u00010~JK\u0010\u00b4\u0007\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00b5\u00070\u00070=2\t\u0010\u00c3\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00a5\u0002\u001a\u0004\u0018\u00010~2\t\u0010\u0080\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00a6\u0002\u001a\u0004\u0018\u00010~2\t\u0010\u00c8\u0001\u001a\u0004\u0018\u00010~JA\u0010\u00b6\u0007\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00b7\u00070\u00070=2\u000b\b\u0002\u0010\u009f\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b1\u0002\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00a8\u0004JN\u0010\u00b8\u0007\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00b9\u00070\u00070=2\u000b\b\u0002\u0010\u00b5\u0002\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00b6\u0002\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00b7\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00d4\u0002J[\u0010\u00ba\u0007\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00bb\u00070\u00070=2\u000b\b\u0002\u0010\u00c3\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00bb\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00bc\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00bd\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00cf\u0002J\u001d\u0010\u00bc\u0007\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070=2\b\u0010\u00a3\u0001\u001a\u00030\u00bd\u0007J\u001d\u0010\u00be\u0007\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070=2\b\u0010\u00a3\u0001\u001a\u00030\u00bd\u0007JJ\u0010\u00bf\u0007\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00c0\u00070\u00070=2\t\u0010\u00c0\u0002\u001a\u0004\u0018\u00010~2\t\u0010\u00c1\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00af\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00f7\u0002J\u0082\u0001\u0010\u00c1\u0007\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00c2\u00070\u00070=2\u000b\b\u0002\u0010\u00c5\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u0083\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c6\u0002\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00c7\u0002\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00a7\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00c8\u0002\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00c9\u0002\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00c3\u0007Jg\u0010\u00c4\u0007\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070=2\u000b\b\u0002\u0010\u0083\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00cc\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00cd\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00ce\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00af\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00c5\u0007Jd\u0010\u00c6\u0007\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00c7\u00070\u00070=2\u000b\b\u0002\u0010\u0083\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00a6\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00a7\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00af\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00a8\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00c8\u0007JZ\u0010\u00c9\u0007\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u0002030\u00070=2\u000b\b\u0002\u0010\u00af\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00a6\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u0083\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00a8\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00d2\u0002JZ\u0010\u00ca\u0007\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u0002030\u00070=2\u000b\b\u0002\u0010\u00af\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00a6\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u0083\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00a8\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00d2\u0002Jd\u0010\u00cb\u0007\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00cc\u00070\u00070=2\u000b\b\u0002\u0010\u0083\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00a6\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00a7\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00af\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00a8\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00c8\u0007J]\u0010\u00cd\u0007\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00ce\u00070\u00070=2\t\u0010\u00cf\u0007\u001a\u0004\u0018\u00010~2\u0007\u0010\u00ae\u0002\u001a\u00020)2\t\u0010\u00d0\u0007\u001a\u0004\u0018\u00010~2\u0007\u0010\u00b1\u0001\u001a\u00020)2\t\u0010\u009f\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u008f\u0003\u001a\u0004\u0018\u00010~2\t\u0010\u00d8\u0002\u001a\u0004\u0018\u00010~J~\u0010\u00cd\u0007\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00ce\u00070\u00070=2\u000b\b\u0002\u0010\u00d1\u0007\u001a\u0004\u0018\u00010~2\t\u0010\u00d8\u0002\u001a\u0004\u0018\u00010~2\t\u0010\u008f\u0003\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u009f\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b1\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00ae\u0002\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00d0\u0007\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00d2\u0007JZ\u0010\u00d3\u0007\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00d4\u00070\u00070=2\n\b\u0002\u0010}\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00dd\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00de\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00df\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00cf\u0002JN\u0010\u00d5\u0007\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00d6\u00070\u00070=2\u000b\b\u0002\u0010\u00af\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00c0\u0002\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00c1\u0002\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00d7\u0007Ju\u0010\u00d8\u0007\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00d9\u00070\u00070=2\u000b\b\u0002\u0010\u00e5\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u0083\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00cc\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00ce\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00e6\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00e7\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00da\u0007J\u009c\u0001\u0010\u00db\u0007\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00dc\u00070\u00070=2\u000b\b\u0002\u0010\u00ea\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00eb\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00ec\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00ed\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00ee\u0002\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00ef\u0002\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00f0\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00f1\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b1\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00dd\u0007J[\u0010\u00de\u0007\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00df\u00070\u00070=2\u000b\b\u0002\u0010\u0083\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00f5\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00d8\u0002\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00f6\u0002\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00e0\u0007J&\u0010\u00e1\u0007\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070=2\u0007\u0010\u00e2\u0007\u001a\u00020)2\b\u0010\u00a3\u0001\u001a\u00030\u00e3\u0007Jo\u0010\u00e4\u0007\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00e5\u00070\u00070=2\u000b\b\u0002\u0010\u00a9\u0002\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u0083\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00a6\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00a7\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b1\u0002\u001a\u0004\u0018\u00010)2\t\u0010\u0081\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00e6\u0007Jf\u0010\u00e7\u0007\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00e8\u00070\u00070=2\u000b\b\u0002\u0010\u00a8\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00ae\u0002\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u009f\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00fd\u0002\u001a\u0004\u0018\u00010~2\t\u0010\u00fe\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00e9\u0007J_\u0010\u00ea\u0007\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070=2\t\b\u0002\u0010\u00a6\u0001\u001a\u00020)2\u000b\b\u0002\u0010\u0083\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00a8\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)2\u0007\u0010\u00a7\u0001\u001a\u00020)2\t\u0010\u0087\u0003\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00eb\u0007Jh\u0010\u00ea\u0007\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00ec\u00070\u00070=2\u000b\b\u0002\u0010\u0083\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c7\u0002\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00c9\u0002\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00a7\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00c8\u0002\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00ed\u0007Ja\u0010\u00ee\u0007\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070=2\t\b\u0002\u0010\u00a6\u0001\u001a\u00020)2\u000b\b\u0002\u0010\u0083\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00a8\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)2\u0007\u0010\u00a7\u0001\u001a\u00020)2\u000b\b\u0002\u0010\u00bc\u0003\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00eb\u0007JN\u0010\u00ef\u0007\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00f0\u00070\u00070=2\u000b\b\u0002\u0010\u0083\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c6\u0002\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00ae\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00f1\u0007Jw\u0010\u00ef\u0007\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070=2\u000b\b\u0002\u0010\u0083\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00cc\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00af\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u0086\u0003\u001a\u0004\u0018\u00010~2\u0007\u0010\u00a6\u0001\u001a\u00020)2\t\b\u0002\u0010\u00a8\u0001\u001a\u00020)2\u0007\u0010\u0087\u0003\u001a\u00020)2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00f2\u0007JE\u0010\u00f3\u0007\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070=2\t\u0010\u008e\u0003\u001a\u0004\u0018\u00010)2\t\u0010\u009f\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u008f\u0003\u001a\u0004\u0018\u00010~2\t\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u009a\u0002JA\u0010\u00f4\u0007\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00f5\u00070\u00070=2\u000b\b\u0002\u0010\u00d6\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u0093\u0003\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00c2\u0002Ju\u0010\u00f6\u0007\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00f7\u00070\u00070=2\u000b\b\u0002\u0010\u00b5\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u0096\u0003\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00af\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u0081\u0002\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u0092\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u0080\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00ca\u0002J[\u0010\u00f8\u0007\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00f9\u00070\u00070=2\u000b\b\u0002\u0010\u00b1\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u0080\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u0082\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u009a\u0003\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00da\u0002JE\u0010\u00fa\u0007\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070=2\t\u0010\u008e\u0003\u001a\u0004\u0018\u00010)2\t\u0010\u009f\u0001\u001a\u0004\u0018\u00010~2\t\u0010\u00d8\u0002\u001a\u0004\u0018\u00010~2\t\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u009a\u0002J[\u0010\u00fb\u0007\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00fc\u00070\u00070=2\u000b\b\u0002\u0010\u00af\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u009f\u0003\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00a0\u0003\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00a1\u0003\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00da\u0002Jq\u0010\u00fd\u0007\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00fe\u00070\u00070=2\u000b\b\u0002\u0010\u00a4\u0003\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00a5\u0003\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00af\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00b1\u0001\u001a\u0004\u0018\u00010)2\t\u0010\u00c9\u0002\u001a\u0004\u0018\u00010~2\t\u0010\u00a6\u0003\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00ff\u0007Ju\u0010\u0080\b\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u0081\b0\u00070=2\u000b\b\u0002\u0010\u00af\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00aa\u0003\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00ab\u0003\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00b6\u0002\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00ac\u0003\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00ad\u0003\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00ff\u0007JA\u0010\u0082\b\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u0083\b0\u00070=2\u000b\b\u0002\u0010\u00b4\u0003\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00b5\u0003\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00c2\u0002Jh\u0010\u0084\b\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u0085\b0\u00070=2\u000b\b\u0002\u0010\u0083\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c7\u0002\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00c9\u0002\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00a7\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00c8\u0002\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00ed\u0007J_\u0010\u0086\b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070=2\t\b\u0002\u0010\u00a6\u0001\u001a\u00020)2\u000b\b\u0002\u0010\u0083\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00a8\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)2\u0007\u0010\u00a7\u0001\u001a\u00020)2\t\u0010\u0087\u0003\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00eb\u0007Ja\u0010\u0087\b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070=2\t\b\u0002\u0010\u00a6\u0001\u001a\u00020)2\u000b\b\u0002\u0010\u0083\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00a8\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)2\u0007\u0010\u00a7\u0001\u001a\u00020)2\u000b\b\u0002\u0010\u00bc\u0003\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00eb\u0007Jg\u0010\u0088\b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u0002080\u00070=2\u000b\b\u0002\u0010\u00ae\u0002\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00af\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u0083\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00a6\u0003\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00bc\u0003\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u0089\bJg\u0010\u008a\b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u0002080\u00070=2\u000b\b\u0002\u0010\u00ae\u0002\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00af\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u0083\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00a6\u0003\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00bc\u0003\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u0089\bJu\u0010\u008b\b\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u008c\b0\u00070=2\u000b\b\u0002\u0010\u00c1\u0003\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u0080\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u0082\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00af\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00b1\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00c2\u0003\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u008d\bJ4\u0010\u008e\b\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u008f\b0\u00070=2\u000b\b\u0002\u0010\u0083\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u00b2\u0002J\u00f7\u0001\u0010\u0090\b\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u0091\b0\u00070=2\u000b\b\u0002\u0010\u00c8\u0003\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00d6\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00d2\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00d3\u0001\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00cc\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00c9\u0003\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u0080\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00ca\u0003\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00cd\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00e5\u0002\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00cb\u0003\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00cc\u0003\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00cd\u0003\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00ce\u0003\u001a\u0004\u0018\u00010~2\u000b\b\u0002\u0010\u00cf\u0003\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00b1\u0001\u001a\u0004\u0018\u00010)2\u000b\b\u0002\u0010\u00c8\u0001\u001a\u0004\u0018\u00010)\u00a2\u0006\u0003\u0010\u0092\bJ\u001e\u0010\u0093\b\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u0094\b0\u00070=2\b\u0010\u0095\b\u001a\u00030\u0096\bJ\u001e\u0010\u0097\b\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u0098\b0\u00070=2\b\u0010\u0095\b\u001a\u00030\u0096\bJ\u001f\u0010\u0099\b\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u009a\b0\u00070=2\t\u0010\u00a0\u0005\u001a\u0004\u0018\u00010~R\u001a\u0010\u0005\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\t\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\n\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u000b0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\f\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u000e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u000f\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00100\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0011\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00120\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0013\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0014\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0015\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00160\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0017\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00180\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0019\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00180\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u001a\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00180\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u001b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001d0\u001c0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u001e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001f0\u001c0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010 \u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020!0\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\"\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020#0\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010$\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020%0\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010&\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000b0\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\'\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000b0\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010(\u001a\b\u0012\u0004\u0012\u00020)0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010*\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020+0\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010,\u001a\b\u0012\u0004\u0012\u00020-0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010.\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020/0\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u00100\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u0002010\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u00102\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u0002030\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u00104\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u00105\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u00106\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u00107\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u0002080\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u00109\u001a\b\u0012\u0004\u0012\u00020)0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010:\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020;0\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010<\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070=\u00a2\u0006\b\n\u0000\u001a\u0004\b>\u0010?R\u001d\u0010@\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070=\u00a2\u0006\b\n\u0000\u001a\u0004\bA\u0010?R\u000e\u0010B\u001a\u00020CX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0016\u0010D\u001a\n\u0012\u0004\u0012\u00020\u000b\u0018\u00010\u0007X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0019\u0010E\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u000b0=\u00a2\u0006\b\n\u0000\u001a\u0004\bF\u0010?R\u001d\u0010G\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\u00070=\u00a2\u0006\b\n\u0000\u001a\u0004\bH\u0010?R\u001d\u0010I\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070=\u00a2\u0006\b\n\u0000\u001a\u0004\bJ\u0010?R\u001d\u0010K\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00100\u00070=\u00a2\u0006\b\n\u0000\u001a\u0004\bL\u0010?R\u001d\u0010M\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00120\u00070=\u00a2\u0006\b\n\u0000\u001a\u0004\bN\u0010?R\u001d\u0010O\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070=\u00a2\u0006\b\n\u0000\u001a\u0004\bP\u0010?R\u001d\u0010Q\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070=\u00a2\u0006\b\n\u0000\u001a\u0004\bR\u0010?R\u001d\u0010S\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00160\u00070=\u00a2\u0006\b\n\u0000\u001a\u0004\bT\u0010?R\u001d\u0010U\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00180\u00070=\u00a2\u0006\b\n\u0000\u001a\u0004\bV\u0010?R\u001d\u0010W\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00180\u00070=\u00a2\u0006\b\n\u0000\u001a\u0004\bX\u0010?R\u001d\u0010Y\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00180\u00070=\u00a2\u0006\b\n\u0000\u001a\u0004\bZ\u0010?R\u001d\u0010[\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001d0\u001c0=\u00a2\u0006\b\n\u0000\u001a\u0004\b\\\u0010?R\u001d\u0010]\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001f0\u001c0=\u00a2\u0006\b\n\u0000\u001a\u0004\b^\u0010?R\u001d\u0010_\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020!0\u00070=\u00a2\u0006\b\n\u0000\u001a\u0004\b`\u0010?R\u001d\u0010a\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020#0\u00070=\u00a2\u0006\b\n\u0000\u001a\u0004\bb\u0010?R\u001d\u0010c\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020%0\u00070=\u00a2\u0006\b\n\u0000\u001a\u0004\bd\u0010?R\u001d\u0010e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000b0\u00070=\u00a2\u0006\b\n\u0000\u001a\u0004\bf\u0010?R\u001d\u0010g\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000b0\u00070=\u00a2\u0006\b\n\u0000\u001a\u0004\bh\u0010?R\u001d\u0010i\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020+0\u00070=\u00a2\u0006\b\n\u0000\u001a\u0004\bj\u0010?R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010k\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020/0\u00070=\u00a2\u0006\b\n\u0000\u001a\u0004\bl\u0010?R\u001d\u0010m\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u0002010\u00070=\u00a2\u0006\b\n\u0000\u001a\u0004\bn\u0010?R\u001d\u0010o\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u0002030\u00070=\u00a2\u0006\b\n\u0000\u001a\u0004\bp\u0010?R\u001d\u0010q\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070=\u00a2\u0006\b\n\u0000\u001a\u0004\br\u0010?R\u001d\u0010s\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070=\u00a2\u0006\b\n\u0000\u001a\u0004\bt\u0010?R\u001d\u0010u\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070=\u00a2\u0006\b\n\u0000\u001a\u0004\bv\u0010?R\u001d\u0010w\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u0002080\u00070=\u00a2\u0006\b\n\u0000\u001a\u0004\bx\u0010?R\u001d\u0010y\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020;0\u00070=\u00a2\u0006\b\n\u0000\u001a\u0004\bz\u0010?\u00a8\u0006\u009c\b"}, d2 = {"Lcom/manaknight/app/viewmodels/BaasViewModel;", "Landroidx/lifecycle/ViewModel;", "repository", "Lcom/manaknight/app/repositories/APIRepository;", "(Lcom/manaknight/app/repositories/APIRepository;)V", "_addLinealFootCostResponse", "Landroidx/lifecycle/MutableLiveData;", "Lcom/manaknight/app/network/Resource;", "Lcom/manaknight/app/model/remote/profitPro/CommonResponse;", "_addSquareFootCostResponse", "_cachedProjectDetailsResource", "Lcom/manaknight/app/model/remote/profitPro/AllLineItemsResponseModel;", "_cancelSubscriptionResponse", "Lcom/manaknight/app/model/remote/CancelSubscriptionResponse;", "_createMaterialResponse", "_createTeamMemberResponse", "Lcom/manaknight/app/model/remote/CreateTeamMemberResponse;", "_deleteDefaultMaterialResponse", "Lcom/manaknight/app/model/remote/DeleteDefaultMaterialResponse;", "_deleteLinealFootCostResponse", "_deleteSquareFootCostResponse", "_deleteTeamMemberResponse", "Lcom/manaknight/app/model/remote/DeleteTeamMemberResponse;", "_getLinealFootCostsResponse", "Lcom/manaknight/app/model/remote/profitPro/LinearResponseModel;", "_getSquareFootCostsResponse", "_linealCostsResource", "_linearItems", "", "Lcom/manaknight/app/viewmodels/LinearItemDisplay;", "_materialItems", "Lcom/manaknight/app/viewmodels/MaterialDisplay;", "_materialListResource", "Lcom/manaknight/app/model/remote/profitPro/MaterialResponseModel;", "_paymentHistoryResource", "Lcom/manaknight/app/model/remote/GetPaymentHistoryResponse;", "_plansResource", "Lcom/manaknight/app/model/remote/GetPlansResponse;", "_projectDetails", "_projectDetailsResource", "_projectId", "", "_projectsResource", "Lcom/manaknight/app/model/remote/ProjectResponseModel;", "_teamMemberFilters", "Lcom/manaknight/app/viewmodels/BaasViewModel$TeamMemberFilters;", "_teamMembersResource", "Lcom/manaknight/app/model/remote/GetTeamMemberListResponse;", "_trackingResponse", "Lcom/manaknight/app/model/remote/profitPro/ProjectTrackingResponse;", "_updateDefaultMaterialResponse", "Lcom/manaknight/app/model/remote/UpdateDefaultMaterialResponse;", "_updateDefaultResponse", "_updateLinealFootCostResponse", "_updateSquareFootCostResponse", "_updateTeamMemberResponse", "Lcom/manaknight/app/model/remote/UpdateTeamMemberResponse;", "_userId", "_userSubscriptionsResource", "Lcom/manaknight/app/model/remote/GetUserSubscriptionsResponse;", "addLinealFootCostResponse", "Landroidx/lifecycle/LiveData;", "getAddLinealFootCostResponse", "()Landroidx/lifecycle/LiveData;", "addSquareFootCostResponse", "getAddSquareFootCostResponse", "apiCallMade", "", "cachedProjectDetails", "cachedProjectDetailsResource", "getCachedProjectDetailsResource", "cancelSubscriptionResponse", "getCancelSubscriptionResponse", "createMaterialResponse", "getCreateMaterialResponse", "createTeamMemberResponse", "getCreateTeamMemberResponse", "deleteDefaultMaterialResponse", "getDeleteDefaultMaterialResponse", "deleteLinealFootCostResponse", "getDeleteLinealFootCostResponse", "deleteSquareFootCostResponse", "getDeleteSquareFootCostResponse", "deleteTeamMemberResponse", "getDeleteTeamMemberResponse", "getLinealFootCostsResponse", "getGetLinealFootCostsResponse", "getSquareFootCostsResponse", "getGetSquareFootCostsResponse", "linealCostsResource", "getLinealCostsResource", "linearItems", "getLinearItems", "materialItems", "getMaterialItems", "materialListResource", "getMaterialListResource", "paymentHistoryResource", "getPaymentHistoryResource", "plansResource", "getPlansResource", "projectDetails", "getProjectDetails", "projectDetailsResource", "getProjectDetailsResource", "projectsResource", "getProjectsResource", "teamMembersResource", "getTeamMembersResource", "trackingResponse", "getTrackingResponse", "updateDefaultMaterialResponse", "getUpdateDefaultMaterialResponse", "updateDefaultResponse", "getUpdateDefaultResponse", "updateLinealFootCostResponse", "getUpdateLinealFootCostResponse", "updateSquareFootCostResponse", "getUpdateSquareFootCostResponse", "updateTeamMemberResponse", "getUpdateTeamMemberResponse", "userSubscriptionsResource", "getUserSubscriptionsResource", "addEcomProductLambda", "Lcom/manaknight/app/model/remote/AddEcomProductLambdaResponse;", "slug", "", "categoryId", "type", "quantity", "data", "name", "isTaxable", "isShipping", "isSticky", "isFeatured", "isDownloadable", "downloadLimit", "isBackorder", "soldSingle", "manageStock", "thumbnailImage", "featuredImage", "image", "sku", "weight", "height", "length", "weightUnit", "heightUnit", "lengthUnit", "avgReview", "salePrice", "shippingPrice", "regularPrice", "position", "downloadExpireAt", "scheduleSaleAt", "scheduleSaleEnd", "description", "isVirtual", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Boolean;Ljava/lang/Boolean;Ljava/lang/Boolean;Ljava/lang/Boolean;Ljava/lang/Boolean;Ljava/lang/String;Ljava/lang/Boolean;Ljava/lang/Boolean;Ljava/lang/Boolean;Ljava/lang/String;Ljava/lang/Boolean;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Boolean;)Landroidx/lifecycle/LiveData;", "addLineItem", "request", "Lcom/manaknight/app/model/remote/profitPro/CreateLineItemReqModel;", "addLinealFootCost", "cost", "laborCost", "hidden", "(Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/Integer;)Landroidx/lifecycle/LiveData;", "addLinealFootCostNew", "addSquareFootCost", "addSquareFootCostNew", "analyticsLog", "Lcom/manaknight/app/model/remote/AnalyticsLogResponse;", "userId", "sessionId", "status", "userAgent", "application", "document", "url", "Ljava/util/ArrayList;", "", "", "Lkotlin/collections/ArrayList;", "linkClicks", "", "clickedButtons", "clientIp", "events", "totalTime", "appAlertsList", "Lcom/manaknight/app/model/remote/AppAlertsListResponse;", "order", "page", "filter", "appAlertsUpdate", "Lcom/manaknight/app/model/remote/AppAlertsUpdateResponse;", "isRead", "id", "(Ljava/lang/Integer;Ljava/lang/String;)Landroidx/lifecycle/LiveData;", "appleAuthCode", "Lcom/manaknight/app/model/remote/AppleAuthCodeResponse;", "state", "code", "appleLogin", "Lcom/manaknight/app/model/remote/AppleLoginResponse;", "appleLoginMobileEndpoint", "Lcom/manaknight/app/model/remote/AppleLoginMobileEndpointResponse;", "firstName", "lastName", "identitytoken", "appleId", "role", "blogAll", "Lcom/manaknight/app/model/remote/BlogAllResponse;", "limit", "offset", "blogCreate", "Lcom/manaknight/app/model/remote/BlogCreateResponse;", "title", "body", "meta", "tags", "categories", "content", "thumbnail", "blogDelete", "Lcom/manaknight/app/model/remote/BlogDeleteResponse;", "blogEdit", "Lcom/manaknight/app/model/remote/BlogEditResponse;", "blogFilter", "Lcom/manaknight/app/model/remote/BlogFilterResponse;", "rule", "search", "blogSimilar", "Lcom/manaknight/app/model/remote/BlogSimilarResponse;", "top", "blogSingle", "Lcom/manaknight/app/model/remote/BlogSingleResponse;", "blogTags", "Lcom/manaknight/app/model/remote/BlogTagsResponse;", "blogTagsDeleteByID", "Lcom/manaknight/app/model/remote/BlogTagsDeleteByIDResponse;", "blogTagsRetrieve", "Lcom/manaknight/app/model/remote/BlogTagsRetrieveResponse;", "blogTagsUpdate", "Lcom/manaknight/app/model/remote/BlogTagsUpdateResponse;", "cacheProjectDetailsResource", "", "resource", "cancelSubscription", "subscriptionId", "reason", "captchaGenerate", "Lcom/manaknight/app/model/remote/CaptchaGenerateResponse;", "width", "captchaTest", "Lcom/manaknight/app/model/remote/CaptchaTestResponse;", "clearAddLinealFootCostResponse", "clearAddSquareFootCostResponse", "clearCreateMaterialResponse", "clearCreateTeamMemberNewResponse", "clearDeleteDefaultMaterialResponse", "clearDeleteLinealFootCostResponse", "clearDeleteSquareFootCostResponse", "clearDeleteTeamMemberNewResponse", "clearGetLinealFootCostsResponse", "clearGetSquareFootCostsResponse", "clearUpdateCompanyDefaultNewResponse", "clearUpdateDefaultMaterialResponse", "clearUpdateLinealFootCostResponse", "clearUpdateSquareFootCostResponse", "clearUpdateTeamMemberResponse", "companyDetails", "Lcom/manaknight/app/model/remote/CompanyDetailsResponse;", "companyOverview", "Lcom/manaknight/app/model/remote/CompanyOverviewResponse;", "createAlerts", "Lcom/manaknight/app/model/remote/CreateAlertsResponse;", "message", "(Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;)Landroidx/lifecycle/LiveData;", "createAnalyticLog", "Lcom/manaknight/app/model/remote/CreateAnalyticLogResponse;", "path", "hostname", "ip", "browser", "country", "(Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Landroidx/lifecycle/LiveData;", "createApiKeys", "Lcom/manaknight/app/model/remote/CreateApiKeysResponse;", "key", "value", "createBlogCategory", "Lcom/manaknight/app/model/remote/CreateBlogCategoryResponse;", "parentId", "createCMSLambda", "Lcom/manaknight/app/model/remote/CreateCMSLambdaResponse;", "createChangeOrder", "Lcom/manaknight/app/model/remote/CreateChangeOrderResponse;", "projectId", "createChangeOrderDescription", "Lcom/manaknight/app/model/remote/CreateChangeOrderDescriptionResponse;", "lineItemId", "(Ljava/lang/String;Ljava/lang/Integer;)Landroidx/lifecycle/LiveData;", "createChat", "Lcom/manaknight/app/model/remote/CreateChatResponse;", "roomId", "unread", "chat", "(Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/String;)Landroidx/lifecycle/LiveData;", "createCms", "Lcom/manaknight/app/model/remote/CreateCmsResponse;", "contentKey", "contentType", "contentValue", "createCompanySettings", "Lcom/manaknight/app/model/remote/CreateCompanySettingsResponse;", "defaultHourlyRate", "defaultProfitOverhead", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;)Landroidx/lifecycle/LiveData;", "createCost", "Lcom/manaknight/app/model/remote/CreateCostResponse;", "costType", "unitCost", "linealFootCost", "materialCost", "profitOverhead", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/Integer;)Landroidx/lifecycle/LiveData;", "createCustomer", "email", "phone", "address", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;)Landroidx/lifecycle/LiveData;", "createDefaultLinealFootCost", "Lcom/manaknight/app/model/remote/CreateDefaultLinealFootCostResponse;", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/Integer;)Landroidx/lifecycle/LiveData;", "createDefaultMaterial", "(Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/Integer;)Landroidx/lifecycle/LiveData;", "createDefaultSquareFootCost", "Lcom/manaknight/app/model/remote/CreateDefaultSquareFootCostResponse;", "createDraw", "amount", "check_no", "(Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;)Landroidx/lifecycle/LiveData;", "createEmail", "Lcom/manaknight/app/model/remote/CreateEmailResponse;", "subject", "tag", "html", "createEmployee", "Lcom/manaknight/app/model/remote/CreateEmployeeResponse;", "(Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/Integer;)Landroidx/lifecycle/LiveData;", "createInvoice", "Lcom/manaknight/app/model/remote/CreateInvoiceResponse;", "companyName", "milestoneDescription", "totalAmountDue", "createJob", "Lcom/manaknight/app/model/remote/CreateJobResponse;", "task", "arguments", "errorLog", "identifier", "retries", "retryCount", "timeInterval", "lastRun", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Landroidx/lifecycle/LiveData;", "createLabor", "Lcom/manaknight/app/model/remote/CreateLaborResponse;", "hours", "perHour", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/Integer;)Landroidx/lifecycle/LiveData;", "createLineItemEntry", "Lcom/manaknight/app/model/remote/CreateLineItemEntryResponse;", "(Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/String;)Landroidx/lifecycle/LiveData;", "createLineItems", "Lcom/manaknight/app/model/remote/CreateLineItemsResponse;", "estimatedBy", "laborHours", "(Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Landroidx/lifecycle/LiveData;", "createLinealFootCost", "Lcom/manaknight/app/model/remote/CreateLinealFootCostResponse;", "(Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/Integer;)Landroidx/lifecycle/LiveData;", "createMaterial", "Lcom/manaknight/app/model/remote/CreateMaterialResponse;", "(Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/String;)Landroidx/lifecycle/LiveData;", "session_name", "is_default", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/String;III)Landroidx/lifecycle/LiveData;", "createMaterialNew", "createNewEstimation", "customerID", "(Ljava/lang/Integer;)Landroidx/lifecycle/LiveData;", "createPercentageDraw", "projectID", "percentage", "(Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;)Landroidx/lifecycle/LiveData;", "createPermission", "Lcom/manaknight/app/model/remote/CreatePermissionResponse;", "permission", "createPhoto", "Lcom/manaknight/app/model/remote/CreatePhotoResponse;", "caption", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/Integer;)Landroidx/lifecycle/LiveData;", "createPosts", "Lcom/manaknight/app/model/remote/CreatePostsResponse;", "links", "(Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Landroidx/lifecycle/LiveData;", "createPriceDraw", "createProfile", "Lcom/manaknight/app/model/remote/CreateProfileResponse;", "fcmToken", "deviceId", "deviceType", "createProject", "Lcom/manaknight/app/model/remote/CreateProjectResponse;", "changeCount", "customerId", "hourlyRate", "(Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;)Landroidx/lifecycle/LiveData;", "createRoom", "Lcom/manaknight/app/model/remote/CreateRoomResponse;", "otherUserId", "chatId", "userUpdateAt", "otherUserUpdateAt", "createRoomRequests", "user_id", "other_user_id", "(ILjava/lang/Integer;)Landroidx/lifecycle/LiveData;", "createSetting", "Lcom/manaknight/app/model/remote/CreateSettingResponse;", "settingKey", "settingValue", "createSqftCosts", "Lcom/manaknight/app/model/remote/CreateSqftCostsResponse;", "createSubscription", "Lcom/manaknight/app/model/remote/CreateSubscriptionResponse;", "cardCharged", "createTeamMember", "isDefault", "(Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/Integer;)Landroidx/lifecycle/LiveData;", "createTeamMemberNew", "createToken", "Lcom/manaknight/app/model/remote/CreateTokenResponse;", "token", "expireAt", "(Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/String;)Landroidx/lifecycle/LiveData;", "createTriggerType", "Lcom/manaknight/app/model/remote/CreateTriggerTypeResponse;", "createUser", "Lcom/manaknight/app/model/remote/CreateUserResponse;", "oauth", "password", "verify", "photo", "refer", "stripeUid", "paypalUid", "twoFactorAuthentication", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/Integer;)Landroidx/lifecycle/LiveData;", "createUserSessionsAnalytics", "Lcom/manaknight/app/model/remote/CreateUserSessionsAnalyticsResponse;", "screenWidth", "screenHeight", "screenSize", "startTime", "endTime", "htmlCopy", "deleteAlerts", "Lcom/manaknight/app/model/remote/DeleteAlertsResponse;", "deleteAnalyticLog", "Lcom/manaknight/app/model/remote/DeleteAnalyticLogResponse;", "deleteApiKeys", "Lcom/manaknight/app/model/remote/DeleteApiKeysResponse;", "deleteBlogCategory", "Lcom/manaknight/app/model/remote/DeleteBlogCategoryResponse;", "deleteCMSLambda", "Lcom/manaknight/app/model/remote/DeleteCMSLambdaResponse;", "deleteChangeOrderDescription", "Lcom/manaknight/app/model/remote/DeleteChangeOrderDescriptionResponse;", "deleteChat", "Lcom/manaknight/app/model/remote/DeleteChatResponse;", "deleteCms", "Lcom/manaknight/app/model/remote/DeleteCmsResponse;", "deleteCompanySettings", "Lcom/manaknight/app/model/remote/DeleteCompanySettingsResponse;", "deleteCost", "Lcom/manaknight/app/model/remote/DeleteCostResponse;", "deleteCustomer", "Lcom/manaknight/app/model/remote/DeleteCustomerResponse;", "deleteDefaultLinealFootCost", "Lcom/manaknight/app/model/remote/DeleteDefaultLinealFootCostResponse;", "deleteDefaultMaterial", "deleteDefaultMaterialNew", "deleteDefaultSquareFootCost", "Lcom/manaknight/app/model/remote/DeleteDefaultSquareFootCostResponse;", "deleteDraws", "deleteEcomProductLambda", "Lcom/manaknight/app/model/remote/DeleteEcomProductLambdaResponse;", "deleteEmail", "Lcom/manaknight/app/model/remote/DeleteEmailResponse;", "deleteEmployee", "Lcom/manaknight/app/model/remote/DeleteEmployeeResponse;", "deleteInvoice", "Lcom/manaknight/app/model/remote/DeleteInvoiceResponse;", "deleteJob", "Lcom/manaknight/app/model/remote/DeleteJobResponse;", "deleteLabor", "Lcom/manaknight/app/model/remote/DeleteLaborResponse;", "deleteLineItemEntry", "Lcom/manaknight/app/model/remote/DeleteLineItemEntryResponse;", "deleteLineItems", "deleteLinealFootCost", "Lcom/manaknight/app/model/remote/DeleteLinealFootCostResponse;", "deleteLinealFootCosts", "deleteLinealFootCostsNew", "deleteMaterial", "Lcom/manaknight/app/model/remote/DeleteMaterialResponse;", "deletePermission", "Lcom/manaknight/app/model/remote/DeletePermissionResponse;", "deletePhoto", "Lcom/manaknight/app/model/remote/DeletePhotoResponse;", "deletePosts", "Lcom/manaknight/app/model/remote/DeletePostsResponse;", "deleteProfile", "Lcom/manaknight/app/model/remote/DeleteProfileResponse;", "deleteProject", "Lcom/manaknight/app/model/remote/DeleteProjectResponse;", "deleteRoom", "Lcom/manaknight/app/model/remote/DeleteRoomResponse;", "deleteSetting", "Lcom/manaknight/app/model/remote/DeleteSettingResponse;", "deleteSqftCosts", "Lcom/manaknight/app/model/remote/DeleteSqftCostsResponse;", "deleteSquareFootCost", "deleteSquareFootCostNew", "deleteTeamMember", "deleteTeamMemberNew", "deleteToken", "Lcom/manaknight/app/model/remote/DeleteTokenResponse;", "deleteTriggerType", "Lcom/manaknight/app/model/remote/DeleteTriggerTypeResponse;", "deleteUser", "Lcom/manaknight/app/model/remote/DeleteUserResponse;", "ecomAddCart", "Lcom/manaknight/app/model/remote/EcomAddCartResponse;", "productid", "(Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/Integer;)Landroidx/lifecycle/LiveData;", "ecomAddProductReview", "Lcom/manaknight/app/model/remote/EcomAddProductReviewResponse;", "review", "ecomDeleteCartItem", "Lcom/manaknight/app/model/remote/EcomDeleteCartItemResponse;", "ecomGetProductReview", "Lcom/manaknight/app/model/remote/EcomGetProductReviewResponse;", "ecomProductByIDDefault", "Lcom/manaknight/app/model/remote/EcomProductByIDDefaultResponse;", "editEcomProductLambda", "Lcom/manaknight/app/model/remote/EditEcomProductLambdaResponse;", "payload", "fetchDefaultMaterialList", "size", "join", "fetchPaymentHistory", "fetchPlans", "fetchProjectDetails", "newProjectId", "fetchProjectTrackingData", "fetchProjects", "fetchSquareFootLinealFootCosts", "fetchTeamMembers", "filters", "fetchUserSubscriptions", "finalizeProject", "Lcom/manaknight/app/model/remote/FinalizeProjectResponse;", "finalizingOnboarding", "Lcom/manaknight/app/model/remote/FinalizingOnboardingResponse;", "forgotPassword", "Lcom/manaknight/app/model/remote/ForgotPasswordResponse;", "forgotPasswordMobile", "Lcom/manaknight/app/model/remote/ForgotPasswordMobileResponse;", "getAlertsList", "Lcom/manaknight/app/model/remote/GetAlertsListResponse;", "getAlertsPaginated", "Lcom/manaknight/app/model/remote/GetAlertsPaginatedResponse;", "getAllCMSLambda", "Lcom/manaknight/app/model/remote/GetAllCMSLambdaResponse;", "getAllDraws", "Lcom/manaknight/app/model/remote/profitPro/DrawInfoRespModel;", "getAllRoom", "Lcom/manaknight/app/model/remote/ChatRoomResponse;", "getAllUser", "Lcom/manaknight/app/model/remote/FriendListResponse;", "getAnalyticLogList", "Lcom/manaknight/app/model/remote/GetAnalyticLogListResponse;", "getAnalyticLogPaginated", "Lcom/manaknight/app/model/remote/GetAnalyticLogPaginatedResponse;", "getAnalytics", "Lcom/manaknight/app/model/remote/GetAnalyticsResponse;", "getApiKeysList", "Lcom/manaknight/app/model/remote/GetApiKeysListResponse;", "getApiKeysPaginated", "Lcom/manaknight/app/model/remote/GetApiKeysPaginatedResponse;", "getBlogCategory", "Lcom/manaknight/app/model/remote/GetBlogCategoryResponse;", "getBlogSubcategory", "Lcom/manaknight/app/model/remote/GetBlogSubcategoryResponse;", "getCMSByIDLambda", "Lcom/manaknight/app/model/remote/GetCMSByIDLambdaResponse;", "getCMSByPageAndKeyLambda", "Lcom/manaknight/app/model/remote/GetCMSByPageAndKeyLambdaResponse;", "getCMSByPageLambda", "Lcom/manaknight/app/model/remote/GetCMSByPageLambdaResponse;", "getCartItems", "Lcom/manaknight/app/model/remote/GetCartItemsResponse;", "getChangeOrderDescriptionList", "Lcom/manaknight/app/model/remote/GetChangeOrderDescriptionListResponse;", "getChangeOrderDescriptionPaginated", "Lcom/manaknight/app/model/remote/GetChangeOrderDescriptionPaginatedResponse;", "getChatList", "Lcom/manaknight/app/model/remote/GetChatListResponse;", "getChatPaginated", "Lcom/manaknight/app/model/remote/GetChatPaginatedResponse;", "getChats", "Lcom/manaknight/app/model/remote/ChatResponse;", "room_id", "getCmsList", "Lcom/manaknight/app/model/remote/GetCmsListResponse;", "getCmsPaginated", "Lcom/manaknight/app/model/remote/GetCmsPaginatedResponse;", "getCompanySettingsList", "Lcom/manaknight/app/model/remote/GetCompanySettingsListResponse;", "getCompanySettingsPaginated", "Lcom/manaknight/app/model/remote/GetCompanySettingsPaginatedResponse;", "getCostList", "Lcom/manaknight/app/model/remote/GetCostListResponse;", "getCostPaginated", "Lcom/manaknight/app/model/remote/GetCostPaginatedResponse;", "getCustomerList", "Lcom/manaknight/app/model/remote/GetCustomerListResponse;", "getCustomerPaginated", "Lcom/manaknight/app/model/remote/GetCustomerPaginatedResponse;", "getDefaultLinealFootCostList", "Lcom/manaknight/app/model/remote/GetDefaultLinealFootCostListResponse;", "getDefaultLinealFootCostPaginated", "Lcom/manaknight/app/model/remote/GetDefaultLinealFootCostPaginatedResponse;", "getDefaultMaterialList", "getDefaultMaterialPaginated", "Lcom/manaknight/app/model/remote/GetDefaultMaterialPaginatedResponse;", "getDefaultSquareFootCostList", "Lcom/manaknight/app/model/remote/GetDefaultSquareFootCostListResponse;", "getDefaultSquareFootCostPaginated", "Lcom/manaknight/app/model/remote/GetDefaultSquareFootCostPaginatedResponse;", "getDrawsList", "Lcom/manaknight/app/model/remote/GetDrawsListResponse;", "getDrawsPaginated", "Lcom/manaknight/app/model/remote/GetDrawsPaginatedResponse;", "getEmailList", "Lcom/manaknight/app/model/remote/GetEmailListResponse;", "getEmailPaginated", "Lcom/manaknight/app/model/remote/GetEmailPaginatedResponse;", "getEmployeeList", "Lcom/manaknight/app/model/remote/GetEmployeeListResponse;", "getEmployeePaginated", "Lcom/manaknight/app/model/remote/GetEmployeePaginatedResponse;", "getHeatmapData", "Lcom/manaknight/app/model/remote/GetHeatmapDataResponse;", "customDate", "getInvoiceList", "Lcom/manaknight/app/model/remote/GetInvoiceListResponse;", "getInvoicePaginated", "Lcom/manaknight/app/model/remote/GetInvoicePaginatedResponse;", "getJobList", "Lcom/manaknight/app/model/remote/GetJobListResponse;", "getJobPaginated", "Lcom/manaknight/app/model/remote/GetJobPaginatedResponse;", "getLaborList", "Lcom/manaknight/app/model/remote/GetLaborListResponse;", "getLaborPaginated", "Lcom/manaknight/app/model/remote/GetLaborPaginatedResponse;", "getLineDetails", "Lcom/manaknight/app/model/remote/GetLineDetailsResponse;", "lineId", "getLineItemEntryList", "Lcom/manaknight/app/model/remote/GetLineItemEntryListResponse;", "getLineItemEntryPaginated", "Lcom/manaknight/app/model/remote/GetLineItemEntryPaginatedResponse;", "getLineItemsList", "Lcom/manaknight/app/model/remote/GetLineItemsListResponse;", "getLineItemsPaginated", "Lcom/manaknight/app/model/remote/GetLineItemsPaginatedResponse;", "getLinealFootCostList", "Lcom/manaknight/app/model/remote/GetLinealFootCostListResponse;", "getLinealFootCostPaginated", "Lcom/manaknight/app/model/remote/GetLinealFootCostPaginatedResponse;", "getMaterialList", "Lcom/manaknight/app/model/remote/GetMaterialListResponse;", "getMaterialPaginated", "Lcom/manaknight/app/model/remote/GetMaterialPaginatedResponse;", "getOneAlerts", "Lcom/manaknight/app/model/remote/GetOneAlertsResponse;", "getOneAnalyticLog", "Lcom/manaknight/app/model/remote/GetOneAnalyticLogResponse;", "getOneApiKeys", "Lcom/manaknight/app/model/remote/GetOneApiKeysResponse;", "getOneChangeOrderDescription", "Lcom/manaknight/app/model/remote/GetOneChangeOrderDescriptionResponse;", "getOneChat", "Lcom/manaknight/app/model/remote/GetOneChatResponse;", "getOneCms", "Lcom/manaknight/app/model/remote/GetOneCmsResponse;", "getOneCompanySettings", "Lcom/manaknight/app/model/remote/GetOneCompanySettingsResponse;", "getOneCost", "Lcom/manaknight/app/model/remote/GetOneCostResponse;", "getOneCustomer", "Lcom/manaknight/app/model/remote/GetOneCustomerResponse;", "getOneDefaultLinealFootCost", "Lcom/manaknight/app/model/remote/GetOneDefaultLinealFootCostResponse;", "getOneDefaultMaterial", "Lcom/manaknight/app/model/remote/GetOneDefaultMaterialResponse;", "getOneDefaultSquareFootCost", "Lcom/manaknight/app/model/remote/GetOneDefaultSquareFootCostResponse;", "getOneDraws", "Lcom/manaknight/app/model/remote/GetOneDrawsResponse;", "getOneEmail", "Lcom/manaknight/app/model/remote/GetOneEmailResponse;", "getOneEmployee", "Lcom/manaknight/app/model/remote/GetOneEmployeeResponse;", "getOneInvoice", "Lcom/manaknight/app/model/remote/GetOneInvoiceResponse;", "getOneJob", "Lcom/manaknight/app/model/remote/GetOneJobResponse;", "getOneLabor", "Lcom/manaknight/app/model/remote/GetOneLaborResponse;", "getOneLineItemEntry", "Lcom/manaknight/app/model/remote/GetOneLineItemEntryResponse;", "getOneLineItems", "Lcom/manaknight/app/model/remote/GetOneLineItemsResponse;", "getOneLinealFootCost", "Lcom/manaknight/app/model/remote/GetOneLinealFootCostResponse;", "getOneMaterial", "Lcom/manaknight/app/model/remote/GetOneMaterialResponse;", "getOnePermission", "Lcom/manaknight/app/model/remote/GetOnePermissionResponse;", "getOnePhoto", "Lcom/manaknight/app/model/remote/GetOnePhotoResponse;", "getOnePosts", "Lcom/manaknight/app/model/remote/GetOnePostsResponse;", "getOneProfile", "Lcom/manaknight/app/model/remote/GetOneProfileResponse;", "getOneProject", "Lcom/manaknight/app/model/remote/GetOneProjectResponse;", "getOneRoom", "Lcom/manaknight/app/model/remote/GetOneRoomResponse;", "getOneSetting", "Lcom/manaknight/app/model/remote/GetOneSettingResponse;", "getOneSqftCosts", "Lcom/manaknight/app/model/remote/GetOneSqftCostsResponse;", "getOneTeamMember", "Lcom/manaknight/app/model/remote/GetOneTeamMemberResponse;", "getOneToken", "Lcom/manaknight/app/model/remote/GetOneTokenResponse;", "getOneTriggerType", "Lcom/manaknight/app/model/remote/GetOneTriggerTypeResponse;", "getOneUser", "Lcom/manaknight/app/model/remote/GetOneUserResponse;", "getPaymentHistory", "getPermissionList", "Lcom/manaknight/app/model/remote/GetPermissionListResponse;", "getPermissionPaginated", "Lcom/manaknight/app/model/remote/GetPermissionPaginatedResponse;", "getPhotoList", "Lcom/manaknight/app/model/remote/GetPhotoListResponse;", "getPhotoPaginated", "Lcom/manaknight/app/model/remote/GetPhotoPaginatedResponse;", "getPlans", "getPostsList", "Lcom/manaknight/app/model/remote/GetPostsListResponse;", "getPostsPaginated", "Lcom/manaknight/app/model/remote/GetPostsPaginatedResponse;", "getProfileList", "Lcom/manaknight/app/model/remote/GetProfileListResponse;", "getProfilePaginated", "Lcom/manaknight/app/model/remote/GetProfilePaginatedResponse;", "getProjectList", "Lcom/manaknight/app/model/remote/GetProjectListResponse;", "getProjectPaginated", "Lcom/manaknight/app/model/remote/GetProjectPaginatedResponse;", "getProjectReview", "Lcom/manaknight/app/model/remote/GetProjectReviewResponse;", "getProjectStats", "Lcom/manaknight/app/model/remote/GetProjectStatsResponse;", "getProjects", "Lcom/manaknight/app/model/remote/GetProjectsResponse;", "timePeriod", "getRoomList", "Lcom/manaknight/app/model/remote/GetRoomListResponse;", "getRoomPaginated", "Lcom/manaknight/app/model/remote/GetRoomPaginatedResponse;", "getSettingList", "Lcom/manaknight/app/model/remote/GetSettingListResponse;", "getSettingPaginated", "Lcom/manaknight/app/model/remote/GetSettingPaginatedResponse;", "getSingleProjectDetails", "getSingleProjectDetailsModified", "getSowTree", "Lcom/manaknight/app/model/remote/GetSowTreeResponse;", "getSqftCostsList", "Lcom/manaknight/app/model/remote/GetSqftCostsListResponse;", "getSqftCostsPaginated", "Lcom/manaknight/app/model/remote/GetSqftCostsPaginatedResponse;", "getSquareFootLinealFootCosts", "getSquareFootLinealFootCostsNew", "getStartPool", "Lcom/manaknight/app/model/remote/SingleChatMessageResponse;", "getStripeData", "Lcom/manaknight/app/model/remote/GetStripeDataResponse;", "currency", "getSubscriptionStatus", "Lcom/manaknight/app/model/remote/GetSubscriptionStatusResponse;", "getTeamMemberList", "getTeamMemberPaginated", "Lcom/manaknight/app/model/remote/GetTeamMemberPaginatedResponse;", "getTokenList", "Lcom/manaknight/app/model/remote/GetTokenListResponse;", "getTokenPaginated", "Lcom/manaknight/app/model/remote/GetTokenPaginatedResponse;", "getTriggerTypeList", "Lcom/manaknight/app/model/remote/GetTriggerTypeListResponse;", "getTriggerTypePaginated", "Lcom/manaknight/app/model/remote/GetTriggerTypePaginatedResponse;", "getUserList", "Lcom/manaknight/app/model/remote/GetUserListResponse;", "getUserPaginated", "Lcom/manaknight/app/model/remote/GetUserPaginatedResponse;", "getUserSubscriptions", "googleCaptchaVerify", "Lcom/manaknight/app/model/remote/GoogleCaptchaVerifyResponse;", "formdata", "captchatoken", "googleCode", "Lcom/manaknight/app/model/remote/GoogleCodeResponse;", "googleCodeMobile", "Lcom/manaknight/app/model/remote/GoogleCodeMobileResponse;", "isRefresh", "(Ljava/lang/String;Ljava/lang/Boolean;Ljava/lang/String;)Landroidx/lifecycle/LiveData;", "googleLogin", "Lcom/manaknight/app/model/remote/GoogleLoginResponse;", "companyId", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Boolean;)Landroidx/lifecycle/LiveData;", "initializeDraws", "initializeUser", "Lcom/manaknight/app/model/remote/InitializeUserResponse;", "lambdaCheck", "Lcom/manaknight/app/model/remote/LambdaCheckResponse;", "logHeatmapAnalytics", "Lcom/manaknight/app/model/remote/LogHeatmapAnalyticsResponse;", "scrollPosition", "coordinates", "snapshot", "loginLambda", "Lcom/manaknight/app/model/remote/LoginLambdaResponse;", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Boolean;)Landroidx/lifecycle/LiveData;", "marketingLoginLambda", "Lcom/manaknight/app/model/remote/MarketingLoginLambdaResponse;", "onboarding", "Lcom/manaknight/app/model/remote/OnboardingResponse;", "preferenceFetch", "Lcom/manaknight/app/model/remote/PreferenceFetchResponse;", "preferenceUpdate", "Lcom/manaknight/app/model/remote/PreferenceUpdateResponse;", "profile", "Lcom/manaknight/app/model/remote/ProfileResponse;", "profileUpdate", "Lcom/manaknight/app/model/remote/ProfileUpdateResponse;", "refreshTeamMembers", "registerLambda", "Lcom/manaknight/app/model/remote/RegisterLambdaResponse;", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Boolean;Ljava/lang/Boolean;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Landroidx/lifecycle/LiveData;", "resetLinearItemVisualStateAfterDelay", "item", "resetPassword", "Lcom/manaknight/app/model/remote/ResetPasswordResponse;", "resetPasswordMobile", "Lcom/manaknight/app/model/remote/ResetPasswordMobileResponse;", "retrieveProductDefault", "Lcom/manaknight/app/model/remote/RetrieveProductDefaultResponse;", "sortid", "direction", "saveDefaultsOnbording", "Lcom/manaknight/app/model/remote/SaveDefaultsOnbordingResponse;", "searchCustomers", "Lcom/manaknight/app/model/remote/profitPro/CustomerResponseModel;", "searchText", "sendInvoice", "pdf", "project_id", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;)Landroidx/lifecycle/LiveData;", "sendMessageToBot", "Lcom/manaknight/app/model/remote/ChatBotTextResponse;", "Lcom/manaknight/app/model/remote/ChatBotRequest;", "sendTextMessage", "Lcom/manaknight/app/model/remote/ChatTextResponse;", "setProjectId", "setTeamMemberFilters", "setTrackingProjectId", "setUserId", "newUserId", "setUserIdForSubscriptions", "signupCompanySeup", "Lcom/manaknight/app/model/remote/profitPro/CompanyRequest;", "trackingDraws", "Lcom/manaknight/app/model/remote/TrackingDrawsResponse;", "trackingLabour", "Lcom/manaknight/app/model/remote/TrackingLabourResponse;", "trackingMaterial", "Lcom/manaknight/app/model/remote/TrackingMaterialResponse;", "twoFAAuth", "Lcom/manaknight/app/model/remote/TwoFAAuthResponse;", "twoFAAuthorize", "Lcom/manaknight/app/model/remote/TwoFAAuthorizeResponse;", "twoFADisable", "Lcom/manaknight/app/model/remote/TwoFADisableResponse;", "twoFAEnable", "Lcom/manaknight/app/model/remote/TwoFAEnableResponse;", "twoFALogin", "Lcom/manaknight/app/model/remote/TwoFALoginResponse;", "twoFASignin", "Lcom/manaknight/app/model/remote/TwoFASigninResponse;", "twoFAVerify", "Lcom/manaknight/app/model/remote/TwoFAVerifyResponse;", "accessToken", "updateAlerts", "Lcom/manaknight/app/model/remote/UpdateAlertsResponse;", "(Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/Integer;)Landroidx/lifecycle/LiveData;", "updateAnalyticLog", "Lcom/manaknight/app/model/remote/UpdateAnalyticLogResponse;", "(Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;)Landroidx/lifecycle/LiveData;", "updateApiKeys", "Lcom/manaknight/app/model/remote/UpdateApiKeysResponse;", "updateBlogCategory", "Lcom/manaknight/app/model/remote/UpdateBlogCategoryResponse;", "updateCMSLambda", "Lcom/manaknight/app/model/remote/UpdateCMSLambdaResponse;", "updateChangeOrderDescription", "Lcom/manaknight/app/model/remote/UpdateChangeOrderDescriptionResponse;", "updateChat", "Lcom/manaknight/app/model/remote/UpdateChatResponse;", "updateCms", "Lcom/manaknight/app/model/remote/UpdateCmsResponse;", "updateCompanyDefault", "Lcom/manaknight/app/model/remote/profitPro/DefaultModel;", "updateCompanyDefaultNew", "updateCompanySettings", "Lcom/manaknight/app/model/remote/UpdateCompanySettingsResponse;", "updateCost", "Lcom/manaknight/app/model/remote/UpdateCostResponse;", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/Integer;)Landroidx/lifecycle/LiveData;", "updateCustomer", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/Integer;)Landroidx/lifecycle/LiveData;", "updateDefaultLinealFootCost", "Lcom/manaknight/app/model/remote/UpdateDefaultLinealFootCostResponse;", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/Integer;)Landroidx/lifecycle/LiveData;", "updateDefaultMaterial", "updateDefaultMaterialNew", "updateDefaultSquareFootCost", "Lcom/manaknight/app/model/remote/UpdateDefaultSquareFootCostResponse;", "updateDraws", "Lcom/manaknight/app/model/remote/UpdateDrawsResponse;", "draws", "paymentType", "checkNo", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/Integer;)Landroidx/lifecycle/LiveData;", "updateEmail", "Lcom/manaknight/app/model/remote/UpdateEmailResponse;", "updateEmployee", "Lcom/manaknight/app/model/remote/UpdateEmployeeResponse;", "(Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/Integer;)Landroidx/lifecycle/LiveData;", "updateInvoice", "Lcom/manaknight/app/model/remote/UpdateInvoiceResponse;", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;)Landroidx/lifecycle/LiveData;", "updateJob", "Lcom/manaknight/app/model/remote/UpdateJobResponse;", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;)Landroidx/lifecycle/LiveData;", "updateLabor", "Lcom/manaknight/app/model/remote/UpdateLaborResponse;", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/Integer;)Landroidx/lifecycle/LiveData;", "updateLineItem", "itemID", "Lcom/manaknight/app/model/remote/profitPro/UpdateLineItemReqModel;", "updateLineItemEntry", "Lcom/manaknight/app/model/remote/UpdateLineItemEntryResponse;", "(Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/Integer;)Landroidx/lifecycle/LiveData;", "updateLineItems", "Lcom/manaknight/app/model/remote/UpdateLineItemsResponse;", "(Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;)Landroidx/lifecycle/LiveData;", "updateLinealFootCost", "(ILjava/lang/String;Ljava/lang/Integer;Ljava/lang/Integer;ILjava/lang/Integer;)Landroidx/lifecycle/LiveData;", "Lcom/manaknight/app/model/remote/UpdateLinealFootCostResponse;", "(Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/Integer;)Landroidx/lifecycle/LiveData;", "updateLinealFootCostNew", "updateMaterial", "Lcom/manaknight/app/model/remote/UpdateMaterialResponse;", "(Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/Integer;)Landroidx/lifecycle/LiveData;", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/String;IIILjava/lang/Integer;)Landroidx/lifecycle/LiveData;", "updatePercentageDraw", "updatePermission", "Lcom/manaknight/app/model/remote/UpdatePermissionResponse;", "updatePhoto", "Lcom/manaknight/app/model/remote/UpdatePhotoResponse;", "updatePosts", "Lcom/manaknight/app/model/remote/UpdatePostsResponse;", "updatePriceDraw", "updateProfile", "Lcom/manaknight/app/model/remote/UpdateProfileResponse;", "updateProject", "Lcom/manaknight/app/model/remote/UpdateProjectResponse;", "(Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;)Landroidx/lifecycle/LiveData;", "updateRoom", "Lcom/manaknight/app/model/remote/UpdateRoomResponse;", "updateSetting", "Lcom/manaknight/app/model/remote/UpdateSettingResponse;", "updateSqftCosts", "Lcom/manaknight/app/model/remote/UpdateSqftCostsResponse;", "updateSquareFootCost", "updateSquareFootCostNew", "updateTeamMember", "(Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/Integer;)Landroidx/lifecycle/LiveData;", "updateTeamMemberNew", "updateToken", "Lcom/manaknight/app/model/remote/UpdateTokenResponse;", "(Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/Integer;)Landroidx/lifecycle/LiveData;", "updateTriggerType", "Lcom/manaknight/app/model/remote/UpdateTriggerTypeResponse;", "updateUser", "Lcom/manaknight/app/model/remote/UpdateUserResponse;", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/Integer;)Landroidx/lifecycle/LiveData;", "uploadImageLocalDefault", "Lcom/manaknight/app/model/remote/UploadImageLocalDefaultResponse;", "file", "Lokhttp3/MultipartBody$Part;", "uploadimages3", "Lcom/manaknight/app/model/remote/UploadImageS3Response;", "userSessionsData", "Lcom/manaknight/app/model/remote/UserSessionsDataResponse;", "TeamMemberFilters", "app_debug"})
public final class BaasViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.manaknight.app.repositories.APIRepository repository = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<java.lang.Integer> _userId = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.ProjectResponseModel>> _projectsResource = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.ProjectResponseModel>> projectsResource = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<java.lang.Integer> _projectId = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.AllLineItemsResponseModel>> _projectDetailsResource = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.AllLineItemsResponseModel>> projectDetailsResource = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<com.manaknight.app.model.remote.profitPro.AllLineItemsResponseModel> _cachedProjectDetailsResource = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<com.manaknight.app.model.remote.profitPro.AllLineItemsResponseModel> cachedProjectDetailsResource = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.AllLineItemsResponseModel>> _projectDetails = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.AllLineItemsResponseModel>> projectDetails = null;
    @org.jetbrains.annotations.Nullable()
    private com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.AllLineItemsResponseModel> cachedProjectDetails;
    private boolean apiCallMade = false;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<java.util.List<com.manaknight.app.viewmodels.LinearItemDisplay>> _linearItems = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.util.List<com.manaknight.app.viewmodels.LinearItemDisplay>> linearItems = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.LinearResponseModel>> _linealCostsResource = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.LinearResponseModel>> linealCostsResource = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.MaterialResponseModel>> _materialListResource = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.MaterialResponseModel>> materialListResource = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<java.util.List<com.manaknight.app.viewmodels.MaterialDisplay>> _materialItems = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.util.List<com.manaknight.app.viewmodels.MaterialDisplay>> materialItems = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetTeamMemberListResponse>> _teamMembersResource = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetTeamMemberListResponse>> teamMembersResource = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<com.manaknight.app.viewmodels.BaasViewModel.TeamMemberFilters> _teamMemberFilters = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.CreateTeamMemberResponse>> _createTeamMemberResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.CreateTeamMemberResponse>> createTeamMemberResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.UpdateTeamMemberResponse>> _updateTeamMemberResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.UpdateTeamMemberResponse>> updateTeamMemberResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.DeleteTeamMemberResponse>> _deleteTeamMemberResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.DeleteTeamMemberResponse>> deleteTeamMemberResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> _updateDefaultResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> updateDefaultResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> _addSquareFootCostResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> addSquareFootCostResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> _addLinealFootCostResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> addLinealFootCostResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> _updateLinealFootCostResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> updateLinealFootCostResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> _updateSquareFootCostResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> updateSquareFootCostResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> _deleteLinealFootCostResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> deleteLinealFootCostResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> _deleteSquareFootCostResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> deleteSquareFootCostResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.UpdateDefaultMaterialResponse>> _updateDefaultMaterialResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.UpdateDefaultMaterialResponse>> updateDefaultMaterialResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.DeleteDefaultMaterialResponse>> _deleteDefaultMaterialResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.DeleteDefaultMaterialResponse>> deleteDefaultMaterialResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> _createMaterialResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> createMaterialResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.LinearResponseModel>> _getLinealFootCostsResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.LinearResponseModel>> getLinealFootCostsResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.LinearResponseModel>> _getSquareFootCostsResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.LinearResponseModel>> getSquareFootCostsResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.ProjectTrackingResponse>> _trackingResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.ProjectTrackingResponse>> trackingResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetUserSubscriptionsResponse>> _userSubscriptionsResource = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetUserSubscriptionsResponse>> userSubscriptionsResource = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetPlansResponse>> _plansResource = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetPlansResponse>> plansResource = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetPaymentHistoryResponse>> _paymentHistoryResource = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetPaymentHistoryResponse>> paymentHistoryResource = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.CancelSubscriptionResponse>> _cancelSubscriptionResponse = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.CancelSubscriptionResponse>> cancelSubscriptionResponse = null;
    
    public BaasViewModel(@org.jetbrains.annotations.NotNull()
    com.manaknight.app.repositories.APIRepository repository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> signupCompanySeup(@org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.CompanyRequest request) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> updateCompanyDefault(@org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.DefaultModel request) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> createMaterial(@org.jetbrains.annotations.Nullable()
    java.lang.String name, @org.jetbrains.annotations.Nullable()
    java.lang.String email, @org.jetbrains.annotations.Nullable()
    java.lang.Integer userId, @org.jetbrains.annotations.Nullable()
    java.lang.String session_name, int cost, int hidden, int is_default) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> updateMaterial(@org.jetbrains.annotations.Nullable()
    java.lang.String name, @org.jetbrains.annotations.Nullable()
    java.lang.String email, @org.jetbrains.annotations.Nullable()
    java.lang.Integer userId, @org.jetbrains.annotations.Nullable()
    java.lang.String session_name, int cost, int hidden, int is_default, @org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CustomerResponseModel>> searchCustomers(@org.jetbrains.annotations.Nullable()
    java.lang.String searchText) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> createCustomer(@org.jetbrains.annotations.Nullable()
    java.lang.String name, @org.jetbrains.annotations.Nullable()
    java.lang.String email, @org.jetbrains.annotations.Nullable()
    java.lang.String phone, @org.jetbrains.annotations.Nullable()
    java.lang.String address, @org.jetbrains.annotations.Nullable()
    java.lang.Integer userId) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> updateCustomer(@org.jetbrains.annotations.Nullable()
    java.lang.String name, @org.jetbrains.annotations.Nullable()
    java.lang.String email, @org.jetbrains.annotations.Nullable()
    java.lang.String phone, @org.jetbrains.annotations.Nullable()
    java.lang.String address, @org.jetbrains.annotations.Nullable()
    java.lang.Integer userId, @org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> createNewEstimation(@org.jetbrains.annotations.Nullable()
    java.lang.Integer customerID) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.MaterialResponseModel>> getDefaultMaterialList(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String size, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> createDefaultMaterial(@org.jetbrains.annotations.Nullable()
    java.lang.Integer userId, @org.jetbrains.annotations.Nullable()
    java.lang.Integer cost, @org.jetbrains.annotations.Nullable()
    java.lang.String name, @org.jetbrains.annotations.Nullable()
    java.lang.Integer hidden) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> addLineItem(@org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.CreateLineItemReqModel request) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> updateLineItem(int itemID, @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.UpdateLineItemReqModel request) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.LinearResponseModel>> getSquareFootLinealFootCosts(@org.jetbrains.annotations.Nullable()
    java.lang.String type) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> addLinealFootCost(@org.jetbrains.annotations.Nullable()
    java.lang.String name, @org.jetbrains.annotations.Nullable()
    java.lang.Integer cost, @org.jetbrains.annotations.Nullable()
    java.lang.Integer laborCost, @org.jetbrains.annotations.Nullable()
    java.lang.Integer hidden) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> addSquareFootCost(@org.jetbrains.annotations.Nullable()
    java.lang.String name, @org.jetbrains.annotations.Nullable()
    java.lang.Integer cost, @org.jetbrains.annotations.Nullable()
    java.lang.Integer laborCost, @org.jetbrains.annotations.Nullable()
    java.lang.Integer hidden) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.AllLineItemsResponseModel>> getSingleProjectDetails(int projectId) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.ProjectResponseModel>> getProjectsResource() {
        return null;
    }
    
    public final void setUserId(int newUserId) {
    }
    
    private final void fetchProjects(int userId) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.AllLineItemsResponseModel>> getProjectDetailsResource() {
        return null;
    }
    
    public final void setProjectId(int newProjectId) {
    }
    
    public final void fetchProjectDetails(int newProjectId) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.model.remote.profitPro.AllLineItemsResponseModel> getCachedProjectDetailsResource() {
        return null;
    }
    
    public final void cacheProjectDetailsResource(@org.jetbrains.annotations.Nullable()
    com.manaknight.app.model.remote.profitPro.AllLineItemsResponseModel resource) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.ProjectResponseModel>> getProjects(int user_id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.AllLineItemsResponseModel>> getProjectDetails() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.AllLineItemsResponseModel>> getSingleProjectDetailsModified(int projectId) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.util.List<com.manaknight.app.viewmodels.LinearItemDisplay>> getLinearItems() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.LinearResponseModel>> getLinealCostsResource() {
        return null;
    }
    
    public final void fetchSquareFootLinealFootCosts(@org.jetbrains.annotations.Nullable()
    java.lang.String type) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.MaterialResponseModel>> getMaterialListResource() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.util.List<com.manaknight.app.viewmodels.MaterialDisplay>> getMaterialItems() {
        return null;
    }
    
    public final void fetchDefaultMaterialList(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String size, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
    }
    
    private final void resetLinearItemVisualStateAfterDelay(com.manaknight.app.viewmodels.LinearItemDisplay item) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetTeamMemberListResponse>> getTeamMembersResource() {
        return null;
    }
    
    public final void setTeamMemberFilters(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String size, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
    }
    
    public final void refreshTeamMembers() {
    }
    
    private final void fetchTeamMembers(com.manaknight.app.viewmodels.BaasViewModel.TeamMemberFilters filters) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.CreateTeamMemberResponse>> getCreateTeamMemberResponse() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.UpdateTeamMemberResponse>> getUpdateTeamMemberResponse() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.DeleteTeamMemberResponse>> getDeleteTeamMemberResponse() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> getUpdateDefaultResponse() {
        return null;
    }
    
    public final void clearUpdateTeamMemberResponse() {
    }
    
    public final void clearCreateTeamMemberNewResponse() {
    }
    
    public final void clearUpdateCompanyDefaultNewResponse() {
    }
    
    public final void clearDeleteTeamMemberNewResponse() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.CreateTeamMemberResponse>> createTeamMemberNew(@org.jetbrains.annotations.Nullable()
    java.lang.Integer projectId, @org.jetbrains.annotations.Nullable()
    java.lang.Integer userId, @org.jetbrains.annotations.Nullable()
    java.lang.String name, @org.jetbrains.annotations.Nullable()
    java.lang.Integer hourlyRate, @org.jetbrains.annotations.Nullable()
    java.lang.Integer isDefault) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.UpdateTeamMemberResponse>> updateTeamMemberNew(@org.jetbrains.annotations.Nullable()
    java.lang.Integer projectId, @org.jetbrains.annotations.Nullable()
    java.lang.Integer userId, @org.jetbrains.annotations.Nullable()
    java.lang.String name, @org.jetbrains.annotations.Nullable()
    java.lang.Integer hourlyRate, @org.jetbrains.annotations.Nullable()
    java.lang.Integer isDefault, @org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.DeleteTeamMemberResponse>> deleteTeamMemberNew(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> updateCompanyDefaultNew(@org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.DefaultModel request) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> getAddSquareFootCostResponse() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> getAddLinealFootCostResponse() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> getUpdateLinealFootCostResponse() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> getUpdateSquareFootCostResponse() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> getDeleteLinealFootCostResponse() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> getDeleteSquareFootCostResponse() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.UpdateDefaultMaterialResponse>> getUpdateDefaultMaterialResponse() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.DeleteDefaultMaterialResponse>> getDeleteDefaultMaterialResponse() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> getCreateMaterialResponse() {
        return null;
    }
    
    public final void clearAddSquareFootCostResponse() {
    }
    
    public final void clearAddLinealFootCostResponse() {
    }
    
    public final void clearUpdateLinealFootCostResponse() {
    }
    
    public final void clearUpdateSquareFootCostResponse() {
    }
    
    public final void clearDeleteLinealFootCostResponse() {
    }
    
    public final void clearDeleteSquareFootCostResponse() {
    }
    
    public final void clearUpdateDefaultMaterialResponse() {
    }
    
    public final void clearDeleteDefaultMaterialResponse() {
    }
    
    public final void clearCreateMaterialResponse() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.LinearResponseModel>> getGetLinealFootCostsResponse() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.LinearResponseModel>> getGetSquareFootCostsResponse() {
        return null;
    }
    
    public final void clearGetLinealFootCostsResponse() {
    }
    
    public final void clearGetSquareFootCostsResponse() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.LinearResponseModel>> getSquareFootLinealFootCostsNew(@org.jetbrains.annotations.Nullable()
    java.lang.String type) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> addSquareFootCostNew(@org.jetbrains.annotations.Nullable()
    java.lang.String name, @org.jetbrains.annotations.Nullable()
    java.lang.Integer cost, @org.jetbrains.annotations.Nullable()
    java.lang.Integer laborCost, @org.jetbrains.annotations.Nullable()
    java.lang.Integer hidden) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> addLinealFootCostNew(@org.jetbrains.annotations.Nullable()
    java.lang.String name, @org.jetbrains.annotations.Nullable()
    java.lang.Integer cost, @org.jetbrains.annotations.Nullable()
    java.lang.Integer laborCost, @org.jetbrains.annotations.Nullable()
    java.lang.Integer hidden) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> updateLinealFootCostNew(int cost, @org.jetbrains.annotations.Nullable()
    java.lang.String name, @org.jetbrains.annotations.Nullable()
    java.lang.Integer hidden, @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, int laborCost, @org.jetbrains.annotations.Nullable()
    java.lang.Integer isDefault) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> updateSquareFootCostNew(int cost, @org.jetbrains.annotations.Nullable()
    java.lang.String name, @org.jetbrains.annotations.Nullable()
    java.lang.Integer hidden, @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, int laborCost, @org.jetbrains.annotations.Nullable()
    java.lang.Integer isDefault) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> deleteLinealFootCostsNew(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> deleteSquareFootCostNew(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.UpdateDefaultMaterialResponse>> updateDefaultMaterialNew(@org.jetbrains.annotations.Nullable()
    java.lang.String userId, @org.jetbrains.annotations.Nullable()
    java.lang.String cost, @org.jetbrains.annotations.Nullable()
    java.lang.String name, @org.jetbrains.annotations.Nullable()
    java.lang.Integer hidden, @org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.DeleteDefaultMaterialResponse>> deleteDefaultMaterialNew(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> createMaterialNew(@org.jetbrains.annotations.Nullable()
    java.lang.String name, @org.jetbrains.annotations.Nullable()
    java.lang.String email, @org.jetbrains.annotations.Nullable()
    java.lang.Integer userId, @org.jetbrains.annotations.Nullable()
    java.lang.String session_name, int cost, int hidden, int is_default) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> initializeDraws(int projectId) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.DrawInfoRespModel>> getAllDraws(int projectId) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> deleteDraws(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> createPriceDraw(@org.jetbrains.annotations.Nullable()
    java.lang.Integer projectID, @org.jetbrains.annotations.Nullable()
    java.lang.String description, @org.jetbrains.annotations.Nullable()
    java.lang.String amount) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> createPercentageDraw(@org.jetbrains.annotations.Nullable()
    java.lang.Integer projectID, @org.jetbrains.annotations.Nullable()
    java.lang.String description, @org.jetbrains.annotations.Nullable()
    java.lang.String percentage) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> sendInvoice(@org.jetbrains.annotations.Nullable()
    java.lang.String pdf, @org.jetbrains.annotations.Nullable()
    java.lang.String session_name, @org.jetbrains.annotations.Nullable()
    java.lang.String email, @org.jetbrains.annotations.Nullable()
    java.lang.Integer project_id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> updatePriceDraw(@org.jetbrains.annotations.Nullable()
    java.lang.Integer projectID, @org.jetbrains.annotations.Nullable()
    java.lang.String description, @org.jetbrains.annotations.Nullable()
    java.lang.String amount, @org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> updatePercentageDraw(@org.jetbrains.annotations.Nullable()
    java.lang.Integer projectID, @org.jetbrains.annotations.Nullable()
    java.lang.String description, @org.jetbrains.annotations.Nullable()
    java.lang.String percentage, @org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> deleteLineItems(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.FriendListResponse>> getAllUser() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.CreateRoomResponse>> createRoomRequests(int user_id, @org.jetbrains.annotations.Nullable()
    java.lang.Integer other_user_id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.ChatTextResponse>> sendTextMessage(@org.jetbrains.annotations.NotNull()
    java.lang.String room_id, int user_id, @org.jetbrains.annotations.Nullable()
    java.lang.String message) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.ChatResponse>> getChats(@org.jetbrains.annotations.NotNull()
    java.lang.String room_id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.SingleChatMessageResponse>> getStartPool(int user_id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.ChatRoomResponse>> getAllRoom(int user_id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.ChatBotTextResponse>> sendMessageToBot(@org.jetbrains.annotations.NotNull()
    java.lang.String request) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.ChatBotTextResponse>> sendMessageToBot(@org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.ChatBotRequest request) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.CreateChangeOrderResponse>> createChangeOrder(@org.jetbrains.annotations.Nullable()
    java.lang.String data, @org.jetbrains.annotations.NotNull()
    java.lang.Object projectId) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.FinalizeProjectResponse>> finalizeProject() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetProjectReviewResponse>> getProjectReview(@org.jetbrains.annotations.NotNull()
    java.lang.Object projectId) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.UpdateDrawsResponse>> updateDraws(@org.jetbrains.annotations.Nullable()
    java.lang.String draws, int projectId, @org.jetbrains.annotations.Nullable()
    java.lang.String paymentType, int status, @org.jetbrains.annotations.Nullable()
    java.lang.String description, @org.jetbrains.annotations.Nullable()
    java.lang.String percentage, @org.jetbrains.annotations.Nullable()
    java.lang.String amount) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.ProjectTrackingResponse>> getTrackingResponse() {
        return null;
    }
    
    public final void setTrackingProjectId(int id) {
    }
    
    public final void fetchProjectTrackingData(int projectId) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.TrackingMaterialResponse>> trackingMaterial(int projectId) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.TrackingLabourResponse>> trackingLabour(int projectId) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.TrackingDrawsResponse>> trackingDraws(int projectId, @org.jetbrains.annotations.Nullable()
    java.lang.String status) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetLineDetailsResponse>> getLineDetails(@org.jetbrains.annotations.NotNull()
    java.lang.Object lineId) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.FinalizingOnboardingResponse>> finalizingOnboarding() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.InitializeUserResponse>> initializeUser() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.SaveDefaultsOnbordingResponse>> saveDefaultsOnbording(int defaultHourlyRate, int defaultProfitOverhead) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetProjectsResponse>> getProjects(@org.jetbrains.annotations.Nullable()
    java.lang.String type, @org.jetbrains.annotations.Nullable()
    java.lang.String timePeriod) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.OnboardingResponse>> onboarding(int defaultHourlyRate, int defaultProfitOverhead, @org.jetbrains.annotations.Nullable()
    java.lang.String name) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.CompanyOverviewResponse>> companyOverview() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.CompanyDetailsResponse>> companyDetails() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetProjectStatsResponse>> getProjectStats() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.LambdaCheckResponse>> lambdaCheck(@org.jetbrains.annotations.Nullable()
    java.lang.String role) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.TwoFALoginResponse>> twoFALogin(@org.jetbrains.annotations.Nullable()
    java.lang.String email, @org.jetbrains.annotations.Nullable()
    java.lang.String password, @org.jetbrains.annotations.Nullable()
    java.lang.String role) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.TwoFASigninResponse>> twoFASignin(@org.jetbrains.annotations.Nullable()
    java.lang.String email, @org.jetbrains.annotations.Nullable()
    java.lang.String password, @org.jetbrains.annotations.Nullable()
    java.lang.String role) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.TwoFAAuthorizeResponse>> twoFAAuthorize(@org.jetbrains.annotations.Nullable()
    java.lang.String userId) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.TwoFAEnableResponse>> twoFAEnable(@org.jetbrains.annotations.Nullable()
    java.lang.String userId, @org.jetbrains.annotations.Nullable()
    java.lang.String type, @org.jetbrains.annotations.Nullable()
    java.lang.String phone, @org.jetbrains.annotations.Nullable()
    java.lang.String token) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.TwoFADisableResponse>> twoFADisable(@org.jetbrains.annotations.Nullable()
    java.lang.String userId) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.TwoFAVerifyResponse>> twoFAVerify(@org.jetbrains.annotations.Nullable()
    java.lang.String token, @org.jetbrains.annotations.Nullable()
    java.lang.String accessToken) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.TwoFAAuthResponse>> twoFAAuth(@org.jetbrains.annotations.Nullable()
    java.lang.String code, @org.jetbrains.annotations.Nullable()
    java.lang.String token) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.AnalyticsLogResponse>> analyticsLog(@org.jetbrains.annotations.Nullable()
    java.lang.String userId, @org.jetbrains.annotations.Nullable()
    java.lang.String sessionId, @org.jetbrains.annotations.Nullable()
    java.lang.String status, @org.jetbrains.annotations.Nullable()
    java.lang.String userAgent, @org.jetbrains.annotations.Nullable()
    java.lang.String application, @org.jetbrains.annotations.Nullable()
    java.lang.String document, @org.jetbrains.annotations.NotNull()
    java.util.ArrayList<java.util.Map<java.lang.String, java.lang.Object>> url, @org.jetbrains.annotations.Nullable()
    java.lang.Number linkClicks, @org.jetbrains.annotations.NotNull()
    java.util.ArrayList<java.util.Map<java.lang.String, java.lang.Object>> clickedButtons, @org.jetbrains.annotations.Nullable()
    java.lang.String clientIp, @org.jetbrains.annotations.NotNull()
    java.util.ArrayList<java.util.Map<java.lang.String, java.lang.Object>> events, @org.jetbrains.annotations.Nullable()
    java.lang.Number totalTime, @org.jetbrains.annotations.NotNull()
    java.util.Map<java.lang.String, ? extends java.lang.Object> data) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetAnalyticsResponse>> getAnalytics() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.LogHeatmapAnalyticsResponse>> logHeatmapAnalytics(@org.jetbrains.annotations.Nullable()
    java.lang.String userId, @org.jetbrains.annotations.Nullable()
    java.lang.String sessionId, @org.jetbrains.annotations.Nullable()
    java.lang.String userAgent, @org.jetbrains.annotations.NotNull()
    java.util.Map<java.lang.String, ? extends java.lang.Object> scrollPosition, @org.jetbrains.annotations.NotNull()
    java.util.Map<java.lang.String, ? extends java.lang.Object> coordinates, @org.jetbrains.annotations.Nullable()
    java.lang.String status, @org.jetbrains.annotations.Nullable()
    java.lang.String clientIp, @org.jetbrains.annotations.Nullable()
    java.lang.Number screenSize, @org.jetbrains.annotations.Nullable()
    java.lang.Number screenWidth, @org.jetbrains.annotations.Nullable()
    java.lang.String page, @org.jetbrains.annotations.Nullable()
    java.lang.Number screenHeight, @org.jetbrains.annotations.Nullable()
    java.lang.String snapshot, @org.jetbrains.annotations.Nullable()
    java.lang.Number totalTime, @org.jetbrains.annotations.NotNull()
    java.util.Map<java.lang.String, ? extends java.lang.Object> data) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetHeatmapDataResponse>> getHeatmapData(@org.jetbrains.annotations.Nullable()
    java.lang.String customDate) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.UserSessionsDataResponse>> userSessionsData(@org.jetbrains.annotations.Nullable()
    java.lang.String customDate) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.CreateUserSessionsAnalyticsResponse>> createUserSessionsAnalytics(int userId, int sessionId, @org.jetbrains.annotations.Nullable()
    java.lang.String status, @org.jetbrains.annotations.NotNull()
    java.util.ArrayList<java.util.Map<java.lang.String, java.lang.Object>> events, int screenWidth, int screenHeight, @org.jetbrains.annotations.Nullable()
    java.lang.String screenSize, @org.jetbrains.annotations.Nullable()
    java.lang.String startTime, @org.jetbrains.annotations.Nullable()
    java.lang.String endTime, @org.jetbrains.annotations.Nullable()
    java.lang.String htmlCopy) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.AppleLoginMobileEndpointResponse>> appleLoginMobileEndpoint(@org.jetbrains.annotations.Nullable()
    java.lang.String firstName, @org.jetbrains.annotations.Nullable()
    java.lang.String lastName, @org.jetbrains.annotations.Nullable()
    java.lang.String identitytoken, @org.jetbrains.annotations.Nullable()
    java.lang.String appleId, @org.jetbrains.annotations.Nullable()
    java.lang.String role) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.AppleLoginResponse>> appleLogin() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.AppleAuthCodeResponse>> appleAuthCode(@org.jetbrains.annotations.Nullable()
    java.lang.String state, @org.jetbrains.annotations.Nullable()
    java.lang.String code) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GoogleCodeResponse>> googleCode(@org.jetbrains.annotations.NotNull()
    java.lang.String state) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GoogleCodeMobileResponse>> googleCodeMobile(@org.jetbrains.annotations.Nullable()
    java.lang.String role, @org.jetbrains.annotations.Nullable()
    java.lang.Boolean isRefresh, @org.jetbrains.annotations.Nullable()
    java.lang.String code) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GoogleLoginResponse>> googleLogin(@org.jetbrains.annotations.Nullable()
    java.lang.String role, @org.jetbrains.annotations.Nullable()
    java.lang.String companyId, @org.jetbrains.annotations.Nullable()
    java.lang.Boolean isRefresh) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.BlogAllResponse>> blogAll(int limit, int offset) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.BlogSimilarResponse>> blogSimilar(int top) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.BlogFilterResponse>> blogFilter(@org.jetbrains.annotations.NotNull()
    java.util.ArrayList<java.util.Map<java.lang.String, java.lang.Object>> categories, @org.jetbrains.annotations.NotNull()
    java.util.ArrayList<java.util.Map<java.lang.String, java.lang.Object>> tags, @org.jetbrains.annotations.Nullable()
    java.lang.String rule, @org.jetbrains.annotations.Nullable()
    java.lang.String search, int limit, int page) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.BlogCreateResponse>> blogCreate(@org.jetbrains.annotations.Nullable()
    java.lang.String title, @org.jetbrains.annotations.Nullable()
    java.lang.String body, @org.jetbrains.annotations.NotNull()
    java.util.Map<java.lang.String, ? extends java.lang.Object> meta, @org.jetbrains.annotations.NotNull()
    java.util.ArrayList<java.util.Map<java.lang.String, java.lang.Object>> tags, @org.jetbrains.annotations.NotNull()
    java.util.ArrayList<java.util.Map<java.lang.String, java.lang.Object>> categories, @org.jetbrains.annotations.Nullable()
    java.lang.String content, @org.jetbrains.annotations.Nullable()
    java.lang.String description, @org.jetbrains.annotations.Nullable()
    java.lang.String thumbnail) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.BlogEditResponse>> blogEdit(@org.jetbrains.annotations.Nullable()
    java.lang.String title, @org.jetbrains.annotations.Nullable()
    java.lang.String content, @org.jetbrains.annotations.Nullable()
    java.lang.String description, @org.jetbrains.annotations.NotNull()
    java.util.Map<java.lang.String, ? extends java.lang.Object> meta, @org.jetbrains.annotations.NotNull()
    java.util.ArrayList<java.util.Map<java.lang.String, java.lang.Object>> tags, @org.jetbrains.annotations.NotNull()
    java.util.ArrayList<java.util.Map<java.lang.String, java.lang.Object>> categories, @org.jetbrains.annotations.Nullable()
    java.lang.String thumbnail, @org.jetbrains.annotations.Nullable()
    java.lang.String id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.BlogDeleteResponse>> blogDelete(@org.jetbrains.annotations.Nullable()
    java.lang.String id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.BlogSingleResponse>> blogSingle(@org.jetbrains.annotations.Nullable()
    java.lang.String id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.BlogTagsResponse>> blogTags(@org.jetbrains.annotations.Nullable()
    java.lang.String name) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.BlogTagsUpdateResponse>> blogTagsUpdate(@org.jetbrains.annotations.Nullable()
    java.lang.String name) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.BlogTagsRetrieveResponse>> blogTagsRetrieve(int limit, int page, @org.jetbrains.annotations.Nullable()
    java.lang.String name) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.BlogTagsDeleteByIDResponse>> blogTagsDeleteByID(@org.jetbrains.annotations.Nullable()
    java.lang.String id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.CreateBlogCategoryResponse>> createBlogCategory(@org.jetbrains.annotations.Nullable()
    java.lang.String name, @org.jetbrains.annotations.Nullable()
    java.lang.String parentId) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.UpdateBlogCategoryResponse>> updateBlogCategory(@org.jetbrains.annotations.Nullable()
    java.lang.String name, @org.jetbrains.annotations.Nullable()
    java.lang.String parentId) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetBlogCategoryResponse>> getBlogCategory(int limit, int page, @org.jetbrains.annotations.Nullable()
    java.lang.String name) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetBlogSubcategoryResponse>> getBlogSubcategory(@org.jetbrains.annotations.Nullable()
    java.lang.String id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.DeleteBlogCategoryResponse>> deleteBlogCategory(@org.jetbrains.annotations.Nullable()
    java.lang.String id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.CaptchaTestResponse>> captchaTest(int width, int height) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.CaptchaGenerateResponse>> captchaGenerate(int width, int height) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GoogleCaptchaVerifyResponse>> googleCaptchaVerify(@org.jetbrains.annotations.NotNull()
    java.util.Map<java.lang.String, ? extends java.lang.Object> formdata, @org.jetbrains.annotations.Nullable()
    java.lang.String captchatoken) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.CreateCMSLambdaResponse>> createCMSLambda(@org.jetbrains.annotations.Nullable()
    java.lang.String page, @org.jetbrains.annotations.Nullable()
    java.lang.String key, @org.jetbrains.annotations.Nullable()
    java.lang.String type, @org.jetbrains.annotations.Nullable()
    java.lang.String value) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.UpdateCMSLambdaResponse>> updateCMSLambda(@org.jetbrains.annotations.Nullable()
    java.lang.String page, @org.jetbrains.annotations.Nullable()
    java.lang.String key, @org.jetbrains.annotations.Nullable()
    java.lang.String type, @org.jetbrains.annotations.Nullable()
    java.lang.String value, @org.jetbrains.annotations.Nullable()
    java.lang.String id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.DeleteCMSLambdaResponse>> deleteCMSLambda(@org.jetbrains.annotations.Nullable()
    java.lang.String id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetCMSByIDLambdaResponse>> getCMSByIDLambda(@org.jetbrains.annotations.Nullable()
    java.lang.String id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetCMSByPageAndKeyLambdaResponse>> getCMSByPageAndKeyLambda(@org.jetbrains.annotations.Nullable()
    java.lang.String page, @org.jetbrains.annotations.Nullable()
    java.lang.String key) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetCMSByPageLambdaResponse>> getCMSByPageLambda(@org.jetbrains.annotations.Nullable()
    java.lang.String page) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetAllCMSLambdaResponse>> getAllCMSLambda() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.RegisterLambdaResponse>> registerLambda(@org.jetbrains.annotations.Nullable()
    java.lang.String email, @org.jetbrains.annotations.Nullable()
    java.lang.String role, @org.jetbrains.annotations.Nullable()
    java.lang.Boolean verify, @org.jetbrains.annotations.Nullable()
    java.lang.Boolean isRefresh, @org.jetbrains.annotations.Nullable()
    java.lang.String password, @org.jetbrains.annotations.Nullable()
    java.lang.String firstName, @org.jetbrains.annotations.Nullable()
    java.lang.String lastName, @org.jetbrains.annotations.Nullable()
    java.lang.String photo, @org.jetbrains.annotations.Nullable()
    java.lang.String phone) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.LoginLambdaResponse>> loginLambda(@org.jetbrains.annotations.Nullable()
    java.lang.String email, @org.jetbrains.annotations.Nullable()
    java.lang.String password, @org.jetbrains.annotations.Nullable()
    java.lang.String role, @org.jetbrains.annotations.Nullable()
    java.lang.Boolean isRefresh) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.MarketingLoginLambdaResponse>> marketingLoginLambda(@org.jetbrains.annotations.Nullable()
    java.lang.String email, @org.jetbrains.annotations.Nullable()
    java.lang.String password, @org.jetbrains.annotations.Nullable()
    java.lang.String role, @org.jetbrains.annotations.Nullable()
    java.lang.Boolean isRefresh) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.ProfileResponse>> profile() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.ProfileUpdateResponse>> profileUpdate(@org.jetbrains.annotations.Nullable()
    java.lang.String firstName, @org.jetbrains.annotations.Nullable()
    java.lang.String lastName, @org.jetbrains.annotations.Nullable()
    java.lang.String photo) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.UploadImageLocalDefaultResponse>> uploadImageLocalDefault(@org.jetbrains.annotations.NotNull()
    okhttp3.MultipartBody.Part file) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.UploadImageS3Response>> uploadimages3(@org.jetbrains.annotations.NotNull()
    okhttp3.MultipartBody.Part file) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.PreferenceFetchResponse>> preferenceFetch() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.PreferenceUpdateResponse>> preferenceUpdate(@org.jetbrains.annotations.NotNull()
    java.util.Map<java.lang.String, ? extends java.lang.Object> payload) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetSowTreeResponse>> getSowTree(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String page) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.AppAlertsListResponse>> appAlertsList(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String page, @org.jetbrains.annotations.Nullable()
    java.lang.String filter) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.AppAlertsUpdateResponse>> appAlertsUpdate(@org.jetbrains.annotations.Nullable()
    java.lang.Integer isRead, @org.jetbrains.annotations.Nullable()
    java.lang.String id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.RetrieveProductDefaultResponse>> retrieveProductDefault(@org.jetbrains.annotations.Nullable()
    java.lang.Number page, @org.jetbrains.annotations.Nullable()
    java.lang.Number limit, @org.jetbrains.annotations.Nullable()
    java.lang.String sortid, @org.jetbrains.annotations.Nullable()
    java.lang.String direction) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.EcomProductByIDDefaultResponse>> ecomProductByIDDefault() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.AddEcomProductLambdaResponse>> addEcomProductLambda(@org.jetbrains.annotations.Nullable()
    java.lang.String slug, @org.jetbrains.annotations.Nullable()
    java.lang.String categoryId, @org.jetbrains.annotations.Nullable()
    java.lang.String type, @org.jetbrains.annotations.Nullable()
    java.lang.String quantity, @org.jetbrains.annotations.Nullable()
    java.lang.String data, @org.jetbrains.annotations.Nullable()
    java.lang.String name, @org.jetbrains.annotations.Nullable()
    java.lang.Boolean isTaxable, @org.jetbrains.annotations.Nullable()
    java.lang.Boolean isShipping, @org.jetbrains.annotations.Nullable()
    java.lang.Boolean isSticky, @org.jetbrains.annotations.Nullable()
    java.lang.Boolean isFeatured, @org.jetbrains.annotations.Nullable()
    java.lang.Boolean isDownloadable, @org.jetbrains.annotations.Nullable()
    java.lang.String downloadLimit, @org.jetbrains.annotations.Nullable()
    java.lang.Boolean isBackorder, @org.jetbrains.annotations.Nullable()
    java.lang.Boolean soldSingle, @org.jetbrains.annotations.Nullable()
    java.lang.Boolean manageStock, @org.jetbrains.annotations.Nullable()
    java.lang.String thumbnailImage, @org.jetbrains.annotations.Nullable()
    java.lang.Boolean featuredImage, @org.jetbrains.annotations.Nullable()
    java.lang.String image, @org.jetbrains.annotations.Nullable()
    java.lang.String sku, @org.jetbrains.annotations.Nullable()
    java.lang.String weight, @org.jetbrains.annotations.Nullable()
    java.lang.String height, @org.jetbrains.annotations.Nullable()
    java.lang.String length, @org.jetbrains.annotations.Nullable()
    java.lang.String weightUnit, @org.jetbrains.annotations.Nullable()
    java.lang.String heightUnit, @org.jetbrains.annotations.Nullable()
    java.lang.String lengthUnit, @org.jetbrains.annotations.Nullable()
    java.lang.String avgReview, @org.jetbrains.annotations.Nullable()
    java.lang.String salePrice, @org.jetbrains.annotations.Nullable()
    java.lang.String shippingPrice, @org.jetbrains.annotations.Nullable()
    java.lang.String regularPrice, @org.jetbrains.annotations.Nullable()
    java.lang.String position, @org.jetbrains.annotations.Nullable()
    java.lang.String downloadExpireAt, @org.jetbrains.annotations.Nullable()
    java.lang.String scheduleSaleAt, @org.jetbrains.annotations.Nullable()
    java.lang.String scheduleSaleEnd, @org.jetbrains.annotations.Nullable()
    java.lang.String description, @org.jetbrains.annotations.Nullable()
    java.lang.Boolean isVirtual) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.EditEcomProductLambdaResponse>> editEcomProductLambda(@org.jetbrains.annotations.NotNull()
    java.util.Map<java.lang.String, ? extends java.lang.Object> payload, @org.jetbrains.annotations.Nullable()
    java.lang.Number id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.DeleteEcomProductLambdaResponse>> deleteEcomProductLambda(@org.jetbrains.annotations.Nullable()
    java.lang.Number id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetCartItemsResponse>> getCartItems(@org.jetbrains.annotations.Nullable()
    java.lang.String userId) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.EcomAddCartResponse>> ecomAddCart(@org.jetbrains.annotations.Nullable()
    java.lang.String userId, @org.jetbrains.annotations.Nullable()
    java.lang.Integer productid, @org.jetbrains.annotations.Nullable()
    java.lang.Integer quantity) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.EcomDeleteCartItemResponse>> ecomDeleteCartItem(@org.jetbrains.annotations.Nullable()
    java.lang.String userId, @org.jetbrains.annotations.Nullable()
    java.lang.String data) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.EcomGetProductReviewResponse>> ecomGetProductReview(@org.jetbrains.annotations.Nullable()
    java.lang.Integer productid) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.EcomAddProductReviewResponse>> ecomAddProductReview(@org.jetbrains.annotations.Nullable()
    java.lang.String review, @org.jetbrains.annotations.Nullable()
    java.lang.Integer productid) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.ForgotPasswordResponse>> forgotPassword(@org.jetbrains.annotations.Nullable()
    java.lang.String email, @org.jetbrains.annotations.Nullable()
    java.lang.String role) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.ForgotPasswordMobileResponse>> forgotPasswordMobile(@org.jetbrains.annotations.Nullable()
    java.lang.String email) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.ResetPasswordResponse>> resetPassword(@org.jetbrains.annotations.Nullable()
    java.lang.String token, @org.jetbrains.annotations.Nullable()
    java.lang.String code, @org.jetbrains.annotations.Nullable()
    java.lang.String password) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.ResetPasswordMobileResponse>> resetPasswordMobile(@org.jetbrains.annotations.Nullable()
    java.lang.String code, @org.jetbrains.annotations.Nullable()
    java.lang.String password) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetStripeDataResponse>> getStripeData(@org.jetbrains.annotations.Nullable()
    java.lang.String userId, @org.jetbrains.annotations.Nullable()
    java.lang.String amount, @org.jetbrains.annotations.Nullable()
    java.lang.String currency) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetOneDefaultSquareFootCostResponse>> getOneDefaultSquareFootCost(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetOneSettingResponse>> getOneSetting(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetOneCostResponse>> getOneCost(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetOneRoomResponse>> getOneRoom(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetOneLaborResponse>> getOneLabor(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetOneLineItemEntryResponse>> getOneLineItemEntry(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetOneCompanySettingsResponse>> getOneCompanySettings(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetOneCmsResponse>> getOneCms(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetOneTeamMemberResponse>> getOneTeamMember(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetOneDefaultMaterialResponse>> getOneDefaultMaterial(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetOneProjectResponse>> getOneProject(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetOneUserResponse>> getOneUser(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetOneProfileResponse>> getOneProfile(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetOneLinealFootCostResponse>> getOneLinealFootCost(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetOneCustomerResponse>> getOneCustomer(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetOnePermissionResponse>> getOnePermission(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetOneTokenResponse>> getOneToken(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetOneSqftCostsResponse>> getOneSqftCosts(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetOneEmailResponse>> getOneEmail(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetOneAlertsResponse>> getOneAlerts(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetOneDrawsResponse>> getOneDraws(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetOneChatResponse>> getOneChat(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetOneMaterialResponse>> getOneMaterial(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetOneInvoiceResponse>> getOneInvoice(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetOneDefaultLinealFootCostResponse>> getOneDefaultLinealFootCost(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetOneTriggerTypeResponse>> getOneTriggerType(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetOneJobResponse>> getOneJob(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetOneLineItemsResponse>> getOneLineItems(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetOnePhotoResponse>> getOnePhoto(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetOneApiKeysResponse>> getOneApiKeys(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetOneChangeOrderDescriptionResponse>> getOneChangeOrderDescription(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetOneAnalyticLogResponse>> getOneAnalyticLog(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetOnePostsResponse>> getOnePosts(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetOneEmployeeResponse>> getOneEmployee(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetDefaultSquareFootCostListResponse>> getDefaultSquareFootCostList(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String size, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetSettingListResponse>> getSettingList(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String size, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetCostListResponse>> getCostList(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String size, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetRoomListResponse>> getRoomList(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String size, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetLaborListResponse>> getLaborList(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String size, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetLineItemEntryListResponse>> getLineItemEntryList(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String size, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetCompanySettingsListResponse>> getCompanySettingsList(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String size, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetCmsListResponse>> getCmsList(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String size, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetTeamMemberListResponse>> getTeamMemberList(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String size, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetProjectListResponse>> getProjectList(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String size, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetUserListResponse>> getUserList(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String size, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetProfileListResponse>> getProfileList(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String size, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetLinealFootCostListResponse>> getLinealFootCostList(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String size, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetCustomerListResponse>> getCustomerList(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String size, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetPermissionListResponse>> getPermissionList(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String size, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetTokenListResponse>> getTokenList(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String size, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetSqftCostsListResponse>> getSqftCostsList(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String size, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetEmailListResponse>> getEmailList(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String size, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetAlertsListResponse>> getAlertsList(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String size, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetDrawsListResponse>> getDrawsList(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String size, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetChatListResponse>> getChatList(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String size, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetMaterialListResponse>> getMaterialList(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String size, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetInvoiceListResponse>> getInvoiceList(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String size, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetDefaultLinealFootCostListResponse>> getDefaultLinealFootCostList(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String size, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetTriggerTypeListResponse>> getTriggerTypeList(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String size, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetJobListResponse>> getJobList(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String size, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetLineItemsListResponse>> getLineItemsList(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String size, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetPhotoListResponse>> getPhotoList(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String size, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetApiKeysListResponse>> getApiKeysList(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String size, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetChangeOrderDescriptionListResponse>> getChangeOrderDescriptionList(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String size, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetAnalyticLogListResponse>> getAnalyticLogList(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String size, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetPostsListResponse>> getPostsList(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String size, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetEmployeeListResponse>> getEmployeeList(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String size, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetDefaultSquareFootCostPaginatedResponse>> getDefaultSquareFootCostPaginated(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String page, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetSettingPaginatedResponse>> getSettingPaginated(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String page, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetCostPaginatedResponse>> getCostPaginated(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String page, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetRoomPaginatedResponse>> getRoomPaginated(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String page, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetLaborPaginatedResponse>> getLaborPaginated(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String page, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetLineItemEntryPaginatedResponse>> getLineItemEntryPaginated(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String page, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetCompanySettingsPaginatedResponse>> getCompanySettingsPaginated(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String page, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetCmsPaginatedResponse>> getCmsPaginated(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String page, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetTeamMemberPaginatedResponse>> getTeamMemberPaginated(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String page, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetDefaultMaterialPaginatedResponse>> getDefaultMaterialPaginated(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String page, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetProjectPaginatedResponse>> getProjectPaginated(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String page, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetUserPaginatedResponse>> getUserPaginated(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String page, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetProfilePaginatedResponse>> getProfilePaginated(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String page, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetLinealFootCostPaginatedResponse>> getLinealFootCostPaginated(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String page, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetCustomerPaginatedResponse>> getCustomerPaginated(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String page, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetPermissionPaginatedResponse>> getPermissionPaginated(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String page, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetTokenPaginatedResponse>> getTokenPaginated(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String page, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetSqftCostsPaginatedResponse>> getSqftCostsPaginated(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String page, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetEmailPaginatedResponse>> getEmailPaginated(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String page, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetAlertsPaginatedResponse>> getAlertsPaginated(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String page, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetDrawsPaginatedResponse>> getDrawsPaginated(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String page, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetChatPaginatedResponse>> getChatPaginated(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String page, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetMaterialPaginatedResponse>> getMaterialPaginated(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String page, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetInvoicePaginatedResponse>> getInvoicePaginated(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String page, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetDefaultLinealFootCostPaginatedResponse>> getDefaultLinealFootCostPaginated(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String page, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetTriggerTypePaginatedResponse>> getTriggerTypePaginated(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String page, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetJobPaginatedResponse>> getJobPaginated(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String page, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetLineItemsPaginatedResponse>> getLineItemsPaginated(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String page, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetPhotoPaginatedResponse>> getPhotoPaginated(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String page, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetApiKeysPaginatedResponse>> getApiKeysPaginated(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String page, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetChangeOrderDescriptionPaginatedResponse>> getChangeOrderDescriptionPaginated(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String page, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetAnalyticLogPaginatedResponse>> getAnalyticLogPaginated(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String page, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetPostsPaginatedResponse>> getPostsPaginated(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String page, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetEmployeePaginatedResponse>> getEmployeePaginated(@org.jetbrains.annotations.Nullable()
    java.lang.String order, @org.jetbrains.annotations.Nullable()
    java.lang.String page, @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.Nullable()
    java.lang.String join) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.CreateDefaultSquareFootCostResponse>> createDefaultSquareFootCost(@org.jetbrains.annotations.Nullable()
    java.lang.String name, @org.jetbrains.annotations.Nullable()
    java.lang.String cost, @org.jetbrains.annotations.Nullable()
    java.lang.String laborCost, @org.jetbrains.annotations.Nullable()
    java.lang.Integer userId, @org.jetbrains.annotations.Nullable()
    java.lang.Integer hidden) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.CreateSettingResponse>> createSetting(@org.jetbrains.annotations.Nullable()
    java.lang.String settingKey, @org.jetbrains.annotations.Nullable()
    java.lang.String settingValue) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.CreateCostResponse>> createCost(@org.jetbrains.annotations.Nullable()
    java.lang.String costType, @org.jetbrains.annotations.Nullable()
    java.lang.String name, @org.jetbrains.annotations.Nullable()
    java.lang.Integer unitCost, @org.jetbrains.annotations.Nullable()
    java.lang.Integer linealFootCost, @org.jetbrains.annotations.Nullable()
    java.lang.Integer laborCost, @org.jetbrains.annotations.Nullable()
    java.lang.Integer materialCost, @org.jetbrains.annotations.Nullable()
    java.lang.Integer profitOverhead) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.CreateRoomResponse>> createRoom(@org.jetbrains.annotations.Nullable()
    java.lang.Integer userId, @org.jetbrains.annotations.Nullable()
    java.lang.Integer otherUserId, @org.jetbrains.annotations.Nullable()
    java.lang.Integer chatId, @org.jetbrains.annotations.Nullable()
    java.lang.Integer unread, @org.jetbrains.annotations.Nullable()
    java.lang.String userUpdateAt, @org.jetbrains.annotations.Nullable()
    java.lang.String otherUserUpdateAt) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.CreateLaborResponse>> createLabor(@org.jetbrains.annotations.Nullable()
    java.lang.String name, @org.jetbrains.annotations.Nullable()
    java.lang.String hours, @org.jetbrains.annotations.Nullable()
    java.lang.Integer amount, @org.jetbrains.annotations.Nullable()
    java.lang.Integer perHour) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.CreateLineItemEntryResponse>> createLineItemEntry(@org.jetbrains.annotations.Nullable()
    java.lang.Integer parentId, @org.jetbrains.annotations.Nullable()
    java.lang.String name, @org.jetbrains.annotations.Nullable()
    java.lang.String cost, @org.jetbrains.annotations.Nullable()
    java.lang.String laborCost, @org.jetbrains.annotations.Nullable()
    java.lang.Integer lineItemId, @org.jetbrains.annotations.Nullable()
    java.lang.String quantity) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.CreateCompanySettingsResponse>> createCompanySettings(@org.jetbrains.annotations.Nullable()
    java.lang.String defaultHourlyRate, @org.jetbrains.annotations.Nullable()
    java.lang.String defaultProfitOverhead, @org.jetbrains.annotations.Nullable()
    java.lang.Integer userId) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.CreateCmsResponse>> createCms(@org.jetbrains.annotations.Nullable()
    java.lang.String page, @org.jetbrains.annotations.Nullable()
    java.lang.String contentKey, @org.jetbrains.annotations.Nullable()
    java.lang.String contentType, @org.jetbrains.annotations.Nullable()
    java.lang.String contentValue) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.CreateTeamMemberResponse>> createTeamMember(@org.jetbrains.annotations.Nullable()
    java.lang.Integer projectId, @org.jetbrains.annotations.Nullable()
    java.lang.Integer userId, @org.jetbrains.annotations.Nullable()
    java.lang.String name, @org.jetbrains.annotations.Nullable()
    java.lang.Integer hourlyRate, @org.jetbrains.annotations.Nullable()
    java.lang.Integer isDefault) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> createDraw(@org.jetbrains.annotations.Nullable()
    java.lang.Integer projectId, @org.jetbrains.annotations.Nullable()
    java.lang.String amount, @org.jetbrains.annotations.Nullable()
    java.lang.String description, @org.jetbrains.annotations.Nullable()
    java.lang.String check_no, @org.jetbrains.annotations.Nullable()
    java.lang.Integer status) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.CreateProjectResponse>> createProject(@org.jetbrains.annotations.Nullable()
    java.lang.Integer changeCount, @org.jetbrains.annotations.Nullable()
    java.lang.Integer customerId, @org.jetbrains.annotations.Nullable()
    java.lang.Integer userId, @org.jetbrains.annotations.Nullable()
    java.lang.Integer status, @org.jetbrains.annotations.Nullable()
    java.lang.String profitOverhead, @org.jetbrains.annotations.Nullable()
    java.lang.String hourlyRate) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.CreateUserResponse>> createUser(@org.jetbrains.annotations.Nullable()
    java.lang.String oauth, @org.jetbrains.annotations.Nullable()
    java.lang.String role, @org.jetbrains.annotations.Nullable()
    java.lang.String firstName, @org.jetbrains.annotations.Nullable()
    java.lang.String lastName, @org.jetbrains.annotations.Nullable()
    java.lang.String email, @org.jetbrains.annotations.Nullable()
    java.lang.String password, @org.jetbrains.annotations.Nullable()
    java.lang.Integer type, @org.jetbrains.annotations.Nullable()
    java.lang.Integer verify, @org.jetbrains.annotations.Nullable()
    java.lang.String phone, @org.jetbrains.annotations.Nullable()
    java.lang.String companyName, @org.jetbrains.annotations.Nullable()
    java.lang.String photo, @org.jetbrains.annotations.Nullable()
    java.lang.String refer, @org.jetbrains.annotations.Nullable()
    java.lang.String stripeUid, @org.jetbrains.annotations.Nullable()
    java.lang.String paypalUid, @org.jetbrains.annotations.Nullable()
    java.lang.Integer twoFactorAuthentication, @org.jetbrains.annotations.Nullable()
    java.lang.Integer status) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.CreateProfileResponse>> createProfile(@org.jetbrains.annotations.Nullable()
    java.lang.Integer userId, @org.jetbrains.annotations.Nullable()
    java.lang.String fcmToken, @org.jetbrains.annotations.Nullable()
    java.lang.String deviceId, @org.jetbrains.annotations.Nullable()
    java.lang.String deviceType) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.CreateLinealFootCostResponse>> createLinealFootCost(@org.jetbrains.annotations.Nullable()
    java.lang.String name, @org.jetbrains.annotations.Nullable()
    java.lang.Integer linealFootCost, @org.jetbrains.annotations.Nullable()
    java.lang.Integer profitOverhead, @org.jetbrains.annotations.Nullable()
    java.lang.Integer laborCost, @org.jetbrains.annotations.Nullable()
    java.lang.Integer materialCost) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.CreatePermissionResponse>> createPermission(@org.jetbrains.annotations.Nullable()
    java.lang.String role, @org.jetbrains.annotations.Nullable()
    java.lang.String permission) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.CreateTokenResponse>> createToken(@org.jetbrains.annotations.Nullable()
    java.lang.String token, @org.jetbrains.annotations.Nullable()
    java.lang.Integer type, @org.jetbrains.annotations.Nullable()
    java.lang.String data, @org.jetbrains.annotations.Nullable()
    java.lang.Integer userId, @org.jetbrains.annotations.Nullable()
    java.lang.Integer status, @org.jetbrains.annotations.Nullable()
    java.lang.String expireAt) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.CreateSqftCostsResponse>> createSqftCosts(@org.jetbrains.annotations.Nullable()
    java.lang.String name, @org.jetbrains.annotations.Nullable()
    java.lang.Integer linealFootCost, @org.jetbrains.annotations.Nullable()
    java.lang.Integer profitOverhead, @org.jetbrains.annotations.Nullable()
    java.lang.Integer laborCost, @org.jetbrains.annotations.Nullable()
    java.lang.Integer materialCost) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.CreateEmailResponse>> createEmail(@org.jetbrains.annotations.Nullable()
    java.lang.String slug, @org.jetbrains.annotations.Nullable()
    java.lang.String subject, @org.jetbrains.annotations.Nullable()
    java.lang.String tag, @org.jetbrains.annotations.Nullable()
    java.lang.String html) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.CreateAlertsResponse>> createAlerts(@org.jetbrains.annotations.Nullable()
    java.lang.Integer userId, @org.jetbrains.annotations.Nullable()
    java.lang.String message, @org.jetbrains.annotations.Nullable()
    java.lang.String image, @org.jetbrains.annotations.Nullable()
    java.lang.Integer isRead) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.CreateChatResponse>> createChat(@org.jetbrains.annotations.Nullable()
    java.lang.Integer roomId, @org.jetbrains.annotations.Nullable()
    java.lang.Integer unread, @org.jetbrains.annotations.Nullable()
    java.lang.String chat) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.CreateMaterialResponse>> createMaterial(@org.jetbrains.annotations.Nullable()
    java.lang.String name, @org.jetbrains.annotations.Nullable()
    java.lang.Integer unitCost, @org.jetbrains.annotations.Nullable()
    java.lang.String projectId) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.CreateInvoiceResponse>> createInvoice(@org.jetbrains.annotations.Nullable()
    java.lang.String companyName, @org.jetbrains.annotations.Nullable()
    java.lang.String name, @org.jetbrains.annotations.Nullable()
    java.lang.String email, @org.jetbrains.annotations.Nullable()
    java.lang.String address, @org.jetbrains.annotations.Nullable()
    java.lang.String milestoneDescription, @org.jetbrains.annotations.Nullable()
    java.lang.String totalAmountDue) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.CreateDefaultLinealFootCostResponse>> createDefaultLinealFootCost(@org.jetbrains.annotations.Nullable()
    java.lang.String name, @org.jetbrains.annotations.Nullable()
    java.lang.String cost, @org.jetbrains.annotations.Nullable()
    java.lang.String laborCost, @org.jetbrains.annotations.Nullable()
    java.lang.Integer userId, @org.jetbrains.annotations.Nullable()
    java.lang.Integer hidden) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.CreateTriggerTypeResponse>> createTriggerType(@org.jetbrains.annotations.Nullable()
    java.lang.String name) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.CreateJobResponse>> createJob(@org.jetbrains.annotations.Nullable()
    java.lang.String task, @org.jetbrains.annotations.Nullable()
    java.lang.String arguments, @org.jetbrains.annotations.Nullable()
    java.lang.String errorLog, @org.jetbrains.annotations.Nullable()
    java.lang.String identifier, @org.jetbrains.annotations.Nullable()
    java.lang.Integer retries, @org.jetbrains.annotations.Nullable()
    java.lang.Integer retryCount, @org.jetbrains.annotations.Nullable()
    java.lang.String timeInterval, @org.jetbrains.annotations.Nullable()
    java.lang.String lastRun, @org.jetbrains.annotations.Nullable()
    java.lang.String status) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.CreateLineItemsResponse>> createLineItems(@org.jetbrains.annotations.Nullable()
    java.lang.Integer hidden, @org.jetbrains.annotations.Nullable()
    java.lang.Integer projectId, @org.jetbrains.annotations.Nullable()
    java.lang.String description, @org.jetbrains.annotations.Nullable()
    java.lang.String estimatedBy, @org.jetbrains.annotations.Nullable()
    java.lang.String laborHours) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.CreatePhotoResponse>> createPhoto(@org.jetbrains.annotations.Nullable()
    java.lang.String url, @org.jetbrains.annotations.Nullable()
    java.lang.String caption, @org.jetbrains.annotations.Nullable()
    java.lang.Integer userId, @org.jetbrains.annotations.Nullable()
    java.lang.Integer width, @org.jetbrains.annotations.Nullable()
    java.lang.Integer height, @org.jetbrains.annotations.Nullable()
    java.lang.Integer type) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.CreateApiKeysResponse>> createApiKeys(@org.jetbrains.annotations.Nullable()
    java.lang.String key, @org.jetbrains.annotations.Nullable()
    java.lang.String value, @org.jetbrains.annotations.Nullable()
    java.lang.String description) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.CreateChangeOrderDescriptionResponse>> createChangeOrderDescription(@org.jetbrains.annotations.Nullable()
    java.lang.String description, @org.jetbrains.annotations.Nullable()
    java.lang.Integer lineItemId) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.CreateAnalyticLogResponse>> createAnalyticLog(@org.jetbrains.annotations.Nullable()
    java.lang.Integer userId, @org.jetbrains.annotations.Nullable()
    java.lang.String url, @org.jetbrains.annotations.Nullable()
    java.lang.String path, @org.jetbrains.annotations.Nullable()
    java.lang.String hostname, @org.jetbrains.annotations.Nullable()
    java.lang.String ip, @org.jetbrains.annotations.Nullable()
    java.lang.String role, @org.jetbrains.annotations.Nullable()
    java.lang.String browser, @org.jetbrains.annotations.Nullable()
    java.lang.String country) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.CreatePostsResponse>> createPosts(@org.jetbrains.annotations.Nullable()
    java.lang.Integer status, @org.jetbrains.annotations.Nullable()
    java.lang.String type, @org.jetbrains.annotations.Nullable()
    java.lang.String data, @org.jetbrains.annotations.Nullable()
    java.lang.String links) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.CreateEmployeeResponse>> createEmployee(@org.jetbrains.annotations.Nullable()
    java.lang.Integer userId, @org.jetbrains.annotations.Nullable()
    java.lang.Integer defaultHourlyRate, @org.jetbrains.annotations.Nullable()
    java.lang.Integer defaultProfitOverhead) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.UpdateDefaultSquareFootCostResponse>> updateDefaultSquareFootCost(@org.jetbrains.annotations.Nullable()
    java.lang.String name, @org.jetbrains.annotations.Nullable()
    java.lang.String cost, @org.jetbrains.annotations.Nullable()
    java.lang.String laborCost, @org.jetbrains.annotations.Nullable()
    java.lang.Integer userId, @org.jetbrains.annotations.Nullable()
    java.lang.Integer hidden, @org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.UpdateSettingResponse>> updateSetting(@org.jetbrains.annotations.Nullable()
    java.lang.String settingKey, @org.jetbrains.annotations.Nullable()
    java.lang.String settingValue, @org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.UpdateCostResponse>> updateCost(@org.jetbrains.annotations.Nullable()
    java.lang.String costType, @org.jetbrains.annotations.Nullable()
    java.lang.String name, @org.jetbrains.annotations.Nullable()
    java.lang.Integer unitCost, @org.jetbrains.annotations.Nullable()
    java.lang.Integer linealFootCost, @org.jetbrains.annotations.Nullable()
    java.lang.Integer laborCost, @org.jetbrains.annotations.Nullable()
    java.lang.Integer materialCost, @org.jetbrains.annotations.Nullable()
    java.lang.Integer profitOverhead, @org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.UpdateRoomResponse>> updateRoom(@org.jetbrains.annotations.Nullable()
    java.lang.Integer userId, @org.jetbrains.annotations.Nullable()
    java.lang.Integer otherUserId, @org.jetbrains.annotations.Nullable()
    java.lang.Integer chatId, @org.jetbrains.annotations.Nullable()
    java.lang.Integer unread, @org.jetbrains.annotations.Nullable()
    java.lang.String userUpdateAt, @org.jetbrains.annotations.Nullable()
    java.lang.String otherUserUpdateAt, @org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.UpdateLaborResponse>> updateLabor(@org.jetbrains.annotations.Nullable()
    java.lang.String name, @org.jetbrains.annotations.Nullable()
    java.lang.String hours, @org.jetbrains.annotations.Nullable()
    java.lang.Integer amount, @org.jetbrains.annotations.Nullable()
    java.lang.Integer perHour, @org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.UpdateLineItemEntryResponse>> updateLineItemEntry(@org.jetbrains.annotations.Nullable()
    java.lang.Integer parentId, @org.jetbrains.annotations.Nullable()
    java.lang.String name, @org.jetbrains.annotations.Nullable()
    java.lang.String cost, @org.jetbrains.annotations.Nullable()
    java.lang.String laborCost, @org.jetbrains.annotations.Nullable()
    java.lang.Integer lineItemId, @org.jetbrains.annotations.Nullable()
    java.lang.String quantity, @org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.UpdateCompanySettingsResponse>> updateCompanySettings(@org.jetbrains.annotations.Nullable()
    java.lang.String defaultHourlyRate, @org.jetbrains.annotations.Nullable()
    java.lang.String defaultProfitOverhead, @org.jetbrains.annotations.Nullable()
    java.lang.Integer userId, @org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.UpdateCmsResponse>> updateCms(@org.jetbrains.annotations.Nullable()
    java.lang.String page, @org.jetbrains.annotations.Nullable()
    java.lang.String contentKey, @org.jetbrains.annotations.Nullable()
    java.lang.String contentType, @org.jetbrains.annotations.Nullable()
    java.lang.String contentValue, @org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.UpdateTeamMemberResponse>> updateTeamMember(@org.jetbrains.annotations.Nullable()
    java.lang.Integer projectId, @org.jetbrains.annotations.Nullable()
    java.lang.Integer userId, @org.jetbrains.annotations.Nullable()
    java.lang.String name, @org.jetbrains.annotations.Nullable()
    java.lang.Integer hourlyRate, @org.jetbrains.annotations.Nullable()
    java.lang.Integer isDefault, @org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.UpdateDefaultMaterialResponse>> updateDefaultMaterial(@org.jetbrains.annotations.Nullable()
    java.lang.String userId, @org.jetbrains.annotations.Nullable()
    java.lang.String cost, @org.jetbrains.annotations.Nullable()
    java.lang.String name, @org.jetbrains.annotations.Nullable()
    java.lang.Integer hidden, @org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> updateLinealFootCost(int cost, @org.jetbrains.annotations.Nullable()
    java.lang.String name, @org.jetbrains.annotations.Nullable()
    java.lang.Integer hidden, @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, int laborCost, @org.jetbrains.annotations.Nullable()
    java.lang.Integer is_default) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> updateSquareFootCost(int cost, @org.jetbrains.annotations.Nullable()
    java.lang.String name, @org.jetbrains.annotations.Nullable()
    java.lang.Integer hidden, @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, int laborCost, @org.jetbrains.annotations.Nullable()
    java.lang.Integer is_default) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.UpdateProjectResponse>> updateProject(@org.jetbrains.annotations.Nullable()
    java.lang.Integer changeCount, @org.jetbrains.annotations.Nullable()
    java.lang.Integer customerId, @org.jetbrains.annotations.Nullable()
    java.lang.Integer userId, @org.jetbrains.annotations.Nullable()
    java.lang.Integer status, @org.jetbrains.annotations.Nullable()
    java.lang.String profitOverhead, @org.jetbrains.annotations.Nullable()
    java.lang.String hourlyRate, @org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.UpdateUserResponse>> updateUser(@org.jetbrains.annotations.Nullable()
    java.lang.String oauth, @org.jetbrains.annotations.Nullable()
    java.lang.String role, @org.jetbrains.annotations.Nullable()
    java.lang.String firstName, @org.jetbrains.annotations.Nullable()
    java.lang.String lastName, @org.jetbrains.annotations.Nullable()
    java.lang.String email, @org.jetbrains.annotations.Nullable()
    java.lang.String password, @org.jetbrains.annotations.Nullable()
    java.lang.Integer type, @org.jetbrains.annotations.Nullable()
    java.lang.Integer verify, @org.jetbrains.annotations.Nullable()
    java.lang.String phone, @org.jetbrains.annotations.Nullable()
    java.lang.String companyName, @org.jetbrains.annotations.Nullable()
    java.lang.String photo, @org.jetbrains.annotations.Nullable()
    java.lang.String refer, @org.jetbrains.annotations.Nullable()
    java.lang.String stripeUid, @org.jetbrains.annotations.Nullable()
    java.lang.String paypalUid, @org.jetbrains.annotations.Nullable()
    java.lang.Integer twoFactorAuthentication, @org.jetbrains.annotations.Nullable()
    java.lang.Integer status, @org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.UpdateProfileResponse>> updateProfile(@org.jetbrains.annotations.Nullable()
    java.lang.Integer userId, @org.jetbrains.annotations.Nullable()
    java.lang.String fcmToken, @org.jetbrains.annotations.Nullable()
    java.lang.String deviceId, @org.jetbrains.annotations.Nullable()
    java.lang.String deviceType, @org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.UpdateLinealFootCostResponse>> updateLinealFootCost(@org.jetbrains.annotations.Nullable()
    java.lang.String name, @org.jetbrains.annotations.Nullable()
    java.lang.Integer linealFootCost, @org.jetbrains.annotations.Nullable()
    java.lang.Integer profitOverhead, @org.jetbrains.annotations.Nullable()
    java.lang.Integer laborCost, @org.jetbrains.annotations.Nullable()
    java.lang.Integer materialCost, @org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.UpdatePermissionResponse>> updatePermission(@org.jetbrains.annotations.Nullable()
    java.lang.String role, @org.jetbrains.annotations.Nullable()
    java.lang.String permission, @org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.UpdateTokenResponse>> updateToken(@org.jetbrains.annotations.Nullable()
    java.lang.String token, @org.jetbrains.annotations.Nullable()
    java.lang.Integer type, @org.jetbrains.annotations.Nullable()
    java.lang.String data, @org.jetbrains.annotations.Nullable()
    java.lang.Integer userId, @org.jetbrains.annotations.Nullable()
    java.lang.Integer status, @org.jetbrains.annotations.Nullable()
    java.lang.String expireAt, @org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.UpdateSqftCostsResponse>> updateSqftCosts(@org.jetbrains.annotations.Nullable()
    java.lang.String name, @org.jetbrains.annotations.Nullable()
    java.lang.Integer linealFootCost, @org.jetbrains.annotations.Nullable()
    java.lang.Integer profitOverhead, @org.jetbrains.annotations.Nullable()
    java.lang.Integer laborCost, @org.jetbrains.annotations.Nullable()
    java.lang.Integer materialCost, @org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.UpdateEmailResponse>> updateEmail(@org.jetbrains.annotations.Nullable()
    java.lang.String slug, @org.jetbrains.annotations.Nullable()
    java.lang.String subject, @org.jetbrains.annotations.Nullable()
    java.lang.String tag, @org.jetbrains.annotations.Nullable()
    java.lang.String html, @org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.UpdateAlertsResponse>> updateAlerts(@org.jetbrains.annotations.Nullable()
    java.lang.Integer userId, @org.jetbrains.annotations.Nullable()
    java.lang.String message, @org.jetbrains.annotations.Nullable()
    java.lang.String image, @org.jetbrains.annotations.Nullable()
    java.lang.Integer isRead, @org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.UpdateDrawsResponse>> updateDraws(@org.jetbrains.annotations.Nullable()
    java.lang.String checkNo, @org.jetbrains.annotations.Nullable()
    java.lang.String amount, @org.jetbrains.annotations.Nullable()
    java.lang.String percentage, @org.jetbrains.annotations.Nullable()
    java.lang.String description, @org.jetbrains.annotations.Nullable()
    java.lang.Integer status, @org.jetbrains.annotations.Nullable()
    java.lang.Integer projectId, @org.jetbrains.annotations.Nullable()
    java.lang.String paymentType, @org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.UpdateChatResponse>> updateChat(@org.jetbrains.annotations.Nullable()
    java.lang.Integer roomId, @org.jetbrains.annotations.Nullable()
    java.lang.Integer unread, @org.jetbrains.annotations.Nullable()
    java.lang.String chat, @org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.UpdateMaterialResponse>> updateMaterial(@org.jetbrains.annotations.Nullable()
    java.lang.String name, @org.jetbrains.annotations.Nullable()
    java.lang.Integer unitCost, @org.jetbrains.annotations.Nullable()
    java.lang.String projectId, @org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.UpdateInvoiceResponse>> updateInvoice(@org.jetbrains.annotations.Nullable()
    java.lang.String companyName, @org.jetbrains.annotations.Nullable()
    java.lang.String name, @org.jetbrains.annotations.Nullable()
    java.lang.String email, @org.jetbrains.annotations.Nullable()
    java.lang.String address, @org.jetbrains.annotations.Nullable()
    java.lang.String milestoneDescription, @org.jetbrains.annotations.Nullable()
    java.lang.String totalAmountDue, @org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.UpdateDefaultLinealFootCostResponse>> updateDefaultLinealFootCost(@org.jetbrains.annotations.Nullable()
    java.lang.String name, @org.jetbrains.annotations.Nullable()
    java.lang.String cost, @org.jetbrains.annotations.Nullable()
    java.lang.String laborCost, @org.jetbrains.annotations.Nullable()
    java.lang.Integer userId, @org.jetbrains.annotations.Nullable()
    java.lang.Integer hidden, @org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.UpdateTriggerTypeResponse>> updateTriggerType(@org.jetbrains.annotations.Nullable()
    java.lang.String name, @org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.UpdateJobResponse>> updateJob(@org.jetbrains.annotations.Nullable()
    java.lang.String task, @org.jetbrains.annotations.Nullable()
    java.lang.String arguments, @org.jetbrains.annotations.Nullable()
    java.lang.String errorLog, @org.jetbrains.annotations.Nullable()
    java.lang.String identifier, @org.jetbrains.annotations.Nullable()
    java.lang.Integer retries, @org.jetbrains.annotations.Nullable()
    java.lang.Integer retryCount, @org.jetbrains.annotations.Nullable()
    java.lang.String timeInterval, @org.jetbrains.annotations.Nullable()
    java.lang.String lastRun, @org.jetbrains.annotations.Nullable()
    java.lang.String status, @org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.UpdateLineItemsResponse>> updateLineItems(@org.jetbrains.annotations.Nullable()
    java.lang.Integer hidden, @org.jetbrains.annotations.Nullable()
    java.lang.Integer projectId, @org.jetbrains.annotations.Nullable()
    java.lang.String description, @org.jetbrains.annotations.Nullable()
    java.lang.String estimatedBy, @org.jetbrains.annotations.Nullable()
    java.lang.String laborHours, @org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.UpdatePhotoResponse>> updatePhoto(@org.jetbrains.annotations.Nullable()
    java.lang.String url, @org.jetbrains.annotations.Nullable()
    java.lang.String caption, @org.jetbrains.annotations.Nullable()
    java.lang.Integer userId, @org.jetbrains.annotations.Nullable()
    java.lang.Integer width, @org.jetbrains.annotations.Nullable()
    java.lang.Integer height, @org.jetbrains.annotations.Nullable()
    java.lang.Integer type, @org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.UpdateApiKeysResponse>> updateApiKeys(@org.jetbrains.annotations.Nullable()
    java.lang.String key, @org.jetbrains.annotations.Nullable()
    java.lang.String value, @org.jetbrains.annotations.Nullable()
    java.lang.String description, @org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.UpdateChangeOrderDescriptionResponse>> updateChangeOrderDescription(@org.jetbrains.annotations.Nullable()
    java.lang.String description, @org.jetbrains.annotations.Nullable()
    java.lang.Integer lineItemId, @org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.UpdateAnalyticLogResponse>> updateAnalyticLog(@org.jetbrains.annotations.Nullable()
    java.lang.Integer userId, @org.jetbrains.annotations.Nullable()
    java.lang.String url, @org.jetbrains.annotations.Nullable()
    java.lang.String path, @org.jetbrains.annotations.Nullable()
    java.lang.String hostname, @org.jetbrains.annotations.Nullable()
    java.lang.String ip, @org.jetbrains.annotations.Nullable()
    java.lang.String role, @org.jetbrains.annotations.Nullable()
    java.lang.String browser, @org.jetbrains.annotations.Nullable()
    java.lang.String country, @org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.UpdatePostsResponse>> updatePosts(@org.jetbrains.annotations.Nullable()
    java.lang.Integer status, @org.jetbrains.annotations.Nullable()
    java.lang.String type, @org.jetbrains.annotations.Nullable()
    java.lang.String data, @org.jetbrains.annotations.Nullable()
    java.lang.String links, @org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.UpdateEmployeeResponse>> updateEmployee(@org.jetbrains.annotations.Nullable()
    java.lang.Integer userId, @org.jetbrains.annotations.Nullable()
    java.lang.Integer defaultHourlyRate, @org.jetbrains.annotations.Nullable()
    java.lang.Integer defaultProfitOverhead, @org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.DeleteDefaultSquareFootCostResponse>> deleteDefaultSquareFootCost(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.DeleteSettingResponse>> deleteSetting(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.DeleteCostResponse>> deleteCost(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.DeleteRoomResponse>> deleteRoom(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.DeleteLaborResponse>> deleteLabor(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.DeleteLineItemEntryResponse>> deleteLineItemEntry(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.DeleteCompanySettingsResponse>> deleteCompanySettings(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.DeleteCmsResponse>> deleteCms(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.DeleteTeamMemberResponse>> deleteTeamMember(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.DeleteDefaultMaterialResponse>> deleteDefaultMaterial(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> deleteLinealFootCosts(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>> deleteSquareFootCost(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.DeleteProjectResponse>> deleteProject(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.DeleteUserResponse>> deleteUser(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.DeleteProfileResponse>> deleteProfile(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.DeleteLinealFootCostResponse>> deleteLinealFootCost(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.DeleteCustomerResponse>> deleteCustomer(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.DeletePermissionResponse>> deletePermission(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.DeleteTokenResponse>> deleteToken(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.DeleteSqftCostsResponse>> deleteSqftCosts(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.DeleteEmailResponse>> deleteEmail(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.DeleteAlertsResponse>> deleteAlerts(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.DeleteChatResponse>> deleteChat(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.DeleteMaterialResponse>> deleteMaterial(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.DeleteInvoiceResponse>> deleteInvoice(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.DeleteDefaultLinealFootCostResponse>> deleteDefaultLinealFootCost(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.DeleteTriggerTypeResponse>> deleteTriggerType(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.DeleteJobResponse>> deleteJob(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.DeletePhotoResponse>> deletePhoto(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.DeleteApiKeysResponse>> deleteApiKeys(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.DeleteChangeOrderDescriptionResponse>> deleteChangeOrderDescription(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.DeleteAnalyticLogResponse>> deleteAnalyticLog(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.DeletePostsResponse>> deletePosts(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.DeleteEmployeeResponse>> deleteEmployee(@org.jetbrains.annotations.Nullable()
    java.lang.Integer id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetUserSubscriptionsResponse>> getUserSubscriptionsResource() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetPlansResponse>> getPlansResource() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetPaymentHistoryResponse>> getPaymentHistoryResource() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.CancelSubscriptionResponse>> getCancelSubscriptionResponse() {
        return null;
    }
    
    public final void setUserIdForSubscriptions(int newUserId) {
    }
    
    public final void fetchPlans() {
    }
    
    private final void fetchUserSubscriptions(int userId) {
    }
    
    private final void fetchPaymentHistory(int userId) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.CreateSubscriptionResponse>> createSubscription(int userId, int status, @org.jetbrains.annotations.NotNull()
    java.lang.String cardCharged, @org.jetbrains.annotations.NotNull()
    java.lang.String amount) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetPlansResponse>> getPlans() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetUserSubscriptionsResponse>> getUserSubscriptions(int userId) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.CancelSubscriptionResponse>> cancelSubscription(int subscriptionId, @org.jetbrains.annotations.NotNull()
    java.lang.String reason) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetPaymentHistoryResponse>> getPaymentHistory(int userId) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.manaknight.app.network.Resource<com.manaknight.app.model.remote.GetSubscriptionStatusResponse>> getSubscriptionStatus(int subscriptionId) {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u000f\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B5\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0002\u0010\u0007J\u000b\u0010\r\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u000e\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u000f\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u0010\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J9\u0010\u0011\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001J\u0013\u0010\u0012\u001a\u00020\u00132\b\u0010\u0014\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0015\u001a\u00020\u0016H\u00d6\u0001J\t\u0010\u0017\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0005\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u0013\u0010\u0006\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\tR\u0013\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\tR\u0013\u0010\u0004\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\t\u00a8\u0006\u0018"}, d2 = {"Lcom/manaknight/app/viewmodels/BaasViewModel$TeamMemberFilters;", "", "order", "", "size", "filter", "join", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V", "getFilter", "()Ljava/lang/String;", "getJoin", "getOrder", "getSize", "component1", "component2", "component3", "component4", "copy", "equals", "", "other", "hashCode", "", "toString", "app_debug"})
    public static final class TeamMemberFilters {
        @org.jetbrains.annotations.Nullable()
        private final java.lang.String order = null;
        @org.jetbrains.annotations.Nullable()
        private final java.lang.String size = null;
        @org.jetbrains.annotations.Nullable()
        private final java.lang.String filter = null;
        @org.jetbrains.annotations.Nullable()
        private final java.lang.String join = null;
        
        public TeamMemberFilters(@org.jetbrains.annotations.Nullable()
        java.lang.String order, @org.jetbrains.annotations.Nullable()
        java.lang.String size, @org.jetbrains.annotations.Nullable()
        java.lang.String filter, @org.jetbrains.annotations.Nullable()
        java.lang.String join) {
            super();
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String getOrder() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String getSize() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String getFilter() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String getJoin() {
            return null;
        }
        
        public TeamMemberFilters() {
            super();
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String component1() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String component2() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String component3() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String component4() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.manaknight.app.viewmodels.BaasViewModel.TeamMemberFilters copy(@org.jetbrains.annotations.Nullable()
        java.lang.String order, @org.jetbrains.annotations.Nullable()
        java.lang.String size, @org.jetbrains.annotations.Nullable()
        java.lang.String filter, @org.jetbrains.annotations.Nullable()
        java.lang.String join) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
}